# Generated by Django 5.1 on 2024-10-21 10:09

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("system_management", "0016_alter_fileupload_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="SystemEditMode",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("group", models.IntegerField(default=0)),
                ("start", models.DateTimeField(blank=True, null=True)),
                ("end", models.DateTimeField(blank=True, null=True)),
                ("edit_after", models.DateTimeField(blank=True, null=True)),
                (
                    "mode",
                    models.CharField(
                        choices=[("on", "On"), ("interim", "Interim"), ("off", "Off")],
                        max_length=7,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.AlterModelTable(
            name="learningunit",
            table=None,
        ),
    ]
