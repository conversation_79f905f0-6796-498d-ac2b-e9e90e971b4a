# from asgiref.sync import async_to_sync
# from channels.generic.websocket import AsyncWebsocketConsumer
# from django.template.loader import get_template
# import json

# class NotificationConsumer(AsyncWebsocketConsumer):
#     async def connect(self):
#         self.user = self.scope['user']
#         self.GROUP_NAME = 'user-notifications'
        
#         await self.channel_layer.group_add(
#              self.GROUP_NAME, 
#              self.channel_name
#         )
#         await self.accept()
#         print("=======================")

#     async def disconnect(self, close_code):

#         await self.channel_layer.group_discard(
#             self.GROUP_NAME, self.channel_name
#         )

#     async def receive(self, text_data):
#         # This is where you'll receive the form data
#         form_data = json.loads(text_data)
#         form_data.pop("HEADERS")

#         # html = get_template("module_ica/module_ica_partial.html").render(
#         #     context=form_data
#         # )
        
#         # Instead of rendering HTML, prepare a dictionary
#         message = {
#             'type': 'update',
#             'data': form_data,
#             # Add any other relevant data you want to send
#         }

#         await self.channel_layer.group_send(
#             self.GROUP_NAME,
#             {
#                 "type": "send_update",
#                 # "html": html
#                 "message":message
#             }
#         )

#     async def send_update(self, event):
#         # await self.send(text_data=event["html"])
#         await self.send(text_data=json.dumps(event["message"]))
    