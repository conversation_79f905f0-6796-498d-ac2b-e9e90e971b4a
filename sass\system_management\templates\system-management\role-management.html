{% load static %}
<div class="row">
    <div class="col-12">
        <div class="breadcrumb">
            <a href="/">Back</a> <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M7.07586 4.51043C7.18836 4.39807 7.34086 4.33496 7.49986 4.33496C7.65886 4.33496 7.81136 4.39807 7.92386 4.51043L11.3239 7.91043C11.4362 8.02293 11.4993 8.17543 11.4993 8.33443C11.4993 8.49343 11.4362 8.64593 11.3239 8.75843L7.92386 12.1584C7.81012 12.2644 7.65969 12.3221 7.50424 12.3194C7.3488 12.3166 7.2005 12.2537 7.09057 12.1437C6.98064 12.0338 6.91766 11.8855 6.91492 11.7301C6.91218 11.5746 6.96988 11.4242 7.07586 11.3104L10.0519 8.33443L7.07586 5.35843C6.9635 5.24593 6.90039 5.09343 6.90039 4.93443C6.90039 4.77543 6.9635 4.62293 7.07586 4.51043Z" fill="#BBBBBB"/>
                </svg>
            <span>Role Management</span>
        </div>  
        <div class="content-system-management">
            <div class="card card-shadow card-role-management card-padding">
                <h2 class="title-section">System Management</h2>
                <c-menu_system_management current_page="roleManagement"></c-menu_system_management>
                <h2 class="title-card text-black mb-4">Role Management</h2>
                
                <div class="content-role-management">
                    <div class="d-flex flex-column flex-md-row align-items-md-center gap-4 justify-content-start">   
                        <form action="{% url 'system_management:export_role_management_table' %}" method="POST">
                            {% csrf_token %}
                            <button  type="submit" class="btn-text v-center p-0 edit-files"><img class="icon-image" src="{% static 'img/icon-export.svg' %}" alt="export"> Export List</button>
                        </form>
                        {% if permission.update or permission.type == 'admin' %}
                        <button hx-get="{% url 'system_management:roleManagement' %}" hx-target=".container-content" hx-vals='{"form_type":"edit"}' hx-swap="innerHTML" type="button" class="btn-text v-center p-0 edit-role-management {% if form_type == 'edit' %}disabled{% endif %}">
                            <img class="icon-image" src="{% static 'img/icon-pencil.svg' %}" alt="Edit"> 
                            Edit
                        </button>
                        {% endif %}
                    </div>
                    <form id="table-permission"  hx-post="{% url 'system_management:update_role_management'  %}" hx-trigger="submit" hx-swap="innerHTML" hx-target=".container-content">
                        {% csrf_token %}
                    <div class="table-responsive table-role-management">
                        <table class="table table-sass table-borderless {% if form_type == 'edit' %}table-edit-role-management{% endif %}">
                            <thead class="bg-gray text-black">
                                <tr>
                                    <th>MODULE</th>
                                    <th colspan="4" class="text-center">ADMIN</th>
                                    <th colspan="4" class="text-center">USER</th>                        
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Access</td>
                                    <td>Create</td>
                                    <td>Update</td>
                                    <td>Delete</td>
                                    <td>View</td>                        
                                    <td>Create</td>
                                    <td>Update</td>
                                    <td>Delete</td>
                                    <td>View</td>
                                </tr>
            
                                    {% for permission in permissions%}
                                    <tr>
                                        <td>
                                            {{permission.page_display}}
                                            <input hidden name="permission-page" value="{{permission.page}}"> </input>
                                        </td>
                                        
                                        {% comment %} ADMIN {% endcomment %}
                                        <td>
                                            <label class="wrapper-checkbox {% if permission.page == 'report' or permission.page == 'live_schedule_status' %} disabled {% endif %}">
                                                <input name="{{permission.page}}-admin-create" type="checkbox" title="checked" {% if form_type != 'edit' %}onclick="return false;"{% endif %} {% if permission.admin_create %} value="true" checked="checked" {% endif %}>
                                                <span class="checkmark {% if permission.page == 'report' or permission.page == 'live_schedule_status' %} disabled {% endif %}"></span>
                                            </label>
                                        </td>
                                        <td>
                                            <label class="wrapper-checkbox {% if permission.page == 'report'  %} disabled {% endif %}">
                                                <input name="{{permission.page}}-admin-update" type="checkbox" title="checked" {% if form_type != 'edit' %}onclick="return false;"{% endif %} {% if permission.admin_update %} value="true" checked="checked" {% endif %}>
                                                <span class="checkmark {% if permission.page == 'report' %} disabled {% endif %}"></span>
                                            </label>
                                        </td>
                                        <td>
                                            <label class="wrapper-checkbox {% if permission.page == 'report' or permission.page == 'live_schedule_status' or permission.page == 'system_management' %} disabled {% endif %}">
                                                <input name="{{permission.page}}-admin-delete" type="checkbox" title="checked" {% if form_type != 'edit' %}onclick="return false;"{% endif %} {% if permission.admin_delete %} value="true" checked="checked" {% endif %}>
                                                <span class="checkmark {% if permission.page == 'report' or permission.page == 'live_schedule_status' or permission.page == 'system_management' %} disabled {% endif %}"></span>
                                            </label>
                                        </td>
                                        <td>
                                            <label class="wrapper-checkbox">
                                                <input name="{{permission.page}}-admin-view" type="checkbox" title="checked" {% if form_type != 'edit' %}onclick="return false;"{% endif %} {% if permission.admin_view %} value="true" checked="checked" {% endif %}>
                                                <span class="checkmark"></span>
                                            </label>
                                        </td>  
                                        
                                        {% comment %} USER {% endcomment %}
                                        <td><label class="wrapper-checkbox {% if permission.page == 'report' or permission.page == 'live_schedule_status' %} disabled {% endif %}">
                                            <input name="{{permission.page}}-user-create" type="checkbox" title="checked" {% if form_type != 'edit' %}onclick="return false;"{% endif %} {% if permission.user_create %} value="true" checked="checked" {% endif %}>
                                            <span class="checkmark {% if permission.page == 'report' or permission.page == 'live_schedule_status' %} disabled {% endif %}"></span>
                                        </label></td>
                                        <td><label class="wrapper-checkbox {% if permission.page == 'report'  %} disabled {% endif %}">
                                            <input name="{{permission.page}}-user-update" type="checkbox" title="checked" {% if form_type != 'edit' %}onclick="return false;"{% endif %} {% if permission.user_update %} value="true" checked="checked" {% endif %}>
                                            <span class="checkmark {% if permission.page == 'report' %} disabled {% endif %}"></span>
                                        </label></td>
                                        <td>
                                            <label class="wrapper-checkbox {% if permission.page == 'report' or permission.page == 'live_schedule_status' or permission.page == 'system_management'  %} disabled {% endif %}">
                                                <input name="{{permission.page}}-user-delete" type="checkbox" title="checked" {% if form_type != 'edit' %}onclick="return false;"{% endif %} {% if permission.user_delete %} value="true" checked="checked" {% endif %}>
                                                <span class="checkmark {% if permission.page == 'report' or permission.page == 'live_schedule_status' or permission.page == 'system_management'  %} disabled {% endif %}"></span>
                                            </label>
                                        </td>
                                        <td>
                                            <label class="wrapper-checkbox">
                                                <input name="{{permission.page}}-user-view" type="checkbox" title="checked" {% if form_type != 'edit' %}onclick="return false;"{% endif %} {% if permission.user_view %} value="true" checked="checked" {% endif %}>
                                                <span class="checkmark"></span>
                                            </label>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </form>
            
            
                            </tbody>
                        </table>
                    </div>        
                </div>
            </div>
            {% if form_type == 'edit' %}
                        <div class="mt-4">
                            <div class="d-flex justify-content-start align-items-center gap-2">
                                <button class="btn btn-primary" type="submit" form="table-permission" >Save changes</button>
                                <button hx-get="{% url 'system_management:roleManagement' %}" hx-vals='{"form_type":""}' hx-target=".container-content" hx-swap="innerHTML" class="btn-text text-danger" type="button">Cancel</button>
                            </div>
                        </div>
                        {% endif %}
        </div>  
    </div>
</div>








