# Generated by Django 5.1 on 2024-11-05 04:46

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("system_management", "0037_department"),
    ]

    def migrate_department_data(apps, schema_editor):
        LearningUnit = apps.get_model('system_management', 'LearningUnit')
        Department = apps.get_model('system_management', 'Department')
        
        # First create all departments
        department_codes = LearningUnit.objects.exclude(department__isnull=True).values_list('department', flat=True).distinct()
        
        for code in department_codes:
            if code:
                Department.objects.get_or_create(
                    code=code,
                    defaults={
                        "name": code
                    }
                )
        
        # Then update all learning units
        for unit in LearningUnit.objects.all():
            if unit.department:
                dept = Department.objects.filter(code=unit.department).first()
                if dept:
                    unit.departments = dept
                    unit.save()

    operations = [
        migrations.AddField(
            model_name="learningunit",
            name="departments",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="system_management.department",
            ),
        ),
        migrations.RunPython(migrate_department_data)
    ]
