{% load static %}
{% load custom_tags %}
<div class="row">
    <div class="col-12">

        <div class="breadcrumb">
            <a href="/">Back</a> <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M7.07586 4.51043C7.18836 4.39807 7.34086 4.33496 7.49986 4.33496C7.65886 4.33496 7.81136 4.39807 7.92386 4.51043L11.3239 7.91043C11.4362 8.02293 11.4993 8.17543 11.4993 8.33443C11.4993 8.49343 11.4362 8.64593 11.3239 8.75843L7.92386 12.1584C7.81012 12.2644 7.65969 12.3221 7.50424 12.3194C7.3488 12.3166 7.2005 12.2537 7.09057 12.1437C6.98064 12.0338 6.91766 11.8855 6.91492 11.7301C6.91218 11.5746 6.96988 11.4242 7.07586 11.3104L10.0519 8.33443L7.07586 5.35843C6.9635 5.24593 6.90039 5.09343 6.90039 4.93443C6.90039 4.77543 6.9635 4.62293 7.07586 4.51043Z" fill="#BBBBBB"/>
                </svg>
            <span>Upload File</span>
        </div>  

        <div class="content-system-management">
            
            <div class="card card-shadow card-padding">
                <h2 class="title-section">System Management</h2>
                <c-menu_system_management current_page="uploadFile"></c-menu_system_management>
                
                <h2 class="title-card text-black mb-4">Upload File</h2>
                
                <div class="content-upload-files">
                    {% if permission.update %} 
                    <div class="d-flex flex-column flex-md-row align-items-md-center gap-4 justify-content-start menu-user-management">   
                        <button type="button" class="btn-text v-center p-0 edit-files" hx-get="{% url 'system_management:formUpload' %}" hx-target=".content-upload-files" hx-swap="innerHTML" hx-trigger="click"><img class="icon-image" src="{% static 'img/icon-pencil.svg' %}" alt="bulk"> Edit files</button>                        
                    </div>
                    <div class="mt-4">
                        <div class="alert alert-warning">
                            Please upload LU List, Course LU Map, and CourseTypeLUTypeCalendar together. Failure to do so will result in data discrepancies in the master data
                        </div>
                    </div>
                    {% endif %} 
                    <div class="mt-4 row">
                        <div class="col-lg-10">
                            <div class="form-group mb-4">
                                <label>Moderation method</label>
                                {% with file_obj="moderation_method"|get_file_object %}
                                
                                    {% if file_obj %}
                                        <p class="text-blue mb-0">
                                            <a href="{% url 'server_media' file_obj.file %}" download class="download-link">{{ file_obj.name }}</a>
                                        </p>
                                        <p class="fst-italic text-gray">Last edited by {{file_obj.uploaded_by.first_name}} {{file_obj.created_at|date:"d-M-Y H:i"}}</p>
                                    {% else %} 
                                        <p class="fst-italic"> - No file selected - </p> 
                                    {% endif %}
                                
                                {% endwith %}       
                            </div>
                            <div class="form-group mb-4">
                                <label>Assessment</label>
                                {% with file_obj="assessment"|get_file_object %}
                                    {% if file_obj %}
                                        <p class="text-blue mb-0">
                                            <a href="{% url 'server_media' file_obj.file %}" download class="download-link">{{ file_obj.name }}</a>
                                        </p>
                                        <p class="fst-italic text-gray">Last edited by {{file_obj.uploaded_by.first_name}} {{file_obj.created_at|date:"d-M-Y H:i"}}</p>
                                    {% else %} 
                                        <p class="fst-italic"> - No file selected - </p> 
                                    {% endif %}
                                {% endwith %}  
                            </div>
                            <div class="form-group mb-4">
                                <label>Course</label>
                                {% with file_obj="course"|get_file_object %}
                                    {% if file_obj %}
                                        <p class="text-blue mb-0">
                                            <a href="{% url 'server_media' file_obj.file %}" download class="download-link">{{ file_obj.name }}</a>
                                        </p>
                                        <p class="fst-italic text-gray">Last edited by {{file_obj.uploaded_by.first_name}} {{file_obj.created_at|date:"d-M-Y H:i"}}</p>
                                    {% else %} 
                                        <p class="fst-italic"> - No file selected - </p> 
                                    {% endif %}
                                {% endwith %}           
                            </div>
                            <div class="form-group mb-4">
                                <label>Department</label>
                                {% with file_obj="department"|get_file_object %}
                                    {% if file_obj %}
                                        <p class="text-blue mb-0">
                                            <a href="{% url 'server_media' file_obj.file %}" download class="download-link">{{ file_obj.name }}</a>
                                        </p>
                                        <p class="fst-italic text-gray">Last edited by {{file_obj.uploaded_by.first_name}} {{file_obj.created_at|date:"d-M-Y H:i"}}</p>
                                    {% else %} 
                                        <p class="fst-italic"> - No file selected - </p> 
                                    {% endif %}
                                {% endwith %}           
                            </div>
                            <div class="form-group mb-4">
                                <label>LUList</label>
                                {% with file_obj="learning_unit"|get_file_object %}
                                    {% if file_obj %}
                                        <p class="text-blue mb-0">
                                            <a href="{% url 'server_media' file_obj.file %}" download class="download-link">{{ file_obj.name }}</a>
                                        </p>
                                        <p class="fst-italic text-gray">Last edited by {{file_obj.uploaded_by.first_name}} {{file_obj.created_at|date:"d-M-Y H:i"}}</p>
                                    {% else %} 
                                        <p class="fst-italic"> - No file selected - </p> 
                                    {% endif %}
                                {% endwith %}                            
                            </div>
                            <div class="form-group mb-4">
                                <label>CourseLUMap</label>
                                {% with file_obj="course_lu_map"|get_file_object %}
                                    {% if file_obj %}
                                        <p class="text-blue mb-0">
                                            <a href="{% url 'server_media' file_obj.file %}" download class="download-link">{{ file_obj.name }}</a>
                                        </p>
                                        <p class="fst-italic text-gray">Last edited by {{file_obj.uploaded_by.first_name}} {{file_obj.created_at|date:"d-M-Y H:i"}}</p>
                                    {% else %} 
                                        <p class="fst-italic"> - No file selected - </p> 
                                    {% endif %}
                                {% endwith %}                               
                            </div>
                            <div class="form-group mb-0">
                                <label>CourseTypeLUTypeCalendar</label>
                                {% with file_obj="academic_week"|get_file_object %}
                                    {% if file_obj %}
                                        <p class="text-blue mb-0">
                                            <a href="{% url 'server_media' file_obj.file %}" download class="download-link">{{ file_obj.name }}</a>
                                        </p>
                                        <p class="fst-italic text-gray">Last edited by {{file_obj.uploaded_by.first_name}} {{file_obj.created_at|date:"d-M-Y H:i"}}</p>
                                    {% else %} 
                                        <p class="fst-italic"> - No file selected - </p> 
                                    {% endif %}
                                {% endwith %}                            
                            </div>
                            
                        </div>
                    </div>
                </div>
            </div>
                        
            <div class="d-flex mt-4 gap-2 align-items-center justify-content-start d-none button-save-files">
                <button hx-encoding="multipart/form-data" hx-include="#file-upload-form" hx-post="{% url 'system_management:upload_file' %}" hx-trigger="click" hx-swap="innerHTML" hx-target=".validation-content" type="button" name="validation"  class="btn btn-primary show-popup btn-save-upload disabled" >Save Changes 
                    <img src="{% static 'img/loading.gif' %}" alt="loading" width="30" class="loading-upload d-none">
                </button>
                <c-popup class="popup-wide">
                    <div class="validation-content">

                    </div>
                    <div class="d-flex align-items-center gap-3 justify-content-center mt-4">
                        <button form="file-upload-form" type="submit" class="btn btn-primary close-popup btn-proceed-upload">Proceed</button>
                        <button type="button" class="btn-text text-danger close-popup">Cancel</button>
                    </div>
                </c-popup>
                <button type="button" class="btn-text text-danger" hx-get="{% url 'system_management:form_file_view' %}" hx-swap="innerHTML" hx-target=".container-content" hx-trigger="click">Cancel</button>
            </div>

            {% if success_files or error_files %}
                <div class="popup popup-wide">
                    <div class="backdrop"></div>
                    <div class="content-popup content-popup-upload">          
                        {% if error_files %}
                            <h2>Almost There!</h2>                             
                            <p>Some files were uploaded successfully, but a few encountered errors.</p>
                    
                            <p class="mb-0">Failed to upload</p>
                            <ul>
                                {% for error_file in error_files %}
                                    <li class="text-danger">[{{ error_file }}]</li>                                
                                {% endfor %}
                            </ul>                            
                            <p>Please re-upload the failed files.</p>
                        {% endif %}

                        {% if success_files %}
                            <h2>Success!</h2>                             
                            <img src="{% static 'img/sassy-happy.png' %}" alt="sassy happy">
            
                            <p class="mb-0">File upload successful.</p>
                            <ul>
                                {% for success_file in success_files %}
                                    <li>[{{ success_file }}]</li>                                
                                {% endfor %}
                            </ul>                            
                        {% endif %}
                        
                        <div class="mt-4">               
                            <div class="d-flex align-items-center gap-3 justify-content-center">
                                {% if success_files %}
                                    <button type="button" class="btn btn-primary close-popup">Okay! </button>  
                                {% endif %}

                                {% if error_files %}
                                <button class="btn btn-primary close-popup btn-reupload" hx-get="{% url 'system_management:formUpload' %}" hx-target=".content-upload-files" hx-swap="innerHTML" hx-trigger="click">Reupload</button>
                                <button type="button" class="btn-text text-danger close-popup">Cancel </button>                                    
                                {% endif %}

                            </div>    
                        </div> 
                    </div>
                </div> 
            {% endif %}
        </div>      
    </div>

</div>

<script>
console.log('Script is executing, document.readyState:', document.readyState);

function initializeDownloadLinks() {
    console.log('Initializing download links...');

    // Handle download link clicks
    const downloadLinks = document.querySelectorAll('.download-link');
    console.log('Found', downloadLinks.length, 'download links');

    downloadLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const url = this.href;
            const filename = this.textContent;

            console.log('Checking file status for:', filename);

            // Use fetch to check file status first
            fetch(url, {
                method: 'HEAD'
            })
            .then(response => {
                console.log('Response status:', response.status);

                if (response.status === 409) {
                    alert('File is not ready yet. Please try again later.');
                } else if (response.status === 404) {
                    alert('File not found.');
                } else if (response.status === 500) {
                    alert('Server error. Please try again later.');
                } else if (response.ok) {
                    // File is ready, proceed with download
                    console.log('File ready, starting download...');
                    window.location.href = url;
                } else {
                    alert('Unable to download file. Please try again later.');
                }
            })
            .catch(error => {
                console.error('Download error:', error);
                alert('Network error. Please check your connection and try again.');
            });
        });
    });
}

// Check if DOM is already loaded
if (document.readyState === 'loading') {
    console.log('DOM is still loading, adding DOMContentLoaded listener');
    document.addEventListener('DOMContentLoaded', initializeDownloadLinks);
} else {
    console.log('DOM already loaded, initializing immediately');
    initializeDownloadLinks();
}
</script>






