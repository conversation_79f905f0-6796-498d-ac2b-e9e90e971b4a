
from django.shortcuts import render, redirect
from .models import *
from .rule import *
from utility import *
from django.http import HttpResponse
from django.urls import reverse
from django.db.models import Case, When, IntegerField
import ast

# Create your views here.
SCHEDULE_ASSESSMENT_MODEL = {
    "schedule-assessment": {
        "normal": ScheduleAssessment,
        "test": ScheduleAssessmentTest,
    },
    "status-learning-unit": {
        "normal": StatusLearningUnit,
        "test": StatusLearningUnitTest,
    }

}


def add_ica(request, id):
    if request.method == "POST":
        SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()

        test_mode = request.POST.get('test_mode', None)
        if test_mode == "None":
            test_mode = None

        weeks_of_max_ica = request.POST.get('weeks_of_max_ica', None)
        if weeks_of_max_ica == "undefined" :weeks_of_max_ica=None

        ica_number = request.POST.get('ica_number', None)
        learning_unit = LearningUnit.objects.get(id=id)

        # Create assessement schedule Temporary
        schedule_assessment = SCHEDULE_ASSESSMENT_MODEL['schedule-assessment']['test' if test_mode else 'normal'].objects.create(
            ica_number=ica_number,
            current_year=SYSTEM_CURRENT_YEAR.current_year,
            current_semester=SYSTEM_CURRENT_YEAR.current_semester,
            lu=learning_unit,
            updated_by=request.user,
            owned_by=request.user)

        context = {
            "learning_unit": learning_unit,
            "assessements": Assessment.objects.all(),
            "test_mode": test_mode,
            "schedule_assessment": schedule_assessment,
            "weeks_of_max_ica":weeks_of_max_ica
        }
        return render(request, 'schedule-assessment/item-ica.html', context)

    return HttpResponse(200)


# def get_assm_task(request):
#     SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()

#     schedule_assessment_id = request.GET.get("schedule_assessment", None)
#     assm_method = request.GET.get("assm-method", None)
#     week = request.GET.get("week", None)
#     mode = request.GET.get("mode", None)

#     schedule_assessment = ScheduleAssessment.objects.filter(
#         id=schedule_assessment_id).first()
#     learning_unit = schedule_assessment.lu
#     assessment_method = Assessment.objects.filter(slug=assm_method).first()
#     schedule_assessment.assessment_method = assessment_method
#     schedule_assessment.save()

#     test_fa_week = RuleDefinition.objects.filter(slug='test_fa_week').first().value
#     TESTFAWEEK = True if week == test_fa_week else False
#     print(week," ",TESTFAWEEK )

#     filter_conditions = Q(lu=schedule_assessment.lu, current_year=SYSTEM_CURRENT_YEAR.current_year,
#                           current_semester=SYSTEM_CURRENT_YEAR.current_semester)

#     if 'test' in assessment_method.slug:
#         filter_conditions &= Q(assessment_method__slug__icontains='test')

#     else:
#         filter_conditions &= Q(assessment_method=assessment_method)

#     schedule_assessment_assm_methods = ScheduleAssessment.objects.filter(
#         filter_conditions).order_by('week')

#     # print("schedule_assessment_assm_methods: ",schedule_assessment_assm_methods)
#     if schedule_assessment_assm_methods:
#         for task_number, schedule_assessment_assm_method in enumerate(schedule_assessment_assm_methods):

#             # Assign Assm Task
#             predefined_assm_task = None
#             assm_task = assessment_method.task
#             if assm_task:
#                 assm_task_categories = assm_task.split(",")
#                 for assm_task_category in assm_task_categories:
#                     if 'TESTFA' in assm_task_category and TESTFAWEEK and learning_unit.is_examinable == 'yes' and (assessment_method.slug == "test-on-campus-proctored-eassm-penpaper" or assessment_method.slug == "test-on-campus-proctored-eassm-keyboard"):
#                         predefined_assm_task = 'TESTFA'
#                     else:
#                         if 'TESTFA' in assm_task_category:
#                             continue

#                         assm_task_components = assm_task_category.split("-")
#                         assm_task_component = separate_string_and_number(assm_task_components[0])[0]

#                         predefined_assm_task = f"{
#                             assm_task_component} {task_number+1}"

#                     schedule_assessment_assm_method.assessment_task = predefined_assm_task
#                     schedule_assessment_assm_method.save()

#     schedule_assessment = ScheduleAssessment.objects.filter(id=schedule_assessment_id).first()
#     print("============ schedule_assessment: ",schedule_assessment)
#     context = {
#         "mode": mode,
#         "learning_unit": learning_unit,
#         "schedule_assessment": schedule_assessment,
#     }

#     return render(request, 'schedule-assessment/ica-schedule-assm-task.html', context)


@check_access(page='schedule_assessement')
def ica_schedule(request, id):
    test_mode = request.GET.get("test_mode", None)
    is_from_submit = request.GET.get("is_from_submit", None)
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
    learning_unit = LearningUnit.objects.get(id=id)
    status_learning_unit = SCHEDULE_ASSESSMENT_MODEL['status-learning-unit']['test' if test_mode else 'normal'].objects.filter(
        current_year=SYSTEM_CURRENT_YEAR.current_year,
        current_semester=SYSTEM_CURRENT_YEAR.current_semester,
        lu=learning_unit
    ).first()

    year_studies = CourseLuMap.objects.filter(academic_year=SYSTEM_CURRENT_YEAR.current_year,
                                              semester=SYSTEM_CURRENT_YEAR.current_semester,
                                              lu=learning_unit).values_list('year_study', flat=True).distinct().order_by('year_study')
    is_edit, is_edit_ica_detail = checking_open_or_close_ica(learning_unit)
    context = {
        "credits": int(learning_unit.credit) if learning_unit.credit else 0,
        "learning_unit": learning_unit,
        "is_edit" : is_edit,
        "system_current_year": SYSTEM_CURRENT_YEAR,
        "status_learning_unit": status_learning_unit,
        "year_studies": year_studies,
        "redirect_to": request.GET.get("redirect_to", None),
        "save_detail": request.GET.get("save_detail", None),
        "test_mode": test_mode,
        "is_from_submit": is_from_submit,
        "permission": check_permission(request.user, 'schedule_assessement')
    }
    return render(request, 'schedule-assessment/ica-schedule.html', context)


def reserved_assessement(request):
    if request.method == "POST":

        test_mode = request.POST.get('test_mode', None)
        if test_mode == "None":
            test_mode = None

        rule = BusinessRules.objects.filter(slug='rule').first()
        if rule:
            if test_mode:
                rule.stage1 = ast.literal_eval(rule.stage1_test)
            else:
                rule.stage1 = ast.literal_eval(rule.stage1)

        weeks = request.POST.getlist('week', None)
        learning_unit_id = request.POST.get('learning_unit_id', None)
        schedule_assessment_id = request.POST.getlist(
            'schedule_assessment', None)

        learning_unit = LearningUnit.objects.get(id=learning_unit_id)

        request_scheduled_week = {}
        for idx, schedule_assessment__id in enumerate(schedule_assessment_id):
            request_scheduled_week[str(schedule_assessment__id)] = weeks[idx]

        for schedule_assessment__id in schedule_assessment_id:

            schedule_assessment = SCHEDULE_ASSESSMENT_MODEL['schedule-assessment']['test' if test_mode else 'normal'].objects.get(
                id=schedule_assessment__id)

            try:
                validation_result = {}
                if rule:
                    if rule.stage1:
                        condition_rule = rule.stage1[0]
                        if 'conditions' in condition_rule:
                            condition_flag = False
                            if 'all' in condition_rule['conditions']:
                                if condition_rule['conditions']['all']:
                                    condition_flag = True
                            elif 'any' in condition_rule['conditions']:
                                if condition_rule['conditions']['any']:
                                    condition_flag = True

                            if condition_flag and not rule.status_admin_break_rule_stage1:
                                validation_result = validate_with_tracker(schedule_assessment, rule.stage1, {
                                                                          'weeks': request_scheduled_week}, test_mode)
                            else:
                                validation_result['triggered_rules'] = [
                                    "mark_valid"]

                # print("validation_result: ", validation_result)
                if validation_result:
                    if validation_result['triggered_rules']:
                        schedule_assessment.week = request_scheduled_week[str(
                            schedule_assessment__id)]
                        schedule_assessment.updated_by = request.user
                        if not schedule_assessment.owned_by:
                            schedule_assessment.owned_by = request.user
                        schedule_assessment.save()

                        schedule_assessment.broken_rules = None
                        schedule_assessment.broken_rules_stage1 = None
                        schedule_assessment.save()
                    else:
                        schedule_assessment.week = None
                        schedule_assessment.broken_rules = validation_result['broken_rules'][0]
                        schedule_assessment.broken_rules_stage1 = validation_result['broken_rules'][0]
                        schedule_assessment.save()

            except Exception as e:
                schedule_assessment.week = None
                schedule_assessment.broken_rules = {
                    'failed_conditions': [f"Error: {e}"]}
                schedule_assessment.broken_rules_stage1 = {
                    'failed_conditions': [f"Error: {e}"]}
                schedule_assessment.save()

        assign_status(learning_unit=learning_unit,
                      user=request.user, ica_detail_submit=False, test_mode=test_mode)

    base_url = reverse('schedule_assessment:icaSchedule', kwargs={
                       'id': request.POST.get("learning_unit_id", None),
                       })
    if test_mode:
        base_url += "?test_mode=true&is_from_submit=true"
    else:
        base_url += "?is_from_submit=true"
    return redirect(f'{base_url}')


def preview_ica_schedule(request):
    id = request.GET.get('learning_unit', None)
    is_from_submit = request.GET.get("is_from_submit", None)
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
    learning_unit = LearningUnit.objects.get(id=id)

    status_learning_unit = StatusLearningUnit.objects.filter(
        current_year=SYSTEM_CURRENT_YEAR.current_year,
        current_semester=SYSTEM_CURRENT_YEAR.current_semester,
        lu=learning_unit
    ).first()
    is_edit, is_edit_ica_detail = checking_open_or_close_ica(learning_unit)
    context = {
        "credits": int(learning_unit.credit) if learning_unit.credit else 0,
        "learning_unit": learning_unit,
        "is_edit" : is_edit,
        "is_edit_ica_detail": is_edit_ica_detail,
        "system_current_year": SYSTEM_CURRENT_YEAR,
        "status_learning_unit": status_learning_unit,
        "test_mode": True,
        "is_from_submit": is_from_submit
    }
    return render(request, 'schedule-assessment/ica-schedule.html', context)


def copy_from_last(request, id):
    is_status_learning_unit = False
    if request.method == 'POST':
        SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()

        learning_unit = LearningUnit.objects.get(id=id)
        test_mode = request.POST.get('test_mode', None)

        # == Start Validasi
        if 'validation' in request.POST:
            current_year = SYSTEM_CURRENT_YEAR.current_year
            current_semester = SYSTEM_CURRENT_YEAR.current_semester

            previous_years = []
            previous_semesters = []
            # First previous semester
            if current_semester == 1:
                previous_years.append(current_year - 1)
                previous_semesters.append(2)
            else:  # current_semester == 2
                previous_years.append(current_year)
                previous_semesters.append(1)

            # Second previous semester
            if current_semester == 1:
                previous_years.append(current_year - 1)
                previous_semesters.append(1)
            else:  # current_semester == 2
                previous_years.append(current_year - 1)
                previous_semesters.append(2)

            status_learning_unit = None
            for checking_year, checking_semester in zip(previous_years, previous_semesters):
                status_learning_unit = StatusLearningUnit.objects.filter(
                    lu=learning_unit,
                    current_year=checking_year,
                    current_semester=checking_semester,
                    status='approved'
                )
                if status_learning_unit:
                    break

            context = {'learning_unit': learning_unit,
                       'status_learning_unit': status_learning_unit,
                       'checking_year': checking_year,
                       'checking_semester': checking_semester}
            return render(request, "schedule-assessment/copy-from-last-validation-popup.html", context)

            # == End Validasi
        else:
            # == Start eksecution

            checking_year = request.POST.get('checking_year', None)
            checking_semester = request.POST.get('checking_semester', None)

            new_assessments = []
            if checking_year and checking_semester:
                new_assessments = SCHEDULE_ASSESSMENT_MODEL['schedule-assessment']['test' if test_mode else 'normal'].objects.filter(
                    current_year=checking_year,
                    current_semester=checking_semester,
                    lu=learning_unit).order_by('created_at')

                if new_assessments:
                    current_assessments = SCHEDULE_ASSESSMENT_MODEL['schedule-assessment']['test' if test_mode else 'normal'].objects.filter(
                        current_year=SYSTEM_CURRENT_YEAR.current_year,
                        current_semester=SYSTEM_CURRENT_YEAR.current_semester,
                        lu=learning_unit).order_by('created_at')
                    current_assessments.delete()

                for new_assm in new_assessments:
                    new_assm.pk = None
                    new_assm.id = None
                    new_assm.current_year = SYSTEM_CURRENT_YEAR.current_year
                    new_assm.current_semester = SYSTEM_CURRENT_YEAR.current_semester
                    new_assm.ica_number = new_assm.ica_number
                    new_assm.weightage = new_assm.weightage
                    new_assm.over_two_weeks = new_assm.over_two_weeks
                    new_assm.assessment_method = new_assm.assessment_method
                    new_assm.assessment_task = new_assm.assessment_task
                    new_assm.duration = new_assm.duration
                    new_assm.moderation_method = new_assm.moderation_method
                    new_assm.assessment_type = new_assm.assessment_type
                    new_assm.digital_submission = new_assm.digital_submission
                    new_assm.descriptions = new_assm.descriptions
                    new_assm.topics = new_assm.topics
                    new_assm.note = new_assm.note
                    new_assm.reasons_change = new_assm.reasons_change
                    new_assm.updated_by = new_assm.updated_by
                    if not new_assm.owned_by:
                        new_assm.owned_by = new_assm.updated_by
                    new_assm.broken_rules = None
                    new_assm.broken_rules_stage1 = None
                    new_assm.broken_rules_stage2 = None
                    new_assm.week = None
                    new_assm.lu = learning_unit
                    new_assm.created_at = timezone.now()
                    new_assm.updated_at = timezone.now()
                    new_assm.save()

            is_edit, is_edit_ica_detail = checking_open_or_close_ica(
                learning_unit)
            context = {
                "learning_unit": learning_unit,
                "is_edit": is_edit,
                "is_edit_ica_detail": is_edit_ica_detail,
                "is_status_learning_unit": is_status_learning_unit,
                "datenow": datetime.now(),
                "credits": int(learning_unit.credit) if learning_unit.credit else 0,
                "schedule_assessments": new_assessments,
                "assessements": Assessment.objects.all(),
                "permission": check_permission(request.user, 'schedule_assessement')
            }
            return render(request, "schedule-assessment/ica-schedule-partial.html", context)

    return HttpResponse(200)


def partial_ica_schedule(request, luID, is_delete=None):

    test_mode = request.GET.get('test_mode')
    is_from_submit = request.GET.get("is_from_submit", None)
    learning_unit = LearningUnit.objects.get(id=luID)
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()

    schedule_assessments = SCHEDULE_ASSESSMENT_MODEL['schedule-assessment']['test' if test_mode else 'normal'].objects.filter(
        current_year=SYSTEM_CURRENT_YEAR.current_year,
        current_semester=SYSTEM_CURRENT_YEAR.current_semester,
        lu=learning_unit).order_by('week')
    
    
    countErrorStage1 = sum(len(schedule_assessment.broken_rules_stage1) if schedule_assessment.broken_rules_stage1 else 0 for schedule_assessment in schedule_assessments)
    
    
    is_edit, is_edit_ica_detail = checking_open_or_close_ica(learning_unit)
    context = {
        "learning_unit": learning_unit,
        "is_edit": is_edit,
        "count_error_stage1":countErrorStage1,        
        "credits": int(learning_unit.credit) if learning_unit.credit else 0,
        "schedule_assessments": schedule_assessments,
        'test_mode': test_mode,
        "assessements": Assessment.objects.all(),
        "is_from_submit": is_from_submit,
        "permission": check_permission(request.user, 'schedule_assessement')
    }
    return render(request, "schedule-assessment/ica-schedule-partial.html", context)


def partial_ica_detail(request, id):
    if request.method == "GET":
        test_mode = request.GET.get('test_mode')
        learning_unit = LearningUnit.objects.get(id=id)
        SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()

        schedule_assessments = SCHEDULE_ASSESSMENT_MODEL['schedule-assessment']['test' if test_mode else 'normal'].objects.filter(
            current_year=SYSTEM_CURRENT_YEAR.current_year,
            current_semester=SYSTEM_CURRENT_YEAR.current_semester,
            lu=learning_unit).order_by('week')

        moderation_methods = ModerationMethod.objects.all()

        is_edit, is_edit_ica_detail = checking_open_or_close_ica(learning_unit)
        context = {
            "learning_unit": learning_unit,
            "is_edit": is_edit,
            "is_edit_ica_detail": is_edit_ica_detail,
            "schedule_assessments": schedule_assessments,
            "moderation_methods": moderation_methods,
            "test_mode": test_mode,
            "permission": check_permission(request.user, 'schedule_assessement')
        }
        return render(request, "schedule-assessment/ica-details-partial.html", context)

    return HttpResponse(200)


def delete_ica(request, id, luID=None):
    test_mode = request.POST.get('test_mode')
   
    # partial_ica_schedule(request, luID, is_delete=is_delete)
    schedule_assessment = SCHEDULE_ASSESSMENT_MODEL['schedule-assessment']['test' if test_mode else 'normal'].objects.filter(
        id=id).first()

    assign_status(learning_unit=schedule_assessment.lu,
                  user=request.user, ica_detail_submit=False, test_mode=test_mode)

    schedule_assessment.delete()

    return HttpResponse(200)


def update_ica_schedule(request):
    test_mode = request.POST.get('test_mode')
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
    learning_unit_id = request.POST.get("learning_unit_id", None)
    learning_unit = LearningUnit.objects.filter(id=learning_unit_id).first()
    records = [
        {
            'id': schedule_assessment_id,
            'weightage': weightage if weightage else 0,
            'over_two_weeks': over_two_weeks,
            'assessment_method': Assessment.objects.filter(slug=assessment_method).first(),
            'week': week,
        }
        for schedule_assessment_id, weightage, over_two_weeks, assessment_method, week in zip(
            request.POST.getlist('schedule_assessment', None),
            request.POST.getlist('weightage', None),
            request.POST.getlist('assm-2-weeks', None),
            request.POST.getlist('assm-method', None),
            request.POST.getlist('week', None),
        )
    ]

    assessment_week_dict = {}
    weightage = 0
    for record in records:

        schedule_assessment = SCHEDULE_ASSESSMENT_MODEL['schedule-assessment']['test' if test_mode else 'normal'].objects.filter(id=record['id']).first()
        
        if schedule_assessment:
            for default_property_key in record:
                if 'week' == default_property_key:
                    continue  # exclude week
                setattr(schedule_assessment, default_property_key,
                        record[default_property_key])

            schedule_assessment.updated_by = request.user
            if not schedule_assessment.owned_by:
                schedule_assessment.owned_by = request.user
            schedule_assessment.save()

            weightage += int(record['weightage'])

            if record['week']:
                record['week'] = int(record['week'])
                assessment_week_dict[str(schedule_assessment.id)] = record['week']

    if records:
        state_learning_unit = SCHEDULE_ASSESSMENT_MODEL['status-learning-unit']['test' if test_mode else 'normal'].objects.filter(
            current_year=SYSTEM_CURRENT_YEAR.current_year,
            current_semester=SYSTEM_CURRENT_YEAR.current_semester,
            lu=learning_unit).first()
        if not state_learning_unit:
            state_learning_unit, _ = SCHEDULE_ASSESSMENT_MODEL['status-learning-unit']['test' if test_mode else 'normal'].objects.get_or_create(
                current_year=SYSTEM_CURRENT_YEAR.current_year,
                current_semester=SYSTEM_CURRENT_YEAR.current_semester,
                lu=learning_unit)
        state_learning_unit.weightage = weightage
        state_learning_unit.updated_by = request.user
        state_learning_unit.save()

    record_assessment_methods = [record['assessment_method'] for record in records]
    record_assessment_methods = list(set(record_assessment_methods))
    for record_assessment_method in record_assessment_methods:
        get_assessment_task(record_assessment_method,
                            learning_unit, assessment_week_dict, test_mode)

    return HttpResponse(200)


def get_assessment_task(assessment_method: Assessment, learning_unit: LearningUnit, assessment_week_dict: dict, test_mode=None):
    # Execute per group assessment
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
    test_fa_week = RuleDefinition.objects.filter(
        slug='test_fa_week').first().value
    if type(test_fa_week) == str:
        test_fa_week = int(test_fa_week)

    if 'test' in assessment_method.slug:
        filter_conditions = Q(assessment_method__slug__icontains='test')
    else:
        filter_conditions = Q(assessment_method=assessment_method)

    filter_conditions &= Q(lu=learning_unit, current_year=SYSTEM_CURRENT_YEAR.current_year,
                           current_semester=SYSTEM_CURRENT_YEAR.current_semester)

    schedule_assessments = SCHEDULE_ASSESSMENT_MODEL['schedule-assessment']['test' if test_mode else 'normal'].objects.filter(filter_conditions).annotate(
        temporary_week=Case(
            *[When(id=k, then=v) for k, v in assessment_week_dict.items()],
            default=0,  # You can set a default value if needed
            output_field=IntegerField(),
        )
    ).order_by('temporary_week')

    for task_number, schedule_assessment in enumerate(schedule_assessments):
        if str(schedule_assessment.id) in assessment_week_dict:
            TESTFAWEEK = True if assessment_week_dict[str(
                schedule_assessment.id)] == test_fa_week else False
        else:
            TESTFAWEEK = False

        # Assign Assm Task
        predefined_assm_task = None
        assm_task = schedule_assessment.assessment_method.task
        if assm_task:
            assm_task_categories = assm_task.split(",")
            for assm_task_category in assm_task_categories:
                if 'TESTFA' in assm_task_category and TESTFAWEEK and schedule_assessment.lu.is_examinable == 'yes' and (schedule_assessment.assessment_method.slug == "test-on-campus-proctored-eassm-penpaper" or schedule_assessment.assessment_method.slug == "test-on-campus-proctored-eassm-keyboard") and (not 'CET' in schedule_assessment.lu.course_type):
                    predefined_assm_task = 'TESTFA'
                else:
                    if 'TESTFA' in assm_task_category:
                        continue

                    assm_task_components = assm_task_category.split("-")
                    assm_task_component = separate_string_and_number(
                        assm_task_components[0])[0]

                    predefined_assm_task = f"{
                        assm_task_component} {task_number+1}"

                schedule_assessment.assessment_task = predefined_assm_task
                schedule_assessment.save()

    return True


def update_ica_detail(request):
    if request.method == "POST":
        test_mode = request.POST.get('test_mode', None)
        if test_mode == 'None':
            test_mode = None

        learning_unit_id = request.POST.get("learning_unit_id", None)

        learning_unit = LearningUnit.objects.filter(id=learning_unit_id).first()

        # create records
        records = [
            {
                'schedule_assessment_id': schedule_assessment,
                'duration': duration if duration else 0,
                'moderation_method': ModerationMethod.objects.filter(id=moderation_method).first() if moderation_method else None,
                'digital_submission': digital_submission,
                'descriptions': description_of_assessment,
                'assessment_type': assessement_type,
                'topics': topic,
                'reasons_change': reason_for_changes,
                'note': note
            }
            for schedule_assessment, duration, moderation_method, assessement_type, digital_submission, description_of_assessment, topic, reason_for_changes,note in zip(
                request.POST.getlist('schedule_assessment'),
                request.POST.getlist('duration'),
                request.POST.getlist('moderation-method'),
                request.POST.getlist('assessement-type'),
                request.POST.getlist('digital-submission'),
                request.POST.getlist('description-of-assessment'),
                request.POST.getlist('topics-LU'),
                request.POST.getlist('reason-for-changes'),
                request.POST.getlist('note')
            )
        ]

        rule = BusinessRules.objects.filter(slug='rule').first()
        if rule:
            if test_mode:
                rule.stage2 = ast.literal_eval(rule.stage2_test)
            else:
                rule.stage2 = ast.literal_eval(rule.stage2)

        schedule_assessment = None
        request_scheduled_week = {}
        for record in records:

            schedule_assessment = SCHEDULE_ASSESSMENT_MODEL['schedule-assessment']['test' if test_mode else 'normal'].objects.filter(
                id=record['schedule_assessment_id']).first()
            
            if schedule_assessment:
                for default_property_key in record:
                    setattr(schedule_assessment, default_property_key,
                            record[default_property_key])
                schedule_assessment.updated_by = request.user
                if not schedule_assessment.owned_by:
                    schedule_assessment.owned_by = request.user
                schedule_assessment.save()

                request_scheduled_week[str(
                    schedule_assessment.id)] = schedule_assessment.week

        for idx, record in enumerate(records):
            schedule_assessment = SCHEDULE_ASSESSMENT_MODEL['schedule-assessment']['test' if test_mode else 'normal'].objects.filter(id=record['schedule_assessment_id']).first()
            if schedule_assessment:
                try:
                    validation_result = {}
                    if rule:
                        if rule.stage2:
                            condition_rule = rule.stage2[0]
                            if 'conditions' in condition_rule:
                                condition_flag = False
                                if 'all' in condition_rule['conditions']:
                                    if condition_rule['conditions']['all']:
                                        condition_flag = True
                                elif 'any' in condition_rule['conditions']:
                                    if condition_rule['conditions']['any']:
                                        condition_flag = True

                                if condition_flag and not rule.status_admin_break_rule_stage2:
                                    validation_result = validate_with_tracker(schedule_assessment, rules=rule.stage2, group_input={
                                                                            'weeks': request_scheduled_week}, test_mode=test_mode)
                                else:
                                    validation_result['triggered_rules'] = [
                                        "mark_valid"]

                    if validation_result:
                        if not validation_result['triggered_rules']:

                            schedule_assessment.broken_rules = validation_result['broken_rules'][0]
                            schedule_assessment.broken_rules_stage2 = validation_result['broken_rules'][0]
                            schedule_assessment.save()
                        else:
                            schedule_assessment.broken_rules = None
                            schedule_assessment.broken_rules_stage2 = None
                            schedule_assessment.save()

                except Exception as e:
                    print("e: ", e)
                    schedule_assessment.broken_rules = {
                        'failed_conditions': [f"Error: {e}"]}
                    schedule_assessment.broken_rules_stage2 = {
                        'failed_conditions': [f"Error: {e}"]}
                    schedule_assessment.save()

        assign_status(learning_unit=learning_unit,
                      user=request.user, ica_detail_submit=True, test_mode=test_mode)

    base_url = reverse('schedule_assessment:icaSchedule', kwargs={
                       'id': request.POST.get("learning_unit_id", None)})+"?redirect_to=ica-detail&save_detail=true"

    if test_mode:
        base_url += "&test_mode=true"

    return redirect(f'{base_url}')


def assign_status(learning_unit: LearningUnit, user: User, ica_detail_submit=False, test_mode=None):
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()

    state_learning_unit = SCHEDULE_ASSESSMENT_MODEL['status-learning-unit']['test' if test_mode else 'normal'].objects.filter(
        current_year=SYSTEM_CURRENT_YEAR.current_year, current_semester=SYSTEM_CURRENT_YEAR.current_semester, lu=learning_unit).first()
    if not state_learning_unit:
        state_learning_unit, _ = SCHEDULE_ASSESSMENT_MODEL['status-learning-unit']['test' if test_mode else 'normal'].objects.get_or_create(
            current_year=SYSTEM_CURRENT_YEAR.current_year, current_semester=SYSTEM_CURRENT_YEAR.current_semester, lu=learning_unit)

    state_learning_unit.ica_detail_submit = ica_detail_submit
    state_learning_unit.updated_by = user

    # assign Status
    schedule_assessments = SCHEDULE_ASSESSMENT_MODEL['schedule-assessment']['test' if test_mode else 'normal'].objects.filter(
        lu=learning_unit, current_year=SYSTEM_CURRENT_YEAR.current_year, current_semester=SYSTEM_CURRENT_YEAR.current_semester).order_by('week')
    state_learning_unit.status = 'pending'
    if schedule_assessments:
        count_week_none = schedule_assessments.filter(
            week__isnull=False).count()
        if count_week_none > 0:
            state_learning_unit.status = 'saved'

        # stage 2
        if state_learning_unit.ica_detail_submit and state_learning_unit.status == 'saved':
            if not schedule_assessments.filter(broken_rules_stage2__isnull=False):
                state_learning_unit.status = 'submitted'

    if state_learning_unit.status == 'pending':
        state_learning_unit.ica_detail_submit = False

    state_learning_unit.save()

    return


def checking_open_or_close_ica(learning_unit: LearningUnit):

    # In comparing the date doesnt need to convert into string. strftime
    systemedit = SystemEditMode.objects.get(group=learning_unit.phase)
    datenow = timezone.now()

    # Initialize edit flag
    is_edit:bool=False
    is_edit_ica_detail:bool=False
    
    # Check edit conditions
    if systemedit.mode == "on":  # All state inside this condition consider as system edit mode ON
        # Check if current time is within the edit window
        start_ok = not systemedit.start or datenow >= systemedit.start
        end_ok = not systemedit.end or datenow <= systemedit.end
        
        if start_ok and end_ok:
            is_edit = True
       
    elif systemedit.mode == "off":
        is_edit = False
        is_edit_ica_detail = False

    elif systemedit.mode == "interim":
        is_edit = False
        # Check if current time is within the interim edit window
        start_ok = not systemedit.start or datenow >= systemedit.start
        end_ok = not systemedit.end or datenow <= systemedit.end
        
        if start_ok and end_ok:
            is_edit_ica_detail = True
    if systemedit.edit_after == "on" and systemedit.end:
        if datenow >= systemedit.end:
            is_edit = True
            is_edit_ica_detail=True

    elif systemedit.edit_after == "interim" and systemedit.end:
        if datenow >= systemedit.end:
            is_edit = False
            is_edit_ica_detail = True

    elif systemedit.edit_after == "off" and systemedit.end:
        if datenow >= systemedit.end:
            is_edit = False
            is_edit_ica_detail = False
            
   

    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
    status_learning_unit = StatusLearningUnit.objects.filter(lu=learning_unit,
                                      current_year=SYSTEM_CURRENT_YEAR.current_year,
                                      current_semester=SYSTEM_CURRENT_YEAR.current_semester).first()
    
    if status_learning_unit:
        if status_learning_unit.status == 'approved':
            is_edit_ica_detail=False
            is_edit=False
    return is_edit, is_edit_ica_detail
