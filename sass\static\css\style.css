@import url("https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,100..900;1,100..900&display=swap");
:root {
  --orange: #ff9966;
  --black: #565454;
  --gray: #787676;
  --light-orange: #fff1e8;
  --white: #fafafa;
  --danger: #ff6665;
  --green: #18884f;
  --blue: #1a78e7;
  --light-blue: #e2eff8;
  --primary: #034ea2;
}

html,
body {
  overflow-x: hidden;
}

body {
  font-family: "Public Sans", sans-serif;
  background-color: #f4f4f4;
}
.container {
  max-width: 1233px;
}
.bg-primary {
  background-color: var(--primary);
}
.text-danger {
  color: #ff6665 !important;
}
.text-black {
  color: var(--black) !important;
}
.bg-orange {
  background-color: var(--orange);
}

.font-public-sans {
  font-family: "Public Sans", sans-serif;
}

.py-20 {
  padding: 20px 0;
}

.text-white {
  color: var(--white);
}
.outer-user h3 {
  font-size: 16px;
  font-weight: 700;
  margin: 0;
}
.card {
  border-radius: 16px;
}
.top-nav {
  margin-bottom: 55px;
}
.card-welcome {
  padding: 75px;
  border-radius: 16px;
  background-color: #fff;
  border: 0.5px solid #787676;
}

.cat {
  width: 227px;
  display: block;
  margin-left: auto;
}
.card-welcome h2 {
  font-size: 32px;
  font-weight: 700;
  line-height: 37.6px;
  color: var(--primary);
  margin-bottom: 24px;
}

.card-welcome h3 {
  font-size: 24px;
  font-weight: 600;
  line-height: 28.2px;
  color: #343232;
  margin-bottom: 41px;
}

.welcome-menu a {
  margin: 0;
  font-size: 20px;
  font-weight: 400;
  line-height: 23.5px;
  color: var(--gray);
}

.title-section {
  font-size: 24px;
  font-weight: 700;
  line-height: 28.2px;
  color: var(--primary);
  margin-bottom: 24px;
}
.title-section-preview {
  color: var(--black) !important;
}

.form-control {
  border: 0.5px solid #bbbbbb;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 8px;
}

.form-control::-ms-input-placeholder {
  /* Edge 12-18 */
  color: #bbbbbb;
  font-size: 16px;
  font-style: italic;
  font-weight: 400;
  line-height: 18.8px;
}

.form-control::placeholder {
  color: #bbbbbb;
  font-size: 16px;
  font-style: italic;
  font-weight: 400;
  line-height: 18.8px;
}
.card {
  border: 0.5px solid #fafafa;
}
.card-shadow {
  box-shadow: 0px 0px 20px 0px #00000033;
}

.form-group label {
  margin-bottom: 8px;
}
.card-assessment-table {
  padding: 32px 64px;
  border-radius: 16px;
}

.schedule-assessment-card h4 {
  font-size: 20px;
  font-style: italic;
  font-weight: 400;
  line-height: 23.5px;
  margin-bottom: 24px;
}
.table-sass {
  margin-top: 18px;
}
.table-sass > :not(caption) > * > * {
  padding: 0;
}
.table-sass thead {
  background-color: var(--light-blue);
  color: var(--primary);
}

.table-sass thead tr th {
  padding: 16px;
  vertical-align: middle;
}
.table-sass tr td {
  padding: 24px 16px;
  font-size: 16px;
  font-weight: 400;
  line-height: 18.8px;
  position: relative;
  vertical-align: middle;
}
.pagination a {
  font-size: 16px;
  font-weight: 400;
  line-height: 18.8px;
  text-decoration: none;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding-top: 5px;
}

.pagination a.active {
  background-color: var(--light-blue);
  color: var(--blue);
}

.tr-cursor-pointer tbody tr:hover {
  cursor: pointer;
  background-color: #f4f4f4;
}

.breadcrumb {
  display: flex;
  gap: 14px;
  align-items: center;
  margin-bottom: 40px;
}
.breadcrumb svg {
  flex-shrink: 0;
}

.breadcrumb a {
  color: #1a78e7;
  text-decoration: underline;
}

/* Custom checkbox */
/* Customize the label (the container) */
.wrapper-checkbox {
  display: flex;
  position: relative;
  padding-left: 23px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  align-items: center;
  justify-content: flex-start;
  height: 18px;
}

/* Hide the browser's default checkbox */
.wrapper-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkmark {
  position: absolute;
  top: 50%;
  left: 0;
  height: 18px;
  width: 18px;
  background-color: #fafafa;
  border-radius: 3px;
  border: 2px solid #bbbbbb;
  transform: translateY(-50%);
  overflow: hidden;
}

/* On mouse-over, add a grey background color */
.wrapper-checkbox:hover input ~ .checkmark {
  background-color: #ccc;
}

/* When the checkbox is checked, add a blue background */
.wrapper-checkbox input:checked ~ .checkmark {
  background-color: #fff;
  border: 2px solid var(--primary);
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.wrapper-checkbox input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.wrapper-checkbox .checkmark:after {
  left: -2px;
  top: -2px;
  width: 18px;
  height: 18px;
  background: url(../img/checkmark.svg) no-repeat center;
  background-size: cover;
  border: 2px solid var(--primary);
  background-color: var(--primary);
}
.wrapper-checkbox-test .checkmark:after {
  left: -2px;
  top: -2px;
  width: 18px;
  height: 18px;
  background: url(../img/checkmark-black.svg) no-repeat center;
  background-size: cover;
  border: 2px solid var(--black);
  background-color: var(--black);
}
/* End Custom checkbox */

.form-control.error {
  background-color: var(--light-blue);
}
.form-control.error-test {
  background-color: #eff0ee;
}
.card {
  background-color: #fafafa;
  border: 0.5px solid #787676;
}
.card-padding {
  padding: 48px 40px;
}
.tab-ica {
  margin-bottom: 32px;
}
.item-ica-card:last-child {
  margin-bottom: 0;
}
.tab-ica button,
.tab-ica a {
  border: none;
  font-size: 20px;
  background-color: transparent;
  color: var(--primary);
  font-weight: 400;
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
}
.tab-ica-test button,
.tab-ica-test a {
  color: var(--black);
}

.tab-ica button.active,
.tab-ica a.active {
  font-weight: 700;
  background-color: var(--light-blue);
  margin-bottom: 32px;
}
.inner-card {
  border: 0.5px solid #787676;
  border-radius: 8px;
  padding: 48px;
  text-align: center;
  background-color: #ededed;
}

.inner-card svg {
  margin-bottom: 36px;
}

.inner-card p {
  font-size: 16px;
  font-weight: 400;
  line-height: 18.8px;
  color: #787676;
}
p:last-child,
p:last-of-type {
  margin-bottom: 0;
}
.btn-text {
  font-size: 16px;
  font-weight: 400;
  line-height: 18.8px;
  color: #787676;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  background-color: transparent;
  padding: 12px;
}
.hide {
  display: none;
}

.item-ica-card {
  padding: 32px;
  gap: 32px;
  border-radius: 8px;
  border: 0.5px solid #787676;
  margin-bottom: 32px;
}
.title-card {
  font-size: 20px;
  font-weight: 700;
  line-height: 23.5px;
  color: var(--primary);
}
.title-card-test {
  color: var(--black);
}
.btn-primary {
  background-color: var(--primary);
  color: #fff;
}
.btn-view-details-log {
  width: 120px;
  padding: 8px;
}
.btn-black {
  background-color: var(--black);
}
.disabled {
  opacity: 0.5;
  pointer-events: none;
  -webkit-user-select: none;
  user-select: none;
}
.disabled-upload {
  pointer-events: none;
  -webkit-user-select: none;
  user-select: none;
}
p + p {
  margin-top: 0;
}
.popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999999;
}
.popup-wide .content-popup {
  max-width: 578px;
  width: 578px;
  padding: 32px 40px 32px 40px;
}
.backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.content-popup {
  position: relative;
  z-index: 999;
  background-color: #fff;
  padding: 30px 40px;
  text-align: center;
  max-width: 474px;
  max-height: 80vh;
  overflow-y: auto;
}

.content-popup h2 {
  font-size: 24px;
  font-weight: 700;
  line-height: 28.2px;
  color: var(--primary);
  margin-bottom: 20px;
}

.btn-orange {
  background-color: var(--orange);
  color: #fff;
}

.btn-orange:hover {
  background-color: #fb8144;
  color: #fff;
}
.form-group {
  margin-bottom: 15px;
}
.text-gray {
  color: #9e9e9e;
}
.btn {
  padding: 12px;
  font-size: 16px;
  font-weight: 700;
  line-height: 18.8px;
  text-transform: none;
}
.item-schedule p {
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 400;
  line-height: 18.8px;
}

.outer-ica-schedule {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.item-schedule {
  width: 20%;
}
.has-tooltips {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
}

.tooltips {
  position: relative;
  cursor: pointer;
}

.tooltips-content {
  position: absolute;
  left: -70px;
  top: 35px;
  background-color: #565454;
  color: #fff;
  padding: 8px 12px;
  width: 300px;
  font-size: 14px;
  display: none;
  transition: opacity 0.3s ease;
  opacity: 0;
  border-radius: 4px;
}
select {
  -webkit-appearance: auto;
  -moz-appearance: auto;
  appearance: auto;
}
.tooltips:hover .tooltips-content {
  display: block;
  opacity: 1;
  z-index: 9;
}
.wrapper-select,
.wrapper-calendar {
  position: relative;
}

.wrapper-select::after {
  content: "";
  position: absolute;
  right: 10px;
  top: 50%;
  background: url(../img/chevron-down.svg) no-repeat center;
  width: 16px;
  height: 16px;
  background-size: contain;
  pointer-events: none;
  transform: translateY(-50%);
}

.wrapper-calendar::after {
  content: "";
  position: absolute;
  right: 10px;
  top: 50%;
  background: url(../img/icon-input-calendar.svg) no-repeat center;
  width: 16px;
  height: 16px;
  background-size: contain;
  pointer-events: none;
  transform: translateY(-50%);
}
.tooltips-content ol {
  margin: 0;
  border-radius: 2px;
}
.tooltips-content p {
  font-size: 12px;
}
.tooltips-content:after {
  content: "";
  width: 15px;
  height: 15px;
  background-color: #565454;
  position: absolute;
  top: -4px;
  left: 70px;
  transform: rotate(45deg);
  border-radius: 2px;
}

.menu-system-management {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  list-style: none;
  margin: 0 0 24px 0;
  padding: 0;
  flex-wrap: wrap;
}
.menu-user-management button {
  font-size: 16px;
  font-weight: 400;
  line-height: 18.8px;
}
.v-center {
  display: flex;
  align-items: center;
}
.menu-system-management li {
  padding: 8px 16px;
  color: var(--primary);
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
}
.icon-image {
  width: 18px;
  height: 20px;
  object-fit: contain;
}
.menu-system-management li.active {
  background-color: var(--light-blue);
}

.list-selected-user {
  padding: 12px 16px;
  border-radius: 4px;
  background: #ededed;
  width: 100%;
}
.select-compact {
  padding: 4px 12px;
  border-radius: 8px;
  border: 0.5px solid #bbbbbb;
  opacity: 0px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  display: block;
  width: 100%;
}

button:active,
button:focus,
select:active,
select:focus {
  outline: none;
  box-shadow: none;
}

.bg-gray {
  background-color: #eaeaea !important;
}
.wrapper-account {
  position: relative;
  height: 100%;
}
.account {
  cursor: pointer;
  -moz-user-select: none;
  -webkit-user-select: none;
  user-select: none;
}
.popup-account {
  position: absolute;
  top: 45px;
  right: 0;
  padding: 12px 8px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: 1px 4px 4px 0px #0000001f;
  width: 137px;
}
.row-form-ica {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.row-form-ica .form-group {
  width: 25%;
}
.table-lu-status tbody tr {
  cursor: pointer;
}
p {
  font-size: 16px;
}
.select2-container .select2-search--inline .select2-search__field {
  vertical-align: unset;
}
.select2-container--default .select2-selection--multiple {
  background-color: #fafafa !important;
}
.headmap-head h3 {
  font-size: 14px;
  font-weight: 700;
  margin: 0;
  height: 32px;
  display: flex;
  align-items: center;
}
.new-headmap {
  max-height: 202px;
  overflow: auto;
}
.headmaps {
  display: flex;
  gap: 24px;
}

.headmap-head {
  width: 114px;
  flex-shrink: 0;
}
.card-headmap {
  padding: 23px 70px;
}
.item-legend span {
  display: block;
  width: 16px;
  height: 16px;
  gap: 0px;
  border-radius: 2px 0px 0px 0px;
  opacity: 0px;
}

.item-legend {
  display: flex;
  align-items: center;
  gap: 8px;
}
.mCSB_inside > .mCSB_container {
  margin-right: 0;
}
.legend-headmap {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 24px;
  gap: 16px;
}

.headmap-head ul li {
  padding: 0;
  height: 32px;
  display: flex;
  align-items: center;
}

.row__headmap-content {
  display: flex;
  gap: 2px;
}
.bg-black {
  background-color: #343232 !important;
}
.bg-green {
  background-color: #95e980;
}
.bg-yellow {
  background-color: #ffdd66;
}
.bg-red {
  background-color: #ff6665;
}
.row__headmap-content span {
  width: 28.5px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  flex-shrink: 0;
}
.headmap-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.headmap-content-dynamic {
  width: 100%;
}

.headmap-content-static {
  width: calc(100% - 147px);
}
.headmap-head {
  padding-top: 0;
}
.headmap-head ul {
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 16.45px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.mCSB_outside + .mCSB_scrollTools {
  right: 0;
}
.custom-scroll-live .mCSB_outside + .mCSB_scrollTools {
  right: -10px;
}

.edit-mode {
  margin-top: 32px;
}
/* Custom radio */
.container-radio {
  display: inline-flex;
  position: relative;
  padding-left: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  font-size: 16px;
  font-weight: 400;
  line-height: 18.8px;
}
.edit-mode h3 {
  font-size: 16px;
  font-weight: 400;
  line-height: 18.8px;
}
.container-radio input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.container-radio .checkmark {
  position: absolute;
  top: 50%;
  left: 0;
  height: 17px;
  width: 17px;
  background-color: #eee;
  border-radius: 50%;
}
.container-radio:hover input ~ .checkmark {
  background-color: #ccc;
}

.container-radio input:checked ~ .checkmark {
  background: var(--primary) url(../img/radio-blue.svg) no-repeat center;
  background-size: contain;
  border: 2px solid var(--primary);
}
.container-radio.container-radio-edit input:checked ~ .checkmark {
  background: var(--green) url(../img/radio-green.svg) no-repeat center;
  background-size: contain;
  border: 2px solid var(--green);
}
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}
.container-radio input:checked ~ .checkmark:after {
  display: block;
}
.text-orange {
  color: var(--orange);
}
.text-primary {
  color: var(--primary);
}
.container-radio .checkmark:after {
  top: 50%;
  left: 50%;
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background: white;
  transform: translate(-50%, -50%);
}
/* End custom radio */

.btn-outline-orange {
  background-color: #fafafa;
  padding: 12px;
  border-radius: 4px;
  border: 0.5px solid var(--orange);
  color: var(--orange);
}
.btn-outline-primary {
  background-color: #fafafa;
  padding: 12px;
  border-radius: 4px;
  border: 0.5px solid var(--primary);
  color: var(--primary);
  transition: 0.3s;
}
.btn-outline-primary:hover {
  background-color: var(--primary);
  padding: 12px;
  border-radius: 4px;
  border: 0.5px solid var(--primary);
  color: #fff;
}
.btn-outline-orange:hover {
  background-color: #ff9966;
  padding: 12px;
  border-radius: 4px;
  border: 0.5px solid var(--orange);
  color: #fff;
}
a {
  color: #1a78e7;
  text-decoration: underline;
}
.checkmark.disabled {
  background-color: #bbbbbb;
}
.table-edit-role-management .wrapper-checkbox input:checked ~ .checkmark {
  background: url(../img/checkmark-green.svg) no-repeat center !important;
  background-color: #18884f;
  border: 2px solid #18884f;
}
.wrapper-checkbox-test input:checked ~ .checkmark {
  background: url(../img/checkmark-black.svg) no-repeat center !important;
  background-color: #343232;
  border: 2px solid #343232;
}

.table-edit-role-management .wrapper-checkbox .checkmark:after {
  background: url(../img/checkmark-green.svg) no-repeat center !important;
  background-size: cover;
  background-color: #18884f;
  border: 2px solid #18884f;
}
.welcome-menu a {
  text-decoration: none;
}
.accordion-report .accordion-item {
  margin-bottom: 16px;
  border-radius: 8px;
  background-color: #fafafa;
  border: 0.5px solid #787676;
}
.custom-accordion-item {
  margin-bottom: 16px;
  border-radius: 8px;
  background-color: #fafafa;
  border: 0.5px solid #787676;
}
.custom-accordion-item .custom-accordion-body {
  display: none;
}
.custom-accordion-item.show .custom-accordion-body {
  display: block;
}
.custom-accordion-item.show .custom-accordion-header img {
  transition: 0.3s;
}
.custom-accordion-header {
  padding: 24px 32px;
}
.custom-accordion-item.show .custom-accordion-header img {
  transform: rotate(540deg);
}
.custom-accordion-body {
  padding-bottom: 24px;
  padding-left: 32px;
  padding-right: 32px;
}
.custom-accordion-item h2 {
  font-size: 16px;
  font-weight: 700;
  line-height: 18.8px;
  color: var(--primary);
  margin: 0;
}
.custom-accordion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}
.accordion-report.accordion-button:not(.collapsed) {
  background-color: #fafafa;
}
.accordion-report .accordion-button {
  background-color: #fafafa;
  border-radius: 8px;
}
.accordion-report .accordion-header button {
  font-size: 16px;
  font-weight: 400;
  line-height: 18.8px;
  color: #787676;
}
.accordion-report .accordion-button:not(.collapsed) {
  color: var(--primary);
  font-weight: 700;
}
footer {
  padding: 32px 0;
  background-color: #e3e3e3;
  font-size: 12px;
  font-weight: 400;
  line-height: 14.52px;
}
.table-live-schedule td button {
  font-size: 14px;
  text-decoration: underline;
}
.table-live-schedule td button:hover {
  text-decoration: none;
}
.table-live-schedule td button {
  font-size: 14px;
  text-decoration: underline;
}
.table-live-schedule td {
  font-size: 14px;
  font-weight: 400;
  line-height: 14.8px;
  padding: 8px;
}

.table-live-schedule th {
  font-size: 12px;
  font-weight: 700;
  line-height: 14.8px;
  padding: 8px;
}
.wrapper-table-live {
  border-radius: 8px;
  border: 0.5px solid #787676;
  padding: 16px;
  box-shadow: 0px 0px 20px 0px #00000014;
  position: relative;
}
.wrapper-table-course {
  height: 100%;
}
.card-live-schedule {
  padding: 24px;
}
.wrapper-table-live a {
  color: #787676;
}
.wrapper-table-live a:hover {
  color: #787676;
}
.table-live-schedule tbody tr {
  border-bottom: 0.5px solid #bbbbbb;
}
.table-live-schedule tbody tr:last-child {
  border-bottom: none;
}
.wrapper-table-course {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  position: relative;
  height: 200px;
}
.loading-absolute {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 2;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 100px;
}
.loading {
  display: none;
  z-index: -99;
}
.htmx-request .loading {
  display: flex;
  opacity: 1;
  z-index: 99;
}
.htmx-request.loading {
  display: flex;
  opacity: 1;
  z-index: 99;
}
.loading img {
  width: 50px;
  display: block;
  margin: 0 auto;
}
.empty-course {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #787676;
}
.empty-schedule-details {
  font-size: 16px !important;
  color: #787676;
  padding: 60px 0 !important;
}
.table-activity-log tr td {
  font-size: 14px;
}
.table-activity-log tr td:nth-child(7) {
  word-break: break-all;
}

.table-activity-log tr td:first-child {
  width: 178px;
}

.table-activity-log tr td:nth-child(4) {
  width: 95px;
}
.vertical-table-mobile th:first-child {
  width: 100px;
}
.popup-log .content-popup {
  max-width: 90vw;
  padding: 24px;
}
.table-activity-log-popup th {
  font-size: 14px !important;
  font-weight: 700;
  text-align: left;
  padding: 8px !important;
}

.table-activity-log-popup td {
  font-size: 14px !important;
  text-align: left;
  padding: 8px 8px 30px !important;
}
.table-activity-log-popup {
  height: 50vh;
}
.cursor-pointer {
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
}
.loading-course {
  top: 48px;
}
.loading-details {
  top: 100px;
}
span.datetime {
  font-size: 14px;
  font-weight: 400;
  line-height: 16.45px;
  color: #bbbbbb;
  display: block;
  margin-top: 8px;
}
.alert-warning {
  background-color: #fffcf6;
  color: #565454;
  border: 1px solid #ff9966;
}
.alert {
  padding: 16px;
}
.alert-dismissible .btn-close {
  padding: 16px;
  top: 50%;
  transform: translateY(-50%);
}
.status-assessment {
  font-size: 16px;
  font-weight: 700;
  line-height: 18.8px;
  margin-bottom: 40px;
}
.loading-ica {
  padding: 80px 0;
}
.icon-sassy-popup {
  height: 80px;
  margin-bottom: 8px;
}
.text-blue {
  color: #1a78e7;
}
.alert-info {
  background-color: #f6faff;
  color: #565454;
  border: 1px solid #1a78e7;
}
.no-border {
  border: none;
  box-shadow: none;
  outline: none;
  background-color: transparent;
}
.table-set-edit-mode input {
  color: var(--gray);
}
.table-set-edit-mode tbody tr {
  cursor: pointer;
}
.form-control-edit-mode,
.form-control-edit-mode:focus {
  outline: none;
  padding: 8px;
  width: 100%;
  border-radius: 8px;
  appearance: none;
  border: 1px solid #034ea2;
  box-shadow: 0px 0px 2px 2px #c2deff80;
  color: #565454;
}
.btn-orange.disabled {
  background-color: var(--orange);
  color: #fff;
}
.table-edit-role-management tbody tr:not(:first-child) td:nth-child(5),
.table-role-management tbody tr:not(:first-child) td:nth-child(5) {
  border-right: 1px solid #bbbbbb;
}
.table-edit-role-management .wrapper-checkbox,
.table-role-management .wrapper-checkbox {
  width: 18px;
  margin: 0 auto;
}
.table-edit-role-management tbody tr:first-child td,
.table-role-management tbody tr:first-child td {
  border-bottom: 1px solid #bbbbbb;
}
.table-edit-role-management tbody tr:first-child td:not(:first-child),
.table-role-management tbody tr:first-child td:not(:first-child) {
  text-align: center;
}

.table-edit-role-management thead,
.table-role-management thead {
  border-bottom: 1px solid #bbbbbb;
}
.forbidden img {
  width: 185px;
}
.h-min-screen {
  height: calc(100vh - 269px);
}
label.error {
  color: red;
  font-size: 13px;
}
.loading-schedule-assessment {
  top: 300px !important;
}
.htmx-request .loading-content-schedule-assessment {
  z-index: 999;
}
.htmx-request.loading-content-schedule-assessment {
  opacity: 999;
}
.table-schedule-assessment {
  position: relative;
}
.p-relative {
  position: relative;
}
.loading-user-management {
  top: 80px;
}

.loading-lu-status {
  top: 133px;
}
.loading-activity-log {
  top: 95px;
}
.ica-disabled select,
.ica-disabled input {
  padding: 12px 0;
  border: none;
  pointer-events: none;
}
.wrapper-percent {
  position: relative;
}
.form-ica-schedule.wrapper-percent:before {
  content: "%";
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 35px;
  background-color: #e9e9e9;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0 8px 8px 0;
}
.form-ica-schedule.ica-disabled.wrapper-percent:before {
  display: none;
}

.form-ica-schedule.ica-disabled.wrapper-percent {
  position: relative;
}
.ica-disabled::after {
  display: none;
}
.form-control:focus {
  border-color: #bbbbbb;
  box-shadow: none;
}
.item-ica-detail-card {
  border: 0.5px solid #787676;
  padding: 32px;
  margin-bottom: 16px;
}
.item-ica-detail-card:last-child {
  margin-bottom: 0;
}

/* Rule Cofiguration style */
.conditional
  .conditional
  .conditional
  .conditional
  .conditional
  .conditional
  .conditional
  .add-condition {
  display: none !important;
}
.content-row div[class^="col-"] {
  position: relative;
}
.rule div[class^="col-"]:last-child {
  position: relative;
}
.remove-rule {
  position: absolute;
  width: 20px;
  height: 20px;
  right: 0;
  top: 50%;
  transform: translateX(20px) translateY(-50%);
  z-index: 5;
}
.remove-rule::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  width: 15px;
  height: 15px;
  background: url(../../static/img/icon-close.svg) no-repeat center;
  background-size: contain;
  cursor: pointer;
}
#conditions a,
#conditions-detail a {
  display: inline-block;
  margin: 10px 0;
}
#conditions,
#conditions-detail,
#actions,
#actions-detail {
  margin: 20px 0;
}

#conditions > .conditional > .remove,
#actions > .conditional > .remove,
#conditions-detail > .conditional > .remove,
#actions-detail > .conditional > .remove {
  display: none;
}

#error_message_wrapper {
  margin: 20px 0;
}

.conditional {
  padding-left: 60px;
}

/* .conditional .all-any-wrapper {
  margin: 5px 20px 5px -50px;
  display: -moz-inline-stack;
  display: inline-block;
} */

.all-any {
  margin-right: 10px;
}

.all-any-wrapper,
.conditional,
.rule {
  margin: 10px 0;
}

.add-rule,
.add-condition,
.remove {
  margin: auto 5px;
}

.remove {
  color: var(--danger);
}

.input-any-all {
  width: 250px;
}

.rule select,
.rule input {
  margin-right: 10px;
}

.action-buttons {
  margin: 20px 0;
}

.action {
  margin: 10px 0;
}

.action .subfields,
.action .subfields .field {
  display: inline;
}

.action select,
.action input,
.action textarea {
  margin-right: 10px;
}

.action textarea,
.action input {
  width: 250px;
}

.action textarea {
  vertical-align: top;
  height: 50px;
}

#demo-form {
  position: absolute;
  top: 0px;
  right: 0px;
  padding: 20px;
  width: 300px;
  background: #ddd;
}

#demo-form div {
  margin: 10px 0;
}
.input-any-all {
  width: 60px !important;
  text-align: center;
}

.all-any-wrapper .wrapper-select {
  width: 140px;
}

.all-any-wrapper .wrapper-select select {
  margin: 0;
}
a.add-rule,
a.add-sub-condition,
.remove-sub-condition {
  text-decoration: none;
}

#conditions,
#conditions-detail {
  margin-right: 40px;
}
.rule {
  padding-left: 60px;
  position: relative;
}
.rule:before {
  content: "";
  width: 39px;
  height: 2px;
  background-color: #bbb;
  position: absolute;
  top: 50%;
  left: 21px;
  transform: translateY(-50%);
}
.conditional {
  position: relative;
}
select.value {
  max-width: 270px;
}
.wrapper-button a:not(.remove) {
  color: var(--black);
  text-decoration: none;
}
.wrapper-button {
  display: flex;
  gap: 10px;
}

.conditional:first-of-type:before {
  content: "";
  position: absolute;
  left: 21px;
  bottom: 23px;
  width: 1px;
  background-color: #bbb;
  top: 117px;
}
.all-any-none-wrapper {
  position: relative;
}

.all-any-none-wrapper::before {
  content: "";
  width: 39px;
  height: 2px;
  background-color: #bbb;
  position: absolute;
  left: -39px;
  top: 54%;
}
#conditions > .conditional > .all-any-none-wrapper::before,
#conditions-detail > .conditional > .all-any-none-wrapper::before {
  width: 0;
}
.conditional .conditional::before {
  content: "";
  position: absolute;
  left: 80px;
  bottom: 26px;
  width: 1px;
  background-color: #bbb;
  top: 120px;
}
.conditional.all .row div[class^="col-"]:first-child:after {
  content: "";
  width: 40px;
  height: 2px;
  background-color: #bbb;
  position: absolute;
  left: -28px;
  top: 50%;
  transform: translateY(-50%);
}

.conditional.all .row div[class^="col-"]:first-child {
  position: relative;
}

.conditional:first-of-type {
  padding: 0;
}
.conditions > .conditional .conditional {
  margin: 40px 0;
}
.conditions > .conditional > .conditional > .container-rule > .conditional {
  margin-bottom: 70px;
}
.conditional > .conditional:last-child:after {
  content: "";
  bottom: 0;
  top: 65px;
  width: 42px;
  left: 18px;
  background-color: #fafafa;
  position: absolute;
}

.container-condition {
  padding-left: 60px;
}
.conditions {
  margin-right: 60px;
}
/* End Rule Cofiguration style */
input#assm-task {
  background-color: transparent;
  border: none;
}
.table-live-schedule td:last-child {
  border: none;
}
.assm-task {
  padding: 0;
}

.assm-task-edit {
  border: 0.5px solid #bbbbbb !important;
  padding: 12px;
  width: 100%;
  border-radius: 8px;
}
.item-schedule .form-ica-schedule {
  position: relative;
  top: -5px;
  z-index: 2;
}
.form-ica-schedule.ica-disabled select {
  white-space: break-spaces;
}
.table-ver-live-scroll {
  height: 400px;
  width: 100%;
}

.table-live-schedule-scroll {
  width: 96%;
  font-size: 14px;
}

.table-live-schedule-scroll tr td {
  padding: 8px;
}
.wrapper-th-sort {
  display: inline-flex;
  justify-content: start;
  align-items: center;
  gap: 15px;
  cursor: pointer;
}

.wrapper-caret-sort {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.wrapper-caret-sort i {
  font-size: 12px;
}

.wrapper-caret-sort span {
  display: flex;
  height: 17px;
  justify-content: center;
  align-items: center;
}
.all-any-none-wrapper span {
  display: block;
  margin-top: 5px;
}
#actions select,
#conditions select,
#actions-detail select,
#conditions-detail select {
  padding: 8px 12px;
  border-radius: 8px;
  color: #787676;
  border: 0.5px solid #bbbbbb;
}
#conditions input,
#conditions-detail input {
  padding: 6px 12px;
  border-radius: 8px;
  color: #787676;
  border: 0.5px solid #bbbbbb;
}
.wrapper-button a.remove {
  position: relative;
  left: 20px;
  text-decoration: none;
}

.wrapper-button a.remove:before {
  content: "";
  width: 14px;
  height: 15px;
  position: absolute;
  top: 50%;
  left: -19px;
  background: url(/static/img/icon-trash-red.svg) no-repeat center;
  background-size: contain;
  transform: translateY(-50%);
}
#conditions > .conditional > .all-any-none-wrapper .remove,
#conditions-detail > .conditional > .all-any-none-wrapper .remove {
  display: none;
}
.form-control.is-invalid,
.was-validated .form-control:invalid {
  margin-bottom: 0;
  background-image: none;
  border-color: #dc4c64;
  color: #dc4c64;
  box-shadow: 0 0 3px 0px #dc4c64;
}
.form-control.is-invalid + span {
  border: 1px solid #dc4c64;
  box-shadow: 0 0 3px 0px #dc4c64;
}
.content-popup-upload {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.loading-heatmap {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
}
.h-200 {
  min-height: 200px;
}
.form-switch .form-check-input:checked[type="checkbox"]:after {
  content: "";
  position: absolute;
  border: none;
  z-index: 2;
  border-radius: 50%;
  width: 1.25rem;
  height: 1.25rem;
  background-color: #fafafa;
  margin-top: -3px;
  margin-left: 1.0625rem;
  box-shadow: none;
  transition: 0.3s;
  top: 5px;
  left: 1px;
}
.form-switch .form-check-input:after {
  content: "";
  position: absolute;
  border: none;
  z-index: 2;
  border-radius: 50%;
  width: 1.25rem;
  height: 1.25rem;
  background-color: #fff;
  margin-top: -0.1875rem;
  box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.07), 0 2px 2px 0 rgba(0, 0, 0, 0.04);
  transition: 0.3s;
  top: 5px;
  left: 3px;
}
.form-check-input[type="checkbox"]:checked {
  background-image: none;
  background-color: #0494e3;
  transition: 0.3s;
}
.form-check-input[type="checkbox"]:checked:focus {
  background-color: #0494e3;
}
.form-switch .form-check-input:checked:focus:before,
.form-switch .form-check-input:focus:before {
  box-shadow: none;
}
.form-switch .form-check-input {
  background-image: none;
  border-width: 0;
  border-radius: 50px;
  width: 40px;
  height: 25px;
  background-color: rgba(0, 0, 0, 0.25);
  margin-top: 0;
  margin-right: 8px;
}
.form-set-current .disabled {
  background-color: #ddd;
}
.table-wrapper {
  height: 200px;
  overflow: auto;
  display: inline-block;
  width: 100%;
}

::-webkit-scrollbar {
  width: 8px;
  height: 10px;
}

::-webkit-scrollbar-thumb {
  background-color: #4f4f4f;
  border-radius: 30px;
}

::-webkit-scrollbar-track {
  background-color: #b9b9b9;
  border-right: 1px solid rgb(169, 169, 169);
}
.select2-dropdown,
.xdsoft_datetimepicker {
  z-index: 999999;
}
.wrapper-upload__bulk-user input {
  width: 155px;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  font-size: 0;
  cursor: pointer;
}

.wrapper-upload__bulk-user {
  position: relative;
  cursor: pointer;
}
.form-input-group {
  display: flex;
  align-items: stretch;
  justify-content: flex-start;
}

.input-group-text {
  border-radius: 0 8px 8px 0;
  background-color: #e9e9e9;
}

.form-input-group input {
  border-radius: 8px 0 0 8px;
  border-right: none;
}
.box-under-construction {
  height: 200px;
  background-color: #ededed;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 15px;
}

.box-under-construction h5 {
  color: #897676;
}

/* Table live schedule */
.wrapper-table-live-schedule-scroll {
  width: 100%;
  max-height: 95vh;
  overflow-y: auto;
  overflow-x: hidden;
}
.has-tooltip__live-schedule .tooltip {
  position: absolute;
  background: #333333;
  padding: 10px;
  color: #fff;
  width: 200px;
  border-radius: 8px;
  opacity: 0;
  z-index: -999;
}
.has-tooltip__live-schedule .tooltip::after {
  content: "";
  width: 15px;
  height: 15px;
  background-color: #333;
  top: 50%;
  display: block;
  position: absolute;
  transform: translateY(-50%) rotate(45deg);
}
.has-tooltip__live-schedule .tooltip.align-right::after {
  left: -7px;
}
.has-tooltip__live-schedule .tooltip.align-left::after {
  right: -7px;
}
.has-tooltip__live-schedule .tooltip.show {
  opacity: 1;
  z-index: 999;
}
/* End Table live schedule */
/* Responsive */

@media (max-width: 1250px) {
  .row__headmap-content span {
    width: 3%;
    height: 23px;
  }
  .custom-scroll-content-headmap {
    width: 100%;
  }
  .mCustomScrollBox + .mCSB_scrollTools.mCSB_scrollTools_horizontal {
    bottom: -17px;
  }

  .headmap-head h3 {
    margin: 2px 0;
  }
  .headmap-head ul li {
    height: 23px;
  }
  .headmap-head h3 {
    font-size: 13px;
    font-weight: 700;
    margin: 0;
    height: 20px;
    display: flex;
    align-items: center;
  }
}
@media (min-width: 1200px) {
  .tooltips-content.align-right {
    right: -70px;
    left: unset;
  }

  .tooltips-content.align-right:after {
    right: 70px;
    left: unset;
  }
}

@media (max-width: 1199px) {
  .vertical-table-mobile {
    display: flex;
  }

  .vertical-table-mobile thead {
    flex-shrink: 0;
    width: 240px;
  }
  .vertical-table-mobile thead tr,
  .vertical-table-mobile tbody tr {
    display: flex;
    flex-direction: column;
  }
  .vertical-table-mobile tbody tr {
    border-bottom: none;
  }
  .table-live-schedule th:nth-child(1),
  .table-live-schedule th:nth-child(2),
  .table-live-schedule th:nth-child(3),
  .table-live-schedule th:nth-child(4),
  .table-live-schedule th:nth-child(5),
  .table-live-schedule th:nth-child(6),
  .table-live-schedule th:nth-child(7),
  .table-live-schedule td:nth-child(1),
  .table-live-schedule td:nth-child(2),
  .table-live-schedule td:nth-child(3),
  .table-live-schedule td:nth-child(4),
  .table-live-schedule td:nth-child(5),
  .table-live-schedule td:nth-child(6),
  .table-live-schedule td:nth-child(7) {
    height: 35px;
  }
  .vertical-table-mobile tbody {
    display: flex;
  }
  .table-live-schedule th:nth-child(8),
  .table-live-schedule th:nth-child(9),
  .table-live-schedule th:nth-child(10),
  .table-live-schedule th:nth-child(11),
  .table-live-schedule th:nth-child(12),
  .table-live-schedule td:nth-child(8),
  .table-live-schedule td:nth-child(9),
  .table-live-schedule td:nth-child(10),
  .table-live-schedule td:nth-child(11),
  .table-live-schedule td:nth-child(12) {
    height: 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: safe;
  }
  .vertical-table-mobile tbody tr {
    width: 300px;
    flex-shrink: 0;
    border-right: 0.5px solid #888;
    border-top: 0.5px solid #bbbbbb;
  }
  tbody.live-schedule-details {
    overflow-x: auto;
  }
  .welcome-menu {
    flex-wrap: wrap;
  }
  .vertical-table-mobile thead tr th {
    border-bottom: 1px solid #bbb !important;
  }

  .vertical-table-mobile th:first-child {
    width: 100%;
  }
  .loading-details {
    top: 0;
    left: 16em;
  }
  .empty-schedule-details {
    position: absolute;
    top: 0;
    left: 256px;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
@media (max-width: 991px) {
  .row__headmap-content span {
    width: 2.9%;
    height: 23px;
  }
  .row__headmap-content span {
    font-size: 12px;
  }

  .wrapper-table-course {
    min-height: 210px;
  }
  .wrapper-table-course {
    height: auto;
  }
  .empty-course {
    min-height: 100px;
  }
  .row-form-ica {
    flex-wrap: wrap;
  }

  .row-form-ica .form-group {
    width: 48%;
  }
  .outer-ica-schedule {
    flex-wrap: wrap;
  }

  .item-schedule {
    width: 100%;
  }
}

@media (max-width: 767px) {
  .headmap-head h3 {
    height: 24px;
  }
  .row__headmap-content span {
    width: 25px;
    height: 24.5px;
  }
  .row__headmap-content {
    margin-bottom: 2px;
  }

  .headmap-head ul li {
    height: 24.5px;
    display: flex;
    align-items: center;
    margin: 1px 0;
  }
  .headmap-head {
    padding-top: 4px;
  }
  .row__headmap-content span {
    font-size: 14px;
  }
  .new-headmap {
    max-height: unset;
    height: auto;
  }
  .container-condition .form-control {
    margin-bottom: 15px;
  }
  .card-headmap {
    padding: 23px 30px;
  }
  .legend-headmap {
    flex-wrap: wrap;
  }
  .popup-wide .content-popup {
    max-width: 80%;
    width: 80%;
    padding: 30px;
  }
  .card-welcome {
    padding: 30px;
    flex-wrap: wrap;
  }

  .card-assessment-table {
    padding: 25px;
  }
  .card-padding {
    padding: 28px 20px;
  }
  .cat {
    margin: 0 auto;
  }
}

@media (max-width: 575px) {
  .vertical-table-mobile thead {
    width: 130px;
  }
  .empty-schedule-details {
    left: 146px;
  }
  .loading-details {
    top: 0;
    left: 146px;
  }
  .vertical-table-mobile tbody tr {
    width: 200px;
  }

  .table-live-schedule th:nth-child(8),
  .table-live-schedule th:nth-child(9),
  .table-live-schedule th:nth-child(10),
  .table-live-schedule th:nth-child(11),
  .table-live-schedule td:nth-child(8),
  .table-live-schedule td:nth-child(9),
  .table-live-schedule td:nth-child(10),
  .table-live-schedule td:nth-child(11) {
    height: 80px;
  }
  .tooltips-content,
  .tooltips-content p {
    font-size: 11px;
    width: 200px;
  }
  .row-form-ica .form-group {
    width: 100%;
  }
  .card-welcome h2 {
    font-size: 25px;
  }
  .card-welcome h3 {
    font-size: 20px;
    line-height: 22px;
  }
  .welcome-menu a {
    font-size: 16px;
  }
  .schedule-assessment-card h4 {
    font-size: 18px;
  }

  .item-ica-card {
    padding: 15px;
    gap: 32px;
    border-radius: 8px;
    border: 0.5px solid #787676;
    margin-bottom: 32px;
  }
}
.table-lu-list tbody tr td .active {
  font-weight: 900;
}
.select2-container .select2-selection--single {
  box-sizing: border-box;
  cursor: pointer;
  display: block;
  height: 40px;
  user-select: none;
  -webkit-user-select: none;
}
.select2-container--default
  .select2-selection--single
  .select2-selection__rendered {
  color: #444;
  line-height: 40px;
  text-align: left;
}
.select2-container--default
  .select2-selection--single
  .select2-selection__arrow {
  height: 26px;
  position: absolute;
  top: 1px;
  right: 1px;
  width: 20px;
  display: none;
}
