(function ($) {
    let conditions,
        btnRunTest,
        actions,
        actionsDetail,
        submit,
        conditionsDetail,
        submitDetail;
    function onReady() {
        conditions = $("#conditions");
        conditionsDetail = $("#conditions-detail");
        actions = $("#actions");
        actionsDetail = $("#actions-detail");
        submit = $("#submit");
        submitDetail = $("#submit-detail");
        btnRunTest = $("button[name='run-test']");

        initializeConditions();
        initializeActions();
        initializeForm();
        initializeConditionsDetail();
        initializeActionsDetail();
        initializeFormDetail();
        runTest();
        $(".action-buttons, .action .remove").remove();
        conditionsDetail.find(".remove").addClass("remove-detail");
        conditions.find(".remove").addClass("remove-schedule");
    }

    let existingConditions = $(".rulefields").data("conditions");
    let fields = $(".rulefields").data("fields");
    function initializeConditions() {
        conditions.conditionsBuilder({
            fields: fields,
            data: existingConditions,
        });
    }

    let existingConditionsDetail = $(".rulefields").data("conditions-detail");
    let fieldsDetail = $(".rulefields").data("fields-detail");
    function initializeConditionsDetail() {
        conditionsDetail.conditionsBuilder({
            fields: fieldsDetail,
            data: existingConditionsDetail,
        });
    }

    function initializeActions() {
        actions.actionsBuilder({
            fields: [
                { label: "Mark_Valid", name: "Mark_Valid", value: "Mark" },
            ],
            data: [
                {
                    name: "action-select",
                    value: "Mark_Valid",
                    fields: [{ name: "newValue", value: "mark_valid" }],
                },
            ],
        });
    }

    function initializeActionsDetail() {
        actionsDetail.actionsBuilder({
            fields: [
                { label: "Mark_Valid", name: "Mark_Valid", value: "Mark" },
            ],
            data: [
                {
                    name: "action-select",
                    value: "Mark_Valid",
                    fields: [{ name: "newValue", value: "mark_valid" }],
                },
            ],
        });
    }

    function initializeForm() {
        submit.click(function (e) {
            e.preventDefault();
            var engine = new RuleEngine({
                conditions: conditions.conditionsBuilder("data"),
                actions: actions.actionsBuilder("data"),
            });

            console.log(JSON.stringify(engine.conditions));
            console.log(JSON.stringify(engine.actions));

            // AJAX request to send the
            let csrfToken = $('input[name="csrfmiddlewaretoken"]').val();
            let status = document.getElementById("switch-ica-schedule").checked;

            $.ajax({
                url: "/submit-business-rule/", // Replace with your actual server endpoint
                method: "POST",
                headers: {
                    "X-CSRFToken": csrfToken,
                },
                contentType: "application/json",
                data: JSON.stringify({
                    stage: "stage1",
                    conditions: engine.conditions,
                    actions: engine.actions,
                    status: status,
                }),
                success: function (response) {
                    //console.log('Success:', response);
                    // Handle success - maybe show a success message or redirect
                    if (response.status === "success") {
                        window.location.href = "/system-management/";
                    }
                },
                error: function (xhr, status, error) {
                    console.error("Error:", error);
                    // Handle error - show error message to user
                },
            });
        });
    }
    function initializeFormDetail() {
        submitDetail.click(function (e) {
            e.preventDefault();
            var engine_detail = new RuleEngine({
                conditions: conditionsDetail.conditionsBuilder("data"),
                actions: actionsDetail.actionsBuilder("data"),
            });
            console.log(JSON.stringify(engine_detail.conditions));
            console.log(JSON.stringify(engine_detail.actions));

            // AJAX request to send the
            let csrfToken = $('input[name="csrfmiddlewaretoken"]').val();
            let status = document.getElementById("switch-ica-detail").checked;
            $.ajax({
                url: "/submit-business-rule/", // Replace with your actual server endpoint
                method: "POST",
                headers: {
                    "X-CSRFToken": csrfToken,
                },
                contentType: "application/json",
                data: JSON.stringify({
                    stage: "stage2",
                    conditions: engine_detail.conditions,
                    actions: engine_detail.actions,
                    status: status,
                }),
                success: function (response) {
                    //console.log('Success:', response);
                    // Handle success - maybe show a success message or redirect
                    if (response.status === "success") {
                        window.location.href = "/system-management/";
                    }
                },
                error: function (xhr, status, error) {
                    console.error("Error:", error);
                    // Handle error - show error message to user
                },
            });
        });
    }

    function runTest() {
        btnRunTest.click(function (e) {
            e.preventDefault();
            let csrfToken = $('input[name="csrfmiddlewaretoken"]').val();
            let luId = $('select[name="learning_unit"]').val();
            let engine_detail = new RuleEngine({
                conditions: conditionsDetail.conditionsBuilder("data"),
                actions: actionsDetail.actionsBuilder("data"),
            });
            var engine = new RuleEngine({
                conditions: conditions.conditionsBuilder("data"),
                actions: actions.actionsBuilder("data"),
            });
            console.log(JSON.stringify(engine_detail.conditions));
            console.log(JSON.stringify(engine_detail.actions));
            console.log(JSON.stringify(engine.conditions));
            console.log(JSON.stringify(engine.actions));
            console.log("LU ID: " + luId);

            $.ajax({
                url: "/submit-business-rule-test/", // Replace with your actual server endpoint
                method: "POST",
                headers: {
                    "X-CSRFToken": csrfToken,
                },
                contentType: "application/json",
                data: JSON.stringify({
                    conditions_detail: engine_detail.conditions,
                    actions_detail: engine_detail.actions,
                    conditions: engine.conditions,
                    actions: engine.actions,
                    learning_unit_id: luId,
                }),
                success: function (response, status, xhr) {
                    // Try both ways to handle redirect
                    if (
                        response.status === "success" &&
                        response.redirect_url
                    ) {
                        window.location.href = response.redirect_url;
                    }
                },
                error: function (xhr, status, error) {
                    // Handle errors appropriately
                    console.error("Error:", error);
                    if (xhr.status === 302) {
                        // Some servers might send redirect as error
                        const redirectUrl = xhr.getResponseHeader("Location");
                        window.location.href = redirectUrl;
                    }
                },
            });
        });
    }
    $(onReady);
})(jQuery);
