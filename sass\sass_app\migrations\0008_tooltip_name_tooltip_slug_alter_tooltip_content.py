# Generated by Django 5.1 on 2024-11-08 09:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("sass_app", "0007_initial"),
    ]

    def populate_data(apps, schema_editor):
        ToolTip = apps.get_model('sass_app', 'ToolTip')

        ToolTip.objects.filter().delete()

        tooltip = ToolTip.objects.filter(slug='duration').first()
        if not tooltip:
            tooltip,_ = ToolTip.objects.get_or_create(slug='duration')
        tooltip.name = "Duration"
        tooltip.content = """Assessment duration according to weightage:
        Weightage up to  30% : 30 mins- 50 mins
        Weightage of ≥ 30% ~  40% : 1 hr -  1 hr 15 mins
        Weightage of ≥ 40% ~  50% : 1 hr -  1 hr 30 mins"""
        tooltip.save()

        tooltip = ToolTip.objects.filter(slug='moderation-method').first()
        if not tooltip:
            tooltip,_ =ToolTip.objects.get_or_create(slug='moderation-method')
        tooltip.name = "Moderation Method"
        tooltip.content = """Moderation Method according to weightage:
        Weightage up to  30% : 30 mins- 50 mins
        Weightage of ≥ 30% ~  40% : 1 hr -  1 hr 15 mins
        Weightage of ≥ 40% ~  50% : 1 hr -  1 hr 30 mins"""
        tooltip.save()

        tooltip = ToolTip.objects.filter(slug='assessement-type').first()
        if not tooltip:
             tooltip,_ =ToolTip.objects.get_or_create(slug='assessement-type')
        tooltip.name = "Assessement Type"
        tooltip.content = """Assessement Type according to weightage:
        Weightage up to  30% : 30 mins- 50 mins
        Weightage of ≥ 30% ~  40% : 1 hr -  1 hr 15 mins
        Weightage of ≥ 40% ~  50% : 1 hr -  1 hr 30 mins"""
        tooltip.save()

        tooltip = ToolTip.objects.filter(slug='ica-schedule-save-success').first()
        if not tooltip:
             tooltip,_ =ToolTip.objects.get_or_create(slug='ica-schedule-save-success')
        tooltip.name = "ica-schedule-save-success"
        tooltip.content = """Your changes have been saved.
            Please proceed to the ICA details screen to submit your changes.

            If your schedule contains an ICA with digital submission and presentation, please ensure the presentation  do not happen during any blocked weeks (e.g. elearning, term break)."""
        tooltip.save()

        tooltip = ToolTip.objects.filter(slug='support-email').first()
        if not tooltip:
             tooltip,_ =ToolTip.objects.get_or_create(slug='support-email')
        tooltip.name = "support email"
        tooltip.content = """<EMAIL>"""
        tooltip.save()

    operations = [
        
        migrations.AddField(
            model_name="tooltip",
            name="name",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="tooltip",
            name="slug",
            field=models.SlugField(default="", max_length=255, unique=True),
        ),
        migrations.AlterField(
            model_name="tooltip",
            name="content",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.RunPython(populate_data),
        
    ]
