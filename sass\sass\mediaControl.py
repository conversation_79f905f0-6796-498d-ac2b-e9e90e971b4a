import os
import hashlib
import time
from django.contrib.auth.decorators import login_required
from django.conf import settings
from django.http import FileResponse, HttpResponse
import boto3
import logging
from botocore.exceptions import ClientError, NoCredentialsError
# Set up logger
logger = logging.getLogger(__name__)

def get_file_checksum(file_path):
    """Calculate MD5 checksum of a file"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        logger.error(f"Error calculating checksum for {file_path}: {str(e)}")
        return None

def is_file_ready(s3_client, bucket_name, key):
    """Simple check if S3 file is ready for download"""
    try:
        response = s3_client.head_object(Bucket=bucket_name, Key=key)
        content_length = response.get('ContentLength', 0)
        return content_length > 0
    except:
        return False

def is_local_file_ready(file_path):
    """Simple check if local file is ready for download"""
    try:
        if not os.path.exists(file_path):
            return False
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            return False
        # Quick check if file is readable
        with open(file_path, 'rb') as f:
            f.read(1)
        return True
    except:
        return False

@login_required
def serve_media(request, path):
    try:
        # Sanitize path to prevent directory traversal
        path = os.path.normpath(path)
        if path.startswith('/') or '..' in path:
            logger.warning(f"Potential directory traversal attempt: {path}")
            return HttpResponse(status=403)

        if settings.SYSTEM_RUN_LOCATION in ['PROD','CSTACK', 'CSTACK-PROD']:
            # S3 File handling
            try:
                s3_client = boto3.client(
                    's3',
                    region_name=settings.AWS_S3_REGION_NAME
                )

                # Simple file readiness check
                if not is_file_ready(s3_client, settings.AWS_STORAGE_BUCKET_NAME, path):
                    logger.error(f"S3 file not ready: {path}")
                    return HttpResponse(status=409)  # Conflict - file not ready

                # For HEAD requests, just return 200 if file is ready
                if request.method == 'HEAD':
                    return HttpResponse(status=200)

                # Get filename for response headers
                filename = os.path.basename(path)

                # Instead of redirecting, stream the file through Django to avoid CORS issues
                try:
                    response = s3_client.get_object(
                        Bucket=settings.AWS_STORAGE_BUCKET_NAME,
                        Key=path
                    )

                    # Create streaming response
                    file_response = HttpResponse(
                        response['Body'].read(),
                        content_type=response.get('ContentType', 'application/octet-stream')
                    )
                    file_response['Content-Disposition'] = f'attachment; filename="{filename}"'
                    file_response['Content-Length'] = response.get('ContentLength', 0)

                    logger.info(f"Served S3 file: {path} to user: {request.user.username}")
                    return file_response

                except ClientError as download_error:
                    logger.error(f"Failed to download S3 file {path}: {str(download_error)}")
                    return HttpResponse(status=404)

            except NoCredentialsError:
                logger.error("AWS credentials not configured properly")
                return HttpResponse(status=500)
            except ClientError as e:
                logger.error(f"S3 error for file {path}: {str(e)}")
                return HttpResponse(status=404)
            except Exception as e:
                logger.error(f"S3 error for file {path}: {str(e)}")
                return HttpResponse(status=500)
        else:
            # Local file handling
            file_path = os.path.join(settings.MEDIA_ROOT, path)

            # Security check
            if not file_path.startswith(settings.MEDIA_ROOT):
                logger.warning(f"Potential directory traversal attempt: {file_path}")
                return HttpResponse(status=403)

            # Simple file readiness check
            if not is_local_file_ready(file_path):
                logger.error(f"Local file not ready: {file_path}")
                return HttpResponse(status=409)  # Conflict - file not ready

            # For HEAD requests, just return 200 if file is ready
            if request.method == 'HEAD':
                return HttpResponse(status=200)

            try:
                # Serve the file for GET requests
                file_size = os.path.getsize(file_path)
                checksum = get_file_checksum(file_path)

                logger.info(f"Serving local file: {path} (size: {file_size}) to user: {request.user.username}")

                file_handle = open(file_path, 'rb')
                response = FileResponse(
                    file_handle,
                    as_attachment=True,
                    filename=os.path.basename(path)
                )
                response['Content-Length'] = file_size
                if checksum:
                    response['Content-MD5'] = checksum
                return response

            except IOError as e:
                logger.error(f"Error reading file {file_path}: {str(e)}")
                return HttpResponse(status=500)

        return HttpResponse(status=404)

    except Exception as e:
        logger.error(f"Unexpected error serving media file {path}: {str(e)}")
        return HttpResponse(status=500)