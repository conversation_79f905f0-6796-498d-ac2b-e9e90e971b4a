from django.db.models import Q
from system_management.models import *
from django.db.utils import OperationalError

def custom_list(request):

    try:
        SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
        if SYSTEM_CURRENT_YEAR == None:
            # Provide a default value if no record exists
            return {'SYSTEM_CURRENT_YEAR': timezone.now().year}
        return {'SYSTEM_CURRENT_YEAR': SYSTEM_CURRENT_YEAR}
    except OperationalError:
        # Provide a fallback value when database connection fails
        return {'SYSTEM_CURRENT_YEAR': timezone.now().year}
