# Generated by Django 5.1 on 2024-12-31 12:48

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("schedule_assessment", "0023_alter_scheduleassessment_updated_at"),
        # ("system_management", "0051_rename_lucategory_academicweek_lutype"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ScheduleAssessmentTest",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("current_year", models.IntegerField(default=2024)),
                ("current_semester", models.IntegerField(default=1)),
                ("ica_number", models.IntegerField()),
                ("week", models.IntegerField(blank=True, null=True)),
                (
                    "weightage",
                    models.IntegerField(
                        default=0,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                (
                    "over_two_weeks",
                    models.CharField(
                        choices=[("no", "No"), ("yes", "Yes")],
                        default="no",
                        max_length=10,
                    ),
                ),
                (
                    "assessment_task",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "duration",
                    models.IntegerField(
                        blank=True, help_text="Duration in minutes", null=True
                    ),
                ),
                (
                    "assessment_type",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "digital_submission",
                    models.CharField(
                        choices=[
                            ("no", "No"),
                            (
                                "yes-in-the-same-week-as-digital-submission",
                                "Yes, in the same week as digital submission",
                            ),
                            (
                                "yes-in-the-following-week-after-digital-submission",
                                "Yes, in the following week after digital submission",
                            ),
                        ],
                        default="no",
                        max_length=255,
                    ),
                ),
                ("descriptions", models.TextField(blank=True, null=True)),
                ("topics", models.TextField(blank=True, null=True)),
                ("reasons_change", models.TextField(blank=True, null=True)),
                ("broken_rules", models.JSONField(blank=True, null=True)),
                ("broken_rules_stage1", models.JSONField(blank=True, null=True)),
                ("broken_rules_stage2", models.JSONField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "original_lu_code",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "original_assessment_slug",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "original_moderation_slug",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "assessment_method",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="system_management.assessment",
                    ),
                ),
                (
                    "lu",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="system_management.learningunit",
                    ),
                ),
                (
                    "moderation_method",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="system_management.moderationmethod",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
