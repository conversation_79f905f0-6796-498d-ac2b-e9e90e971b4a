# Generated by Django 5.1 on 2024-10-25 03:37

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "system_management",
            "0024_academicweek_assessment_course_moderationmethod_and_more",
        ),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name="learningunit",
            name="status",
        ),
        migrations.RemoveField(
            model_name="learningunit",
            name="updated_by",
        ),
        migrations.RemoveField(
            model_name="learningunit",
            name="weightage",
        ),
        migrations.CreateModel(
            name="StatusLearningUnit",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "weightage",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("saved", "Saved"),
                            ("approved", "Approved"),
                            ("submitted", "Submitted"),
                            ("rework", "Rework"),
                        ],
                        default="pending",
                        max_length=10,
                    ),
                ),
                (
                    "current_year",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="system_management.systemcurrentyear",
                    ),
                ),
                (
                    "lu",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="system_management.learningunit",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
