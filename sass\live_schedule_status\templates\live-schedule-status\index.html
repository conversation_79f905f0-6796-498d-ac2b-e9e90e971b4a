{% extends 'base.html' %}
{% block title %}Live Schedule Status{% endblock title %}
{% load static %}
{% load custom_tags %}

{% block content %}
<div class="row ">
   <div class="col-12">
        <div class="breadcrumb">
            <a href="/">Back</a> <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M7.07586 4.51043C7.18836 4.39807 7.34086 4.33496 7.49986 4.33496C7.65886 4.33496 7.81136 4.39807 7.92386 4.51043L11.3239 7.91043C11.4362 8.02293 11.4993 8.17543 11.4993 8.33443C11.4993 8.49343 11.4362 8.64593 11.3239 8.75843L7.92386 12.1584C7.81012 12.2644 7.65969 12.3221 7.50424 12.3194C7.3488 12.3166 7.2005 12.2537 7.09057 12.1437C6.98064 12.0338 6.91766 11.8855 6.91492 11.7301C6.91218 11.5746 6.96988 11.4242 7.07586 11.3104L10.0519 8.33443L7.07586 5.35843C6.9635 5.24593 6.90039 5.09343 6.90039 4.93443C6.90039 4.77543 6.9635 4.62293 7.07586 4.51043Z" fill="#BBBBBB"/>
                </svg>
            <span>Live schedule status</span>
        </div> 
        <div class="card card-shadow card-live-schedule">
            <h2 class="title-section">Live Schedule Status </h2>

            <p class="fw-bold"> Acad Year {{SYSTEM_CURRENT_YEAR.current_year}}, Semester {{SYSTEM_CURRENT_YEAR.current_semester}} </p>  
            
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="department">Department</label>
                        <div class="wrapper-select">
                            <form>
                                <select id="department" name="department" class="form-control" title="department">

                                    <option value="">Select department</option>

                                    {% for department in departments %}
                                    <option value="{{department.code}}" {% if department.code == department_code %} selected {% endif %}>{{department.name}}</option>
                                    {% endfor %}
                                
                                </select>
                            </form>
                            <script>
                                document.getElementById('department').addEventListener('change', function() {
                                    this.form.submit();
                                });
                            </script>
                        </div>
                    </div>
               </div>
            </div>
           
            <div class="row mt-2">
                <div class="col-lg-8">                   
                    <div class="table-wrapper">
                        <table style="width: 98%;" class="mb-0 table table-live-schedule table-lu-list table-borderless">
                            <thead class="bg-gray text-black sticky-top">
                                <tr>
                                    <th>LU CODE</th>
                                    <th>LU DESC</th>
                                    <th>LU TYPE</th>
                                    <th>EXAMINABLE</th>
                                    <th>YEAR</th>
                                    <th>STATUS</th>
                                    <th>ACTIVITY LOG</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for learning_unit in learning_units %}
                                    {% with status_unit=learning_unit|get_status:SYSTEM_CURRENT_YEAR %}

                                    <div class="d-none" 
                                        id="course-{{learning_unit.id}}"
                                        hx-get="{% url 'live-schedule:getCourse' %}" 
                                        hx-target=".body-course" 
                                        hx-indicator=".loading-course" 
                                        hx-swap="innerHTML" 
                                        hx-trigger='click'
                                        hx-vals='{"details-type": "course","SYSTEM_CURRENT_YEAR":"{{SYSTEM_CURRENT_YEAR.id}}","learning_unit":"{{learning_unit.id}}" }' 
                                        
                                    ></div>
                                    <div class="d-none" 
                                        id="details-{{learning_unit.id}}"
                                        hx-get="{% url 'live-schedule:getCourse' %}" 
                                        hx-target=".live-schedule-details" 
                                        hx-vals='{"details-type": "details","SYSTEM_CURRENT_YEAR":"{{SYSTEM_CURRENT_YEAR.id}}","learning_unit":"{{learning_unit.id}}"}' 
                                        hx-swap="innerHTML" 
                                        hx-indicator=".loading-details"></div>
                                    <tr>
                                        <td width="20%">
                                            <div class="cursor-pointer" onclick='getCourse("{{learning_unit.id}}"),getDetails("{{learning_unit.id}}"),setLearningUnitToChangeStatusForm("{{learning_unit.id}}")'>
                                                {{learning_unit.code}}
                                            </div>
                                        </td>
                                        <td>{{learning_unit.name}}</td>
                                        <td>{{learning_unit.lu_type}}</td>
                                        <td>{% if learning_unit.is_examinable %} {{learning_unit.get_is_examinable_display}} {% endif %}</td>
                                        <td>{{ learning_unit|get_year_of_study|join:", " }}</td>
                                        <td>{% if status_unit.status %} {{status_unit.get_status_display|title}} {% else %} Pending {% endif %}</td>
                                        <td>
                                            <button type="button" class="show-popup btn-text p-0" data-popup="popup-log"
                                            
                                            hx-get="{% url 'sass_app:getActivityLog' learning_unit.id %}" 
                                            hx-target="#activity-log" 
                                            hx-swap="innerHTML" 
                                            
                                            >See log</button>
                                        </td>
                                    </tr>
                                    {% endwith %}
                                {% endfor %}
                                
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="table-responsive wrapper-table-live wrapper-table-course mt-4 mt-lg-0 " >
                        
                            <div class="table-wrapper">
                                <table style="width: 96%" class="mb-0 table table-live-schedule table-borderless">
                                    <thead class="bg-gray text-black sticky-top">
                                        <tr>
                                            <th>COURSE</th>                                   
                                            <th class="text-end">YEAR</th>                                    
                                        </tr>
                                    </thead>
        
                                    <div class="d-none get-course" 
                                        hx-get="{% url 'live-schedule:getCourse' %}" 
                                        hx-target=".body-course" 
                                        hx-indicator=".loading-course" 
                                        hx-swap="innerHTML" 
                                        hx-trigger='load once'
                                        hx-vals='{"details-type": "course","SYSTEM_CURRENT_YEAR":"{{SYSTEM_CURRENT_YEAR.id}}" }' 
                                        ></div>
        
                                    <tbody class="body-course"></tbody>
        
                                </table>
                            </div>
                        
          
                        <div class="loading htmx-indicator loading-course loading-absolute">
                            <img src="{% static 'img/loading.gif' %}" alt="loading">
                        </div>

                    </div>
                </div>
            </div>
            
            
            <div id="message-specific-lu" role="alert" class="mt-4 mb-0 fade d-flex align-items-center alert alert-info show sgds d-none">
                
                <div class="d-flex align-items-center justify-content-start gap-2">
                    <img src="{% static 'img/icon-info-circle.svg' %}" alt="Info"/>
                    
                    <div id="message-specific-lu-message" >Information message for a specific LU</div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <h6 class="fw-bold lu-code__live-schedule-detail text-primary"></h6>
                    <div class="wrapper-table-live mb-4">
                        <div class="wrapper-table-live-schedule-scroll">
                            <table class="mb-0 table table-borderless table-live-schedule">
                                <thead class="bg-gray text-black sticky-top">                            
                                    <tr>
                                        <th>ASSM. TASK</th>
                                        <th>WEEK NO. </th>
                                        <th>ASSM. METHOD</th>
                                        <th>WEIGHTAGE</th>
                                        <th>DURATION</th>
                                        <th>Over 2 WEEKS</th>
                                        <th>ASSM. TYPE</th>
                                        <th>DESC. OF ASSM. <br />INSTRUMENT & FORMAT</th>
                                        <th>MODERATION <br/> METHOD</th>
                                        <th>TOPICS & LU <br />LEARNING OUTCOME</th>
                                        <th>INVOLVE DIGITAL SUBMISSION & SYNCHRONOUS PRESENTATION</th>
                                        <th>REASON FOR CHANGE IN ASSM</th>
                                    </tr>
                                </thead>
                                
                                <tbody class="live-schedule-details"></tbody>
    
                            </table>
                        </div>
                            <div class="text-center empty-schedule-details">Click on the LU Code to see the ICA detail</div>
                        <div class="loading loading-details htmx-indicator loading-absolute">
                            <img src="{% static 'img/loading.gif' %}" alt="loading">
                        </div>
                    </div>   
                </div>
            </div>

        </div>
        
        {% if permission.update %}
            <button type="button" class="btn btn-primary show-popup btn-change-status d-none mt-4">Change status</button>     
        {% endif %}

        <c-popup class="popup-wide" buttonleft>
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0">Change Status</h2>
                <button class="btn-text close-popup" type="button"><img src="{% static 'img/icon-close.svg' %}" alt="close"></button>
            </div>

            <form action="{% url 'live-schedule:status' %}" method="POST">
                {% csrf_token %}
                
                <div id="change-status-learning_unit" class="d-none"></div>

                <input class="d-none" name="department" value="{{department_code}}"></input>
                <div class="form-group my-4">
                    <label for="status" class="text-start w-100">Change status to</label>
                    <div class="wrapper-select">
                        <select name="status" id="status" class="form-control">
                            <option value="">Choose status</option>

                            <option value="submitted">Submitted</option>
                            <option value="approved">Approved</option>
                            <option value="pending">Pending</option>
                            <option value="rework">Rework</option>
                        
                        </select>
                    </div>
                </div>   
                
                <div class="d-flex align-items-center gap-3 justify-content-left mt-4">
                    <button type="submit" class="close-popup btn btn-primary">Change</button>
                    <button type="button" class="btn-text text-danger close-popup">Cancel</button>
                </div>
            </form>
        </c-popup>            
   </div>   
</div>

<c-popup class="popup-log" buttonLeft>    
    <div class="d-flex justify-content-between align-items-center">
        <h2 class="mb-0">Activity Log</h2>
        <button class="btn-text close-popup" type="button"><img src="{% static 'img/icon-close.svg' %}" alt="close"></button>
    </div>

    <div id="activity-log"></div>

</c-popup>  


<div class="popup popup-confirm-status popup-wide {% if not success_message %} d-none {% endif %}">
    <div class="backdrop"></div>
    <div class="content-popup content-popup-upload">
        <h2>Success!</h2>                             
        <img src="{% static 'img/sassy-happy.png' %}" alt="sassy happy">
        <p>Change status was successful!</p>            
        <button class="btn btn-primary close-popup mt-4">Okay!</button>
    </div>
</div>     

    
{% endblock content %}
{% block script %}
    <script>
        
         function getCourse(learning_unit_id) {
            $(`#course-${learning_unit_id}`).click();
            $('.empty-schedule-details').addClass('d-none')
            $('.btn-change-status').removeClass('d-none')
        }
        function getDetails(learning_unit_id) {
            $(`#details-${learning_unit_id}`).click();
            $('.empty-schedule-details').addClass('d-none')
            $('.btn-change-status').removeClass('d-none')
        }
        function setLearningUnitToChangeStatusForm(learning_unit_id) {

            var container = document.getElementById('change-status-learning_unit');
    
            // Create input element
            var input = document.createElement('input');
            input.type = 'hidden';  // Since it's likely meant to be hidden
            input.name = 'learning_unit';
            input.value = learning_unit_id;
            
            // Clear previous content if any
            container.innerHTML = '';
            
            // Append the input to the container
            container.appendChild(input);
        }
    </script>
{% endblock script %}