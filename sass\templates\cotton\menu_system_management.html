<ul class="menu-system-management">    
    <li hx-get="{% url 'system_management:userManagement' %}" hx-swap="innerHTML" hx-target=".container-content" hx-trigger="click" {% if current_page == "userManagement" %} class="active" {% endif  %} >User Management</li>
    <li hx-get="{% url 'system_management:luStatus' %}" hx-swap="innerHTML" hx-target=".container-content" hx-trigger="click" {% if current_page == "luStatus" %} class="active" {% endif  %}>LU Status</li>
    <li hx-get="{% url 'system_management:setCurrent' %}" hx-swap="innerHTML" hx-target=".container-content" hx-trigger="click" {% if current_page == "setCurrent" %} class="active" {% endif  %}>Set Current Year & Edit Mode</li>
    <li hx-get="{% url 'system_management:form_file_view' %}" hx-swap="innerHTML" hx-target=".container-content" hx-trigger="click" {% if current_page == "uploadFile" %} class="active" {% endif  %}>Upload File</li>
    <li hx-get="{% url 'system_management:activityLog' %}" hx-swap="innerHTML" hx-target=".container-content" hx-trigger="click" {% if current_page == "activityLog" %} class="active" {% endif  %}>Activity Log</li>
    <li hx-get="{% url 'system_management:roleManagement' %}" hx-swap="innerHTML" hx-target=".container-content" hx-trigger="click" {% if current_page == "roleManagement" %} class="active" {% endif  %}>Role Management</li>
    <li hx-get="{% url 'system_management:ruleConfiguration' %}" hx-swap="innerHTML" hx-target=".container-content" hx-trigger="click" {% if current_page == "ruleConfiguration" %} class="active rule-config" {% endif  %}>Rule Configuration</li>
</ul>