
from datetime import datetime
from django.db.models import <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
from django.utils.dateparse import parse_datetime
import pandas as pd
from django.db import transaction
import re
from django.shortcuts import render
from system_management.models import LearningUnit, AcademicWeek, Assessment, ModerationMethod,CourseLuMap, SystemCurrentYear
from django.shortcuts import redirect
from django.contrib import messages
from functools import wraps
from datetime import datetime, timedelta
from django.db.models import Subquery, OuterRef
from system_management.models import Permission
from django.contrib.auth.models import User

import threading
from django.core.mail import EmailMessage
from io import BytesIO
from sass_app.email import *

from django.core.validators import validate_email
from django.core.exceptions import ValidationError

from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import Count
def is_valid_email(email):
    try:
        validate_email(email)
        return True
    except ValidationError:
        return False

def check_access(page=None):
    """
    Custom decorator to check if user has specific permission.
    If not, redirects to specified URL.

    Args:
        permission_name (str): The permission to check (e.g., 'app.add_model')
        redirect_url (str): The URL name to redirect to if permission check fails

    Usage:
        @check_access('app.add_post')
        def my_view(request):
            # View logic here
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Check if user is authenticated and has permission
            if not request.user.is_authenticated:
                messages.error(request, 'Please login to access this page.')
                return redirect('sass_app:main')

            if not permission_user_check(request):
                messages.error(
                    request,
                    'You do not have permission to access this page.'
                )
                return render(request, '403.html')

            if page:
                check_ = check_permission(request.user, page)
                if check_:
                    if not check_.view:
                        return render(request, '403.html')

            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def permission_user_check(request):
    if request.user.role:
        if request.user.role.status in ["suspended", "inactive"]:
            return False

    return True


def sending_email_if_timeout(timeout_seconds=20):
    def decorator(func):
        @wraps(func)
        def wrapper(request, *args, **kwargs):

            result = []

            request_path = request.path

            def target():
                try:
                    excel_file, _ = func(request, *args, **kwargs)
                    result.append(excel_file)
                except Exception as e:
                    result.append(e)

            thread = threading.Thread(target=target)
            thread.start()
            thread.join(timeout_seconds)

            if thread.is_alive():
                # Switch to async if timeout
                def async_process():
                    try:
                        _, data = func(request, *args, **kwargs)
                        email_msg = EmailMessage(
                            subject=data['email_subject'],
                            body=data['email_body'],
                            from_email=settings.DEFAULT_FROM_EMAIL,
                            to=[request.user.email]
                        )

                        excel_file = BytesIO()
                        data['wb'].save(excel_file)
                        excel_file.seek(0)
                        email_msg.attach(
                            data['file_name'],
                            excel_file.getvalue(),
                            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        )

                        # Convert email to raw string
                        raw_message = email_msg.message().as_string()
                        # Send using Boto3 SES
                        ses_service = SESEmailService()
                        response = ses_service.client.send_raw_email(
                            Source=settings.DEFAULT_FROM_EMAIL,
                            Destinations=[request.user.email],
                            RawMessage={'Data': raw_message}
                        )

                    except Exception as e:
                        payload={
                            'subject':'Process Download Failed',
                            'message': f'Error: {str(e)}',
                            'destination_email':[request.user.email]
                            }

                        send_text_email_bg_jobs(payload=payload)

                background = threading.Thread(target=async_process)
                background.daemon = True
                background.start()
                
                if 'report' in request_path:
                    return redirect('report:index')
                else:
                    return redirect('system_management:systemManagement')

            if result:
                if isinstance(result[0], Exception):
                    raise result[0]
                return result[0]

            if 'report' in request_path:
                return redirect('report:index')
            else:
                return redirect('system_management:systemManagement')
            
        return wrapper
    return decorator


def parse_date(date_string):
    try:
        # First, try parsing with Django's parse_datetime
        parsed_date = parse_datetime(date_string)
        if parsed_date:
            return parsed_date

        # If that fails, try parsing common formats
        for fmt in ('%Y/%m/%d', '%Y-%m-%d', '%Y/%m/%d %H:%M:%S', '%Y-%m-%d %H:%M:%S'):
            try:
                return datetime.strptime(date_string, fmt)
            except ValueError:
                pass

        # If all parsing attempts fail, raise an exception
        raise ValueError(f"Unable to parse date: {date_string}")
    except Exception as e:
        raise ValueError(f"Error parsing date: {e}")


class FormatDateTime(Func):
    function = 'TO_CHAR'
    template = "%(function)s(%(expressions)s, 'DD-MM-YYYY HH24:MI:SS')"


class GetDisplayValue(Func):
    def __init__(self, column, model, **extra):
        self.column = column
        self.model = model
        super().__init__(column, output_field=CharField(), **extra)

    def as_sql(self, compiler, connection):
        choices = dict(getattr(self.model, self.column).field.choices)

        when_statements = ' '.join([
            f"WHEN '{key}' THEN '{value}'" for key, value in choices.items()
        ])
        template = f"CASE CAST({self.column} AS VARCHAR) {
            when_statements} ELSE CAST({self.column} AS VARCHAR) END"

        self.template = template
        return super().as_sql(compiler, connection)


def excel_reader(file_object):
    df = None
    with file_object.open() as f:
        df = pd.read_excel(f, dtype=str)
    return df


def bulk_delete_and_create(YourModel, records):
    """
    Deletes all existing records and creates new ones from the input.

    Args:
        YourModel: Django model class
        records: List of dictionaries containing the data to create
    """
    with transaction.atomic():
        # Delete all existing records
        deleted_count = YourModel.objects.all().delete()[0]

        # Create new records
        new_records = [YourModel(**record) for record in records]
        YourModel.objects.bulk_create(new_records)

        print(f"Deleted {deleted_count} existing records and created {
              len(new_records)} new records.")
        return True


def bulk_create_or_update(YourModel, records, key_fields):
    with transaction.atomic():

        # Get existing records
        existing_records = {
            tuple(getattr(obj, field) for field in key_fields): obj
            for obj in YourModel.objects.filter(**{
                f"{field}__in": [record[field] for record in records]
                for field in key_fields
            })
        }

        to_create = []
        to_update = []

        for record in records:
            record_key = tuple(record[field] for field in key_fields)
            if record_key in existing_records:
                # Update existing record
                obj = existing_records[record_key]
                for key, value in record.items():
                    setattr(obj, key, value)
                to_update.append(obj)
            else:
                # Create new record
                to_create.append(YourModel(**record))

        # Bulk create new records only if there are any to create
        if to_create:
            YourModel.objects.bulk_create(to_create)

        # Bulk update existing records only if there are any to update
        if to_update:
            update_fields = [field.name for field in YourModel._meta.fields
                             if not field.primary_key and field.name not in key_fields]
            YourModel.objects.bulk_update(
                to_update,
                fields=update_fields
            )

        print(f"Created {len(to_create)} new records and updated {
              len(to_update)} existing records.")
        return True

    return False


def transform_to_json_column(df):
    """
    Transform week columns into a single column with JSON structure

    Parameters:
    df (pandas.DataFrame): DataFrame with Week{n} headers

    Returns:
    pandas.DataFrame: New DataFrame with single 'week' column containing JSON
    """
    # First, identify week columns and their status

    week_columns = [col for col in df.columns
                    if re.match(r'week\s*\d+', col.lower().strip())]

    def create_row_json(row):
        status_dict = {}
        for col in week_columns:
            week_num = int(re.search(r'week\s*(\d+)', col.lower()).group(1))
            if pd.notna(row[col]) and row[col] != '':
                status_dict[str(week_num)] = row[col]

        return {
            "start": min([int(re.search(r'week\s*(\d+)', col.lower()).group(1))
                         for col in week_columns]),
            "end": max([int(re.search(r'week\s*(\d+)', col.lower()).group(1))
                       for col in week_columns]),
            "status": status_dict
        }

    # Apply the JSON creation to each row
    result_df = pd.DataFrame({
        'week': [create_row_json(row) for _, row in df.iterrows()]
    })

    return result_df


def get_date_by_week(week_number, learning_unit: LearningUnit = None, academicWeek: AcademicWeek = None):
    target_date = 'Not Reserved'
    if week_number:

        if week_number:
            week_number = int(week_number)

        if not academicWeek:
            academicWeek = AcademicWeek.objects.filter(
                lutype=learning_unit.lu_type, coursetype=learning_unit.course_type).first()

        if academicWeek.startdate:
            start_date = academicWeek.startdate
        else:
            start_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)

        weeks_to_add = week_number - 1

        # Calculate the target date
        target_date = start_date + timedelta(weeks=weeks_to_add)

    return target_date


class RelationshipReconnector:
    @classmethod
    def reconnect_lu(cls, model_class):
        model_class.objects.filter(
            lu__isnull=True,
            original_lu_code__isnull=False
        ).update(
            lu=Subquery(
                LearningUnit.objects.filter(
                    code=OuterRef('original_lu_code')
                ).values('id')[:1]
            )
        )

    @classmethod
    def reconnect_assessment(cls, model_class):
        model_class.objects.filter(
            assessment_method__isnull=True,
            original_assessment_slug__isnull=False
        ).update(
            assessment_method=Subquery(
                Assessment.objects.filter(
                    slug=OuterRef('original_assessment_slug')
                ).values('id')[:1]
            )
        )

    @classmethod
    def reconnect_moderation(cls, model_class):
        model_class.objects.filter(
            moderation_method__isnull=True,
            original_moderation_slug__isnull=False
        ).update(
            moderation_method=Subquery(
                ModerationMethod.objects.filter(
                    slug=OuterRef('original_moderation_slug')
                ).values('id')[:1]
            )
        )

    @classmethod
    def reconnect_all(cls, model_class):
        cls.reconnect_lu(model_class)
        cls.reconnect_assessment(model_class)
        cls.reconnect_moderation(model_class)


def separate_string_and_number(text):
    letters = ''.join(char for char in text if char.isalpha())
    numbers = ''.join(char for char in text if char.isdigit())
    return letters, numbers  # Returns ('ASSN', '1')


def check_permission(user: User, page):
    return Permission.objects.filter(type=user.role, page=page).first()

def get_department(object_repr):
    try:
        luname = ""
        match = re.search(r'Lu\s+(.*?)-', object_repr)
        if match:
            luname = match.group(1).strip()
        else:
            luname = object_repr

        lu = LearningUnit.objects.filter(name=f'{luname}').first()
        if lu:
            return lu.department
        return ''
    except (TypeError, AttributeError):
        return ''
    
def get_lu_code(object_repr):
    try:
        luname = ""
        match = re.search(r'Lu\s+(.*?)-', object_repr)
        if match:
            luname = match.group(1).strip()
        else:
            luname = object_repr

        lu = LearningUnit.objects.filter(name=f'{luname}').first()
        if lu:
            return lu.code
        return ''
    except (TypeError, AttributeError):
        return ''
    
def find_lu_course_group(lu_code):
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()

    aggregated_data = CourseLuMap.objects.filter(
        academic_year=SYSTEM_CURRENT_YEAR.current_year,
        semester=SYSTEM_CURRENT_YEAR.current_semester
    ).values(
        'course_code__code',  # Group by course code
        'year_study'          # Group by year of study
    ).annotate(
        lu_codes=ArrayAgg('lu__code')  # Aggregate LU codes into an array
    ).filter(
        lu_codes__contains=[lu_code]  # This is the key part - filter on the array
    ).order_by('course_code__code', 'year_study')
    
    # Example of how to use the result
    # lu_codes=[]
    # for item in aggregated_data:
    #     lu_codes.extend(item['lu_codes'])
    #     # print(f"{lu_code} Course: {item['course_code__code']} | Year: {item['year_study']} | LU Codes: {item['lu_codes']}")
    # if lu_codes:lu_codes=list(set(lu_codes))

    return aggregated_data