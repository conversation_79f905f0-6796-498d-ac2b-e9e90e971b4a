
from schedule_assessment.models import *
from system_management.models import *

# Backup
# Update LU codes
# records = ScheduleAssessment.objects.filter(
#         lu__isnull=False,  # Has LU connection
#     )
# print("records: ", records)
# for record in records:
#     record.original_lu_code = record.lu.code
#     record.save()

# records = ScheduleAssessment.objects.filter(
#         assessment_method__isnull=False,
#     )
# print("records: ", records)
# for record in records:
#     record.original_assessment_slug = record.assessment_method.slug
#     record.save()

# records = ScheduleAssessment.objects.filter(
#         moderation_method__isnull=False,
#     )
# for record in records:
#     record.original_moderation_slug = record.moderation_method.slug
#     record.save()


# records = StatusLearningUnit.objects.filter(
#         lu__isnull=False,  # Has LU connection
#     )
# print("records: ", records)
# for record in records:
#     record.original_lu_code = record.lu.code
#     record.save()





