{% load static %}           
{% load custom_tags %}
<div class="card card-shadow card-padding mt-5">
    
    <div class="tab-ica {% if test_mode %}tab-ica-test{% endif %}">
        
        <a class="btn-ica-schedule" href="{% url 'schedule_assessment:icaSchedule' learning_unit.id %}?redirect_to=ica-schedule{% if test_mode %}&test_mode=true{% endif %}">ICA Schedule</a> 
        <a href="{% url 'schedule_assessment:icaSchedule' learning_unit.id %}?redirect_to=ica-detail{% if test_mode %}&test_mode=true{% endif %}" class="active btn-ica-detail">ICA Detail</a>
    </div>       

    {% if test_mode %}
        {% get_total_weightage_test learning_unit as total_weightage %}
    {% else %}
        {% get_total_weightage learning_unit as total_weightage %}
    {% endif %}
    
    <div class="container-ica">                
        {% if total_weightage == 100 and schedule_assessments|count_none_weeks == 0 %}
        <form id="ica-detail-form" action="{% url 'schedule_assessment:update_ica_detail' %}" method="POST">
            {% csrf_token %}
            <input hidden name="learning_unit_id" value="{{learning_unit.id}}"></input> 
            {% if test_mode %}<input hidden name="test_mode" value="{{test_mode}}"></input> {% endif %}

            {% for schedule_assessment in schedule_assessments %}                    
            <input hidden name="schedule_assessment" value="{{schedule_assessment.id}}"></input> 
            <div class="card item-ica-detail-card {{ schedule_assessment.assessment_task|slice:":-2"|lower }}">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="title-card {% if test_mode %}title-card-test{% endif %}">ICA 
                        <span class="number-ica"></span>
                        <span class="value-weightage">({{schedule_assessment.weightage}}%)</span></h2>
                </div>
                <div class="outer-ica-schedule mb-3">
                    <div class="item-schedule">
                        <p class="text-gray">Week</p>
                        <p class="fw-bold">{%if schedule_assessment.get_date_from_week.week %}{{schedule_assessment.get_date_from_week.week}} ({{schedule_assessment.get_date_from_week.date}}){% else %}{{schedule_assessment.get_date_from_week.date}}{% endif %} </p>
                    </div>
                    <div class="item-schedule">
                        <p class="text-gray">Assm. Weightage</p>
                        <p class="fw-bold">{{schedule_assessment.weightage}}% </p>
                    </div>
                    <div class="item-schedule">
                        <p class="text-gray">Over 2 weeks</p>
                        <p class="fw-bold">{{schedule_assessment.over_two_weeks|capfirst}} </p>
                    </div>
                    <div class="item-schedule">
                        <p class="text-gray">Assm. Method</p>
                        <p class="fw-bold">{{schedule_assessment.assessment_method.method}}</p>
                    </div>
                    <div class="item-schedule">
                        <p class="text-gray">Assm. Task</p>
                        <p class="fw-bold">{{schedule_assessment.assessment_task|upper}}</p>
                    </div>
                </div>
            

                <div class="row">
                    <div class="col-lg-4">
                        <div class="form-group">
                            
                            <label for="Duration2" class="has-tooltips">
                                Duration 
                                <div class="tooltips">
                                    <img src="{% static 'img/icon-info.svg' %}" alt="info">
                                    <div class="tooltips-content">
                                        
                                            {% get_tooltip 'duration' as tooltips %}
                                            
                                            <p>{{tooltips|clean_text}}</p>
                                    </div>
                                </div>
                            </label>
                           
                            <div class="form-group form-input-group">                                
                                <input type="number" title="Input" name="duration" class="form-control {% if not is_edit and not is_edit_ica_detail or not permission.update %}disabled{% endif %}"  placeholder="duration"
                                    {% if schedule_assessment.duration %}value={{schedule_assessment.duration}}{% else %} 0 {% endif %}
                                    /> 
                                <span class="input-group-text">Min</span>                                
                            </div>
                            
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label for="moderation-method" class="has-tooltips">Moderation method 
                                <div class="tooltips">
                                    <img src="{% static 'img/icon-info.svg' %}" alt="info">
                                    <div class="tooltips-content">

                                        {% get_tooltip 'moderation-method' as tooltips %}
                                        
                                        <p>{{tooltips|clean_text}}</p>

                                    </div>
                                </div>
                            </label>
                            <div class="wrapper-select">
                                <select name="moderation-method" title="Input" class="form-control {% if not is_edit and not is_edit_ica_detail or not permission.update %}disabled{% endif %}">
                                    
                                    <option value="">choose method</option>
                                    {% for moderation_method in moderation_methods %}
                                        <option value={{moderation_method.id}} {% if moderation_method == schedule_assessment.moderation_method %} selected {% endif %}>{{moderation_method.description}}</option>
                                    {% endfor %}

                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label for="Duration" class="has-tooltips">Assessment type 
                                <div class="tooltips">
                                    <img src="{% static 'img/icon-info.svg' %}" alt="info">
                                    <div class="tooltips-content align-right">

                                        {% get_tooltip 'assessement-type' as tooltips %}
                                        
                                        <p>{{tooltips|clean_text}}</p>

                                    </div>
                                </div>
                            </label>
                            <div class="wrapper-select">
                                <select name="assessement-type" title="Input" class="form-control {% if not is_edit and not is_edit_ica_detail or not permission.update %}disabled{% endif %}">
                                    <option value="">--</option>
                                    <option value="Individual" {% if 'Individual' == schedule_assessment.assessment_type %} selected {% endif %}>Individual</option>
                                    <option value="Group" {% if 'Group' == schedule_assessment.assessment_type %} selected {% endif %}>Group</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label for="digital-submission" class="has-tooltips">Is there synchronous presentation after digital submission?
                                <div class="tooltips">
                                    <img src="{% static 'img/icon-info.svg' %}" alt="info">
                                    <div class="tooltips-content align-right">

                                        {% get_tooltip 'ica-syncronous' as tooltips %}
                                        
                                        <p>{{tooltips|clean_text}}</p>

                                    </div>
                                </div>
                            </label>
                            <div class="wrapper-select">
                                <select name="digital-submission" title="Input" class="form-control {% if not is_edit and not is_edit_ica_detail or not permission.update %}disabled{% endif %}">                                
                                    <option value="" {% if schedule_assessment.digital_submission == '' %}selected{% endif %} >--</option>
                                    <option value="no"  {% if schedule_assessment.digital_submission == 'no' %} selected {% endif %}>No</option>
                                    <option value="yes-in-the-same-week-as-digital-submission"  {% if schedule_assessment.digital_submission == 'yes-in-the-same-week-as-digital-submission' %} selected {% endif %}>Yes, in the same week as digital submission</option>
                                    <option value="yes-in-the-following-week-after-digital-submission"  {% if schedule_assessment.digital_submission == 'yes-in-the-following-week-after-digital-submission' %} selected {% endif %}>Yes, in the following week after digital submission</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="description-of-assessment" class="has-tooltips">Description of assessment instruments & format
                                <div class="tooltips">
                                    <img src="{% static 'img/icon-info.svg' %}" alt="info">
                                    <div class="tooltips-content align-right">

                                        {% get_tooltip 'ica-descriptions' as tooltips %}
                                        
                                        <p>{{tooltips|clean_text}}</p>

                                    </div>
                                </div>
                            </label>
                            <textarea name="description-of-assessment" title="Input" class="form-control {% if not is_edit and not is_edit_ica_detail or not permission.update %}disabled{% endif %}" id="description-of-assessment" placeholder="Type here...">{%if schedule_assessment.descriptions %}{{schedule_assessment.descriptions}}{% endif %}</textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="topics-LU" class="has-tooltips">Topics & LU learning outcomes
                                <div class="tooltips">
                                    <img src="{% static 'img/icon-info.svg' %}" alt="info">
                                    <div class="tooltips-content align-right">

                                        {% get_tooltip 'ica-topic' as tooltips %}
                                        
                                        <p>{{tooltips|clean_text}}</p>

                                    </div>
                                </div>
                            </label>
                            <textarea name="topics-LU" title="Input" class="form-control {% if not is_edit and not is_edit_ica_detail or not permission.update %}disabled{% endif %}" id="topics-LU" placeholder="Type here...">{%if schedule_assessment.topics %}{{schedule_assessment.topics}}{% endif %}</textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="reason-for-changes" class="has-tooltips">Reason for changes in assessment
                                <div class="tooltips">
                                    <img src="{% static 'img/icon-info.svg' %}" alt="info">
                                    <div class="tooltips-content align-right">

                                        {% get_tooltip 'ica-reasons-change' as tooltips %}
                                        
                                        <p>{{tooltips|clean_text}}</p>

                                    </div>
                                </div>
                            </label>
                            <textarea name="reason-for-changes" title="Input" class="form-control {% if not is_edit and not is_edit_ica_detail or not permission.update %}disabled{% endif %}" id="reason-for-changes" placeholder="Type here...">{%if schedule_assessment.reasons_change %}{{schedule_assessment.reasons_change}}{% endif %}</textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="reason-for-changes" class="has-tooltips">Note
                                <div class="tooltips">
                                    <img src="{% static 'img/icon-info.svg' %}" alt="info">
                                    <div class="tooltips-content">
                                        {% get_tooltip 'ica-note' as tooltips %}
                                        <p>{{tooltips|clean_text}}</p>
                                    </div>
                                </div>
                            </label>
                            <textarea name="note" title="Input" class="form-control {% if not is_edit and not is_edit_ica_detail or not permission.update %}disabled{% endif %}" id="note" placeholder="Type here...">{%if schedule_assessment.note %}{{schedule_assessment.note}}{% endif %}</textarea>
                        </div>
                    </div>

                    
                    {% if schedule_assessment.broken_rules_stage2 %}
                
                        <div  role="alert" class="mb-4 mb-0 fade d-flex align-items-center alert alert-danger show sgds container-error">
                            <div class="d-flex align-items-center justify-content-start">
                                <div id="message-specific-lu-message-{{schedule_assessment.id}}" >
                                    {% for failed_message in schedule_assessment.broken_rules_stage2.failed_conditions %}
                                    
                                    <div>
                                        {% if failed_message.name == "context_error" %}
                                            {{forloop.counter}}. {{failed_message.message|title}} | Please Check Rule Configuration
                                        {% elif failed_message.name %}
                                        
                                            {{forloop.counter}}. Rule: {{failed_message.name|rule_error_cleanup_message}} | 
                                            Input: {{failed_message.actual|check_dict|title}} | 

                                            Expected Input: ({{failed_message.operator|rule_error_cleanup_message|upper}}) {{failed_message.expected|title}}

                                    
                                        {% else %}
                                        
                                            {{forloop.counter}}. {{failed_message}}
                                        
                                        {% endif %}
                                    </div>                                        
                                    
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
            
                    {% endif %} 


                </div> 
                
                
            </div>
        
            {% endfor %}
        </form>

        

        {% else %}
        <div  class="mt-4 mb-0 fade d-flex align-items-center alert alert-warning show sgds">
            <div class="d-flex align-items-center justify-content-start gap-2">
                <img src="{% static 'img/icon-warning.svg' %}" alt="Warning">
                <div>Total weightage must be 100% and make sure all weeks have been reserved </div>
            </div>
        </div>
        {% endif %}    
    </div>   
</div>
{% if is_edit or is_edit_ica_detail and permission.update %}
<div class="alert alert-success mt-4 message-ica-detail d-none" role="alert">
    ICA detail updated successfully.
</div>
<div class="mt-4 d-flex justify-content-start align-items-center gap-2">
    <button form='ica-detail-form' class="btn save-change-ica-detail disabled {% if test_mode %}btn-black{% else %}btn-primary{% endif %}" type="submit">Save changes</button>
    <button class="btn-text text-danger show-popup" data-popup="popup-warning-unsaved" type="button">Cancel</button>
</div>       

{% endif %}

<c-popup title="Unsaved Changes" class="popup-warning-unsaved" >
    <p>Some required fields are missing. 
        If you leave now, your progress will be lost. 
        Make sure to complete all fields before saving.</p>
    <p>Do you want to leave without saving?</p>
    <div class="d-flex align-items-center gap-3 mt-4 justify-content-center">                      
        <button type="button" class="btn btn-primary close-popup">Stay on Page</button>
        <a href="{% url 'schedule_assessment:icaSchedule' learning_unit.id %}" class="btn-text text-danger fw-bold close-popup no-underline">Leave without saving</a>                       
    </div>
</c-popup>
