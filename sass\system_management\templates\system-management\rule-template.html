{% if rule.conditions %}
    {% if rule.conditions.all %}
        <div class="logic-operator">ALL of the following:</div>
        <div class="logic-group">
            {% for condition in rule.conditions.all %}
                {% include "system-management/rule-template.html" with rule=condition %}
            {% endfor %}
        </div>
    {% endif %}
    
    {% if rule.conditions.any %}
        <div class="logic-operator">ANY of the following:</div>
        <div class="logic-group">
            {% for condition in rule.conditions.any %}
                {% include "system-management/rule-template.html" with rule=condition %}
            {% endfor %}
        </div>
    {% endif %}
{% endif %}

{% if rule.name %}
    <div class="condition">
        <span class="field-name">{{ rule.name }}</span>
        <span class="operator">{{ rule.operator|replace_underscore }}</span>
        <span class="value">{{ rule.value }}</span>
    </div>
{% endif %}

{% if rule.actions %}
    <div class="actions">
        <h3>Actions:</h3>
        {% for action in rule.actions %}
            <div>
                {{ action.name }}: {{ action.params.message }}
            </div>
        {% endfor %}
    </div>
{% endif %}