# Generated by Django 5.1 on 2024-12-29 17:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "schedule_assessment",
            "0022_rename_status_stage1_businessrules_status_admin_break_rule_stage1_and_more",
        ),
    ]

    def add_update_at_not_null(apps, schema_editor):
        ScheduleAssessments = apps.get_model('schedule_assessment', 'ScheduleAssessment')
        for scheduleassessments in  ScheduleAssessments.objects.all():
            scheduleassessments.updated_at = scheduleassessments.created_at
            scheduleassessments.save()

    operations = [
        migrations.RunPython(add_update_at_not_null),
        migrations.AlterField(
            model_name="scheduleassessment",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
    ]
