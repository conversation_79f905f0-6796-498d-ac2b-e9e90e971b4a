from django.contrib import admin
from schedule_assessment.models import *
from django.utils.html import format_html
# Register your models here.
@admin.register(RuleDefinition)
class RuleDefinitionAdmin(admin.ModelAdmin):
    list_display = ('name', 'type', 'is_active','value','slug')
    list_filter = ('type', 'is_active')
    search_fields = ('name', 'description', 'value')
    readonly_fields = ('id','slug')

    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'type', 'value', 'is_active')
        }),
        ('Readonly Settings', {
            'fields': ('slug',),
        }),
    )


@admin.register(ScheduleAssessment)
class ScheduleAssessmentAdmin(admin.ModelAdmin):
    list_display = ('ica_number','week', 'lu', 'weightage','moderation_method',
                   'assessment_method', 'assessment_task','duration', 'digital_submission',
                   'updated_by', 'updated_at','owned_by','original_lu_code', 'original_assessment_slug', 'original_moderation_slug','created_at')
    ordering = ('-created_at',)
    list_filter = (
        'week',
        'over_two_weeks',
        'assessment_method',
        'digital_submission',
        'moderation_method',
        'updated_by'
    )

    search_fields = (
        'ica_number',
        'descriptions',
        'topics',
        'reasons_change',
        'lu__code'
    )

    readonly_fields = ('created_at', 'id','updated_at')

    fieldsets = (
        ('Basic Information', {
            'fields': (
                'ica_number',
                'week',
                'lu',
                'weightage',
                'over_two_weeks',
                'note',
                'broken_rules_stage1',
                'broken_rules_stage2'
            )
        }),
        ('Assessment Details', {
            'fields': (
                'assessment_method',
                'assessment_task',
                'duration',
                'moderation_method',
                'digital_submission'
            )
        }),
        ('Additional Information', {
            'fields': (
                'descriptions',
                'topics',
                'reasons_change'
            ),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': (
                'id',
                'updated_by',
                'updated_at',
                'owned_by',
                'created_at',
                'original_lu_code', 
                'original_assessment_slug', 
                'original_moderation_slug'
            ),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If this is a new object
            obj.updated_by = request.user
        else:
            obj.updated_by = request.user
        obj.save()

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.select_related('lu', 'moderation_method', 'updated_by')

@admin.register(ScheduleAssessmentTest)
class ScheduleAssessmentTestAdmin(admin.ModelAdmin):
    list_display = ('ica_number','week', 'lu', 'weightage','moderation_method',
                   'assessment_method', 'assessment_task','duration', 'digital_submission',
                   'updated_by', 'updated_at','original_lu_code', 'original_assessment_slug', 'original_moderation_slug','created_at')
    ordering = ('-created_at',)
    list_filter = (
        'week',
        'over_two_weeks',
        'assessment_method',
        'digital_submission',
        'moderation_method',
        'updated_by'
    )

    search_fields = (
        'ica_number',
        'descriptions',
        'topics',
        'reasons_change'
    )

    readonly_fields = ('created_at', 'id','updated_at')

    fieldsets = (
        ('Basic Information', {
            'fields': (
                'ica_number',
                'week',
                'lu',
                'weightage',
                'over_two_weeks',
                'broken_rules_stage1',
                'broken_rules_stage2'
            )
        }),
        ('Assessment Details', {
            'fields': (
                'assessment_method',
                'assessment_task',
                'duration',
                'moderation_method',
                'digital_submission'
            )
        }),
        ('Additional Information', {
            'fields': (
                'descriptions',
                'topics',
                'reasons_change'
            ),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': (
                'id',
                'updated_by',
                'updated_at',
                'created_at',
                'original_lu_code', 
                'original_assessment_slug', 
                'original_moderation_slug'
            ),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If this is a new object
            obj.updated_by = request.user
        else:
            obj.updated_by = request.user
        obj.save()

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.select_related('lu', 'moderation_method', 'updated_by')


@admin.register(BusinessRules)
class BusinessRulesAdmin(admin.ModelAdmin):
    list_display = ('slug', 'stage1','stage2', 'created_at')
    search_fields = ('slug', 'stage1','stage2')