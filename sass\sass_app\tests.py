# from django.test import TestCase
# from .models import LearningUnit, Week, Assessment
# import threading
# import time
# from django.db import transaction,connection
# from django.test import TransactionTestCase
# from django.urls import reverse
# from django.test import Client
# import concurrent.futures
# import json

# class AssessmentSchedulingTest(TransactionTestCase):
#     def setUp(self):
#         self.client = Client()
#         self.semester = 1
#         self.academic_year = 2024
#         self.learning_unit = LearningUnit.objects.create(
#             code="BM0055", 
#             name="HUMAN RESOURCES MANAGEMENT AND PRACTICES", 
#             semester=self.semester, 
#             academic_year=self.academic_year
#         )
#         self.week = Week.objects.create(
#             number=2, 
#             start_date="2024-09-03", 
#             end_date="2024-09-03", 
#             semester=self.semester, 
#             academic_year=self.academic_year
#         )
#         self.url = reverse('sass_app:schedule_assessment')  # Replace with your actual URL name
        
#     def attempt_scheduling(self):
#         data = {
#             'learning_unit_id': self.learning_unit.id,
#             'week_id': self.week.id,
#             'semester': self.semester,
#             'academic_year': self.academic_year,
#             'asses_over_2_week': False,
#             'asses_weightage': 20,  # Example value, adjust as needed
#             'asses_method': 'presentation'  # Example value, adjust as needed
#         }
#         response = self.client.post(self.url, data=data)
#         return response.status_code, response.content
    
#     def test_concurrent_scheduling(self):
#         num_requests = 10
#         with concurrent.futures.ThreadPoolExecutor(max_workers=num_requests) as executor:
#             futures = [executor.submit(self.attempt_scheduling) for _ in range(num_requests)]
#             results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
#         # Print summary
#         print("\nTest Results Summary:")
#         successful = [r for r in results if r[0] == 200]
#         failed = [r for r in results if r[0] != 200]
        
#         print(f"Successful attempts: {len(successful)}")
#         print(f"Failed attempts: {len(failed)}")

#         if successful:
#             print("\nSuccessful responses:")
#             for status_code, content in successful:
#                 response_data = json.loads(content)
#                 print(f"Status: {response_data['status']}, Message: {response_data['message']}")

#         if failed:
#             print("\nFailed responses:")
#             for status_code, content in failed:
#                 response_data = json.loads(content)
#                 print(f"Status code: {status_code}, Error: {response_data['message']}")

#         # Check that only one assessment was created
#         assessment_count = Assessment.objects.count()
#         self.assertEqual(assessment_count, 1)
#         print(f"\nTotal assessments created: {assessment_count}")

#         # Verify the created assessment
#         if assessment_count == 1:
#             assessment = Assessment.objects.first()
#             self.assertEqual(assessment.learning_unit, self.learning_unit)
#             self.assertEqual(assessment.week, self.week)
#             self.assertEqual(assessment.asses_over_2_week, False)
#             self.assertEqual(assessment.asses_weightage, 20)  # Adjust if you used a different value
#             self.assertEqual(assessment.asses_method, 'presentation')  # Adjust if you used a different value