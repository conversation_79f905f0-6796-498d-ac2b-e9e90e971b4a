{% extends 'base.html' %}
{% load static %}
{% load mathfilters %}
{% load custom_tags %}
{% block title %}
    Welcome
{% endblock %}
{% block content %}
 
    <section>
        <div class="card-welcome card-shadow">
            <div class="row w-100 align-items-center">
                <div class="col-lg-8">
                    <h2>Hi, {{ request.user.first_name }}!</h2>
                    <h3>What are we doing today?</h3>
           
                    <div class="d-flex justify-content-start align-items-center gap-4 welcome-menu">
                        {% if permission.report.view %}
                        <a href="{% url 'report:index' %}" class="d-flex justify-content-start align-items-center gap-2">
                            <img src="{% static 'img/icon-book.svg' %}" alt="report">
                            <span>Reports</span>
                        </a>
                        {% endif %}
                        {% if permission.live_schedule_status.view %}
                        <a href="{% url 'live-schedule:liveScheduleStatus' %}" class="d-flex justify-content-start align-items-center gap-2">
                            <img src="{% static 'img/icon-calendar.svg' %}" alt="Live Schedule Status">
                            <span>Live Schedule Status</span>
                        </a>
                        {% endif %}
                        {% if permission.system_management.view %}
                        <a href="{% url 'system_management:systemManagement' %}" class="d-flex justify-content-start align-items-center gap-2">
                            <img src="{% static 'img/icon-setting.svg' %}" alt="report">
                            <span>System management</span>
                        </a>
                        {% endif %}
                    </div>
                </div>
                <div class="col-lg-4">
                    <img src="{% static 'img/cat.png' %}" alt="cat" class="cat">
                </div>
            </div>
        </div>    
    </section>
 
    <!-- Weeks Heatmap -->    
    <div class="d-none"
        hx-get="{% url 'sass_app:heatmap_container' %}" 
        hx-trigger="load" 
        hx-swap="innerHTML" 
        hx-target="#heatmap-container"
    ></div>
    <div id="heatmap-container"></div>
   
 
    {% if permission.schedule_assessement.view %}
    <section class="mt-5 schedule-assessment-card" >
       
        <div class="card card-shadow card-assessment-table">
            <h3 class="title-section">Schedule Assessment</h3>
            <p class="fst-italic mb-2">Academic Year {{system_current_year.current_year}} - Semester {{system_current_year.current_semester}}</p>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group mb-0">
                            <label for="search-lu-code">LU Code</label>
                            <input autocomplete="off" type="text" name="search-lu-code" id="search-lu-code" class="form-control" placeholder="search LU code" hx-get="{% url 'sass_app:main' %}" hx-trigger="keyup changed delay:0.5s" hx-swap="outerHTML" hx-target=".content-schedule-assessment-home" hx-indicator=".loading-schedule-assessment" hx-select=".content-schedule-assessment-home">
                       
                    </div>
                </div>
            </div>
           
           
            <div class="wrapper-table-schedule-assessment-home">
                <div class="content-schedule-assessment-home">
                    <div class="table-responsive">
                        <table class="table-sass tr-cursor-pointer table table-borderless table-schedule-assessment">
                            <thead>
                                <tr>
                                    <th>LU CODE</th>
                                    <th>LU NAME</th>
                                    <th>TOTAL WEIGHTAGE</th>
                                    <th>STATUS</th>
                                    <th>LAST CHANGES BY</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for learning_unit in learning_units %}
                                    {% with status_unit=learning_unit|get_status:SYSTEM_CURRENT_YEAR %}
                                        <tr class='clickable-row' data-url="{% url 'schedule_assessment:icaSchedule' learning_unit.id %}">
                                            <td>{{learning_unit.code}}</td>
                                            <td>{{learning_unit.name}}</td>
                                           
                                            <td>
                                           
                                                {% get_total_weightage learning_unit as total_weightage %}
                                                {% if total_weightage %}
                                                    {{total_weightage}}%
                                                {% else %}
                                                    0%
                                                {% endif %}
                                            </td>
                                           
                                            <td>{% if status_unit.status %} {{status_unit.get_status_display|title}} {% else %} Pending {% endif %}</td>
                                            <td>
                                                {% if status_unit.updated_by %} {{status_unit.updated_by.first_name}} {% else %} N/A {% endif %}
                                                {% if status_unit.updated_at %} <span class="datetime">{{status_unit.updated_at|date:"d-M-Y H:i"}}</span>{% endif %}
                                            </td>
                                        </tr>  
                                    {% endwith %}
                                {% empty %}  
                                    <tr>
                                        <td colspan="5" class="text-center">No data found.</td>
                                    </tr>
                                {% endfor %}  
                         
                            </tbody>
                        </table>
                    </div>
       
                    <div class="d-flex justify-content-center">
                        {% if page_content.paginator.num_pages > 1 %}
                            {% with total_pages=page_content.paginator.num_pages %}
                                <div class="pagination mt-3">
                                    {% for num in page_content.paginator.page_range %}
                                        {% if num == 1 or num == total_pages %}
                                            <a hx-get="{% url 'sass_app:main' %}?page={{num}}{% if search_lu_code %}&search-lu-code={{ search_lu_code }}{% endif %}" class="page-link cursor-pointer {% if num == page_content.number %} active {% endif %}" hx-trigger="click" hx-swap="outerHTML" hx-target=".content-schedule-assessment-home" hx-indicator=".loading-schedule-assessment" hx-select=".content-schedule-assessment-home">{{ num }}</a>
                                        {% elif num|sub:page_content.number >= -1 and num|sub:page_content.number <= 1  %}
                                            <a hx-get="{% url 'sass_app:main' %}?page={{num}}{% if search_lu_code %}&search-lu-code={{ search_lu_code }}{% endif %}" class="page-link cursor-pointer {% if num == page_content.number %} active {% endif %}" hx-trigger="click" hx-swap="outerHTML" hx-target=".content-schedule-assessment-home" hx-indicator=".loading-schedule-assessment" hx-select=".content-schedule-assessment-home">{{ num }}</a>
                                        {% elif num|sub:page_content.number > -3 and num|sub:page_content.number < 3  %}
                                            <span class="page-link">...</span>
                                        {% endif %}
                                   
                                    {% endfor %}
                                </div>
                            {% endwith %}
                        {% endif %}
                    </div>
                </div>
                <div class="loading htmx-indicator loading-schedule-assessment loading-absolute">
                    <img src="{% static 'img/loading.gif' %}" alt="loading" width="50">
                </div>                
            </div>
        </div>
       
    </section>
    {% endif %}
 
{% endblock %}
 
{% block script %}
    <script>
      
        $(document).ready(function(){
            $(document).on("click",  '.clickable-row', function(){
                window.location = $(this).data('url');
            });
 
        })
 
    </script>
   
{% endblock script %}