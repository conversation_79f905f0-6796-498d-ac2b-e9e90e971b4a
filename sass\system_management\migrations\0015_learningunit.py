# Generated by Django 5.1 on 2024-10-20 12:15

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("system_management", "0014_alter_fileupload_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="LearningUnit",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("code", models.<PERSON>r<PERSON><PERSON>(max_length=20, unique=True)),
                ("name", models.Char<PERSON><PERSON>(max_length=255)),
                ("credit", models.IntegerField(blank=True, null=True)),
                ("is_examinable", models.BooleanField(default=False)),
                (
                    "unit_leader",
                    models.Char<PERSON><PERSON>(blank=True, max_length=255, null=True),
                ),
                (
                    "course_type",
                    models.Char<PERSON><PERSON>(blank=True, max_length=255, null=True),
                ),
                ("lu_type", models.Char<PERSON><PERSON>(blank=True, max_length=255, null=True)),
                ("department", models.Char<PERSON><PERSON>(blank=True, max_length=255, null=True)),
                ("phase", models.Integer<PERSON>ield(blank=True, null=True)),
                ("message", models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "db_table": "LearningUnit",
            },
        ),
    ]
