from django.contrib import admin
from system_management.models import *
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _

# Create an Inline admin class for Role
class RoleInline(admin.StackedInline):
    model = Role
    can_delete = False
    verbose_name_plural = 'Role'
    fk_name = 'user'


# Customize the User Admin
class CustomUserAdmin(UserAdmin):
    inlines = (RoleInline,)
    list_display = ('username', 'email', 'first_name', 'last_name', 'get_role_type', 'get_role_status')
    list_select_related = ('registered_user',)

    def get_role_type(self, instance):
        return instance.role.type if hasattr(instance, 'role') else '-'
    get_role_type.short_description = 'Role Type'

    def get_role_status(self, instance):
        return instance.role.status if hasattr(instance, 'role') else '-'
    get_role_status.short_description = 'Status'

    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        (_('Personal info'), {'fields': ('first_name', 'last_name', 'email')}),
        (_('Permissions'), {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
        }),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )

# Unregister the default UserAdmin and register our CustomUserAdmin
admin.site.unregister(User)
admin.site.register(User, CustomUserAdmin)

# Register your models here.
class PermissionAdmin(admin.ModelAdmin):
    list_display = ('type', 'page', 'create', 'update', 'delete', 'view', 'created_at')
    list_filter = ('type', 'page', 'create', 'update', 'delete', 'view')
    search_fields = ('type', 'page')
    readonly_fields = ('created_at',)
    fieldsets = (
        (None, {
            'fields': ('type', 'page')
        }),
        ('Permissions', {
            'fields': ('create', 'update', 'delete', 'view')
        }),
        ('Metadata', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        })
    )

    def get_readonly_fields(self, request, obj=None):
        if obj:  # editing an existing object
            return self.readonly_fields + ('type',)
        return self.readonly_fields
admin.site.register(Permission, PermissionAdmin)

class RoleAdmin(admin.ModelAdmin):
    list_display = ('user', 'status', 'type', 'created_at')
    list_filter = ('status', 'type')
    search_fields = ['user__email', 'type']
admin.site.register(Role, RoleAdmin)

class AccountAdmin(admin.ModelAdmin):
    list_display = ('user', 'created_at')

class FileUploadAdmin(admin.ModelAdmin):
    list_display = ('name', 'type', 'uploaded_by', 'created_at')
    list_filter = ('type', 'uploaded_by', 'created_at')
    search_fields = ('name', 'uploaded_by__username')
    readonly_fields = ('id', 'created_at')

    fieldsets = (
        (None, {
            'fields': ('name', 'file', 'type', 'uploaded_by')
        }),
        ('Advanced options', {
            'classes': ('collapse',),
            'fields': ('id', 'created_at'),
        }),
    )
admin.site.register(FileUpload, FileUploadAdmin)


class LearningUnitAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'credit', 'is_examinable', 'unit_leader', 'course_type', 'lu_type', 'phase')
    list_filter = ('is_examinable', 'course_type', 'lu_type', 'phase')
    search_fields = ('code', 'name', 'unit_leader')
    readonly_fields = ('id', 'created_at', 'updated_at')
    ordering = ('code',)
admin.site.register(LearningUnit, LearningUnitAdmin)


class SystemEditModeAdmin(admin.ModelAdmin):
    list_display = ('id', 'mode', 'group', 'start', 'end', 'edit_after', 'created_at')
    list_filter = ('mode', 'group', 'created_at')
    search_fields = ('id', 'mode', 'group')
    readonly_fields = ('id', 'created_at')
    ordering = ('-created_at',)
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'mode', 'group')
        }),
        ('Time Settings', {
            'fields': ('start', 'end', 'edit_after', 'created_at')
        }),
    )

    # Optional: Add date hierarchy for better filtering
    date_hierarchy = 'created_at'

admin.site.register(SystemEditMode, SystemEditModeAdmin)

@admin.register(StatusLearningUnit)
class StatusLearningUnitAdmin(admin.ModelAdmin):
    list_display = ('lu', 'current_year', 'current_semester', 'weightage', 'status', 'updated_by','original_lu_code','created_at')
    list_filter = ('status', 'current_year', 'current_semester','original_lu_code')
    search_fields = ('lu__name','lu__code', 'updated_by__username','original_lu_code')
    
    list_per_page = 20

    readonly_fields = ('created_at',)
    
    fieldsets = (
        ('Learning Unit Information', {
            'fields': ('lu', 'weightage')
        }),
        ('Status Information', {
            'fields': ('status', 'updated_by')
        }),
        ('Academic Period', {
            'fields': ('current_year', 'current_semester')
        })
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If creating new object
            obj.updated_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(StatusLearningUnitTest)
class StatusLearningUnitTestAdmin(admin.ModelAdmin):
    list_display = ('lu', 'current_year', 'current_semester', 'weightage', 'status', 'updated_by','original_lu_code','created_at')
    list_filter = ('status', 'current_year', 'current_semester','original_lu_code')
    search_fields = ('lu__name','lu__code', 'updated_by__username','original_lu_code')
    
    list_per_page = 20

    readonly_fields = ('created_at',)
    
    fieldsets = (
        ('Learning Unit Information', {
            'fields': ('lu', 'weightage')
        }),
        ('Status Information', {
            'fields': ('status', 'updated_by')
        }),
        ('Academic Period', {
            'fields': ('current_year', 'current_semester')
        })
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If creating new object
            obj.updated_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(SystemCurrentYear)
class SystemCurrentYearAdmin(admin.ModelAdmin):
    list_display = ('id', 'current_year', 'current_semester', 'created_at')
    list_filter = ('current_year', 'current_semester')
    readonly_fields = ('id', 'created_at')
    search_fields = ('id', 'current_year', 'current_semester')
    ordering = ('-created_at',)

    def has_delete_permission(self, request, obj=None):
        # Prevent deletion if there's only one record
        if SystemCurrentYear.objects.count() <= 1:
            return False
        return True

@admin.register(ModerationMethod)
class ModerationMethodAdmin(admin.ModelAdmin):
    list_display = ['description', 'slug', 'created_at', 'updated_at']
    list_filter = ['created_at', 'updated_at']
    search_fields = ['description', 'slug']
    readonly_fields = ['id', 'slug', 'created_at', 'updated_at']
    ordering = ['-created_at']

    def get_readonly_fields(self, request, obj=None):
        if obj:  # editing an existing object
            return self.readonly_fields
        return ['id', 'created_at', 'updated_at']  # allow slug to be set on creation
    
@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'created_at', 'updated_at']
    list_filter = ['created_at', 'updated_at']
    search_fields = ['code', 'name']
    readonly_fields = ['id', 'created_at', 'updated_at']
    ordering = ['code']
    
    # Optional: Add a custom list_display_links to make both code and name clickable
    list_display_links = ['code', 'name']


@admin.register(CourseLuMap)
class CourseLuMapAdmin(admin.ModelAdmin):
    list_display = ['course_code', 'lu', 'academic_year', 'semester', 'year_study']
    list_filter = ['academic_year', 'semester', 'year_study']
    search_fields = [
        'course_code__code',  # Assuming Course model has 'code' field
        'lu__code',      # Assuming LearningUnit model has 'code' field
        'academic_year',
    ]
    readonly_fields = ['id', 'created_at', 'updated_at']
    ordering = ['academic_year', 'semester', 'course_code']
    
    # Optional: Add autocomplete fields for better performance with large datasets
    autocomplete_fields = ['course_code', 'lu']
    
    # Optional: Group fields in the edit form
    fieldsets = (
        ('Course Information', {
            'fields': ('course_code', 'lu')
        }),
        ('Academic Period', {
            'fields': ('academic_year', 'semester', 'year_study')
        }),
        ('System Fields', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def get_readonly_fields(self, request, obj=None):
        if obj:  # editing an existing object
            return self.readonly_fields
        return ['id', 'created_at', 'updated_at']
    
@admin.register(Assessment)
class AssessmentAdmin(admin.ModelAdmin):
   list_display = ['method', 'task', 'duration', 'notes', 'created_at']
   list_filter = ['created_at', 'updated_at']
   search_fields = ['method', 'task', 'notes']
   readonly_fields = ['id', 'slug', 'created_at', 'updated_at']
   ordering = ['-created_at']

   # Optional: Group fields in the edit form
   fieldsets = (
       ('Assessment Details', {
           'fields': ('method', 'task', 'duration', 'notes')
       }),
       ('System Fields', {
           'fields': ('id', 'slug', 'created_at', 'updated_at'),
           'classes': ('collapse',)
       })
   )

   def get_readonly_fields(self, request, obj=None):
       if obj:  # editing existing object
           return self.readonly_fields
       return ['id', 'created_at', 'updated_at']  # allow slug on creation

@admin.register(AcademicWeek)
class AcademicWeekAdmin(admin.ModelAdmin):
    list_display = (
        'coursetype',
        'lutype',
        'maxicacountperweek',
        'startdate',
        'term1end',
        'lastinstructionalweek'
    )
    
    list_filter = ('coursetype', 'lutype')
    
    search_fields = ('coursetype', 'lutype')

@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'owner', 'created_at')
    list_filter = ('created_at', 'owner')
    search_fields = ('code', 'name', 'owner')
    readonly_fields = ('id', 'created_at')
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('code', 'name')
        }),
        ('Ownership Details', {
            'fields': ('owner',)
        }),
        ('System Fields', {
            'classes': ('collapse',),
            'fields': ('id', 'created_at'),
        }),
    )