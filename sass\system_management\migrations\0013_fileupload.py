# Generated by Django 5.1 on 2024-10-19 06:29

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("system_management", "0012_alter_permission_options_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="FileUpload",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("file", models.FileField(upload_to="uploads/")),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("course", "Course"),
                            ("assesement", "Assessment"),
                            ("moderation_method", "Moderation Method"),
                            ("course_lu_map", "Course LU Map"),
                            ("academic_week", "Academic Week"),
                            ("learning_unit", "Learning Unit"),
                        ],
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
