
from django.shortcuts import render, redirect
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from sass_app.microsoft_auth.auth_helper import get_sign_in_flow, get_token_from_code, get_token, get_sign_out_flow
from sass_app.microsoft_auth.graph_helper import *
from django.contrib.auth import login, logout
from django.contrib.auth.models import User
from system_management.models import Role
from django.contrib.auth.decorators import login_required
# In your views.py or account.py


@require_http_methods(["GET"])
def login_view(request):
    # login Via Micorosft Only
    authorization_url = microsoft_login(request)
    if authorization_url:
        return redirect(authorization_url)

    return redirect('app:main')


@require_http_methods(["POST"])
def login_submit(request):
    return redirect('app:main')

@login_required
def logout_submit(request):
    if request.method == 'POST':
        # logout third party
        logout_url = microsoft_logout(request)
        if logout_url:
            # logout django
            logout(request)
            return redirect(logout_url)

    return redirect('app:main')


@csrf_exempt
def microsoft_callback(request):
    # Make the token request
    try:
        result = get_token_from_code(request)
    except Exception as e:
        return redirect('app:main')

    token_response = get_token(request)
    if token_response:
        if "id_token" in token_response:
            request.session['id_token'] = token_response['id_token']

    if "access_token" in result:
        if not result['access_token']:
            return redirect('app:main')
    else:
        return redirect('app:main')
    
    
    # Get the user's profile from graph_helper.py script
    user = get_me(result['access_token'])
    try:
        
        email = user['mail']
        
        if not 'displayName' in user:
            displayName = email.split("@")[0]
        else: displayName = user['displayName']

        if not 'surname' in user:
            surname = email.split("@")[0]
        else: surname = user['surname']
        
        username = None
        if email:
            username = email.split("@")[0]

        user = User.objects.filter(email=email).first()
        if not user:
            user = User.objects.create_user(username=username, email=email)
        if displayName:
            user.first_name = displayName
        if surname:
            user.last_name = surname
        user.save()

        # account,_ = Account.objects.get_or_create(user=user)
        # if account:
        #     account.access_token = result['access_token']
        #     account.save()

        role_, _ = Role.objects.get_or_create(user=user)
        if not user:
            role_.status = 'inactive'
            role_.type = 'user'
            role_.save()

        # Log the user in
        login(request, user)

    except Exception as e:
        print("e: ",e)
        return redirect('app:logout')

    return redirect('app:main')


def microsoft_login(request):

    # Get the sign-in flow
    flow = get_sign_in_flow()
    authorization_url = flow['auth_uri']
    # Save the expected flow so we can use it in the callback
    try:
        request.session['auth_flow'] = flow
    except Exception as e:
        print(e)
    # Redirect the user to the Azure AD login page
    return authorization_url


def microsoft_logout(request):
    logout_url = get_sign_out_flow(request)
    return logout_url