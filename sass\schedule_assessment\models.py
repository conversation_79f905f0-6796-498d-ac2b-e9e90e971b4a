from django.db import models
import uuid
from django.contrib.auth.models import User
import system_management.models as system_management
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from utility import *
from django.utils.text import slugify


# Create your models here.
class RuleDefinition(models.Model):
    RULE_TYPES = (
        ('Variable', 'Variable'),
        ('Rule', 'Rule'),
        ('Definitions', 'Definitions'),
        ('File', 'File'),
    )

    slug = models.SlugField(max_length=255, unique=True, default="")
    name = models.CharField(max_length=200, unique=True)
    description = models.TextField()
    type = models.CharField(max_length=20, choices=RULE_TYPES)
    value = models.CharField(max_length=255, blank=True, null=True)

    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)

        super().save(*args, **kwargs)


class BusinessRules(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    slug = models.CharField(max_length=200, unique=True)

    stage1 = models.TextField(blank=True, null=True)
    stage2 = models.TextField(blank=True, null=True)
    status_admin_break_rule_stage1 = models.BooleanField(default=True)
    status_admin_break_rule_stage2 = models.BooleanField(default=True)

    stage1_test = models.TextField(blank=True, null=True)
    stage2_test = models.TextField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.slug

    class Meta:
        verbose_name = u'business_rules'
        verbose_name_plural = u'business_rules'


class ScheduleAssessment(models.Model):
    # The categories a user might want to include
    DIGITAL_SUBMISSION_CHOICES = (
        ('no', 'No'),
        ('yes-in-the-same-week-as-digital-submission',
         'Yes, in the same week as digital submission'),
        ('yes-in-the-following-week-after-digital-submission',
         'Yes, in the following week after digital submission')
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    current_year = models.IntegerField(
        null=False, blank=False, default=int(timezone.now().year))
    current_semester = models.IntegerField(null=False, blank=False, default=1)

    ica_number = models.IntegerField()
    week = models.IntegerField(null=True, blank=True)
    lu = models.ForeignKey(system_management.LearningUnit,
                           on_delete=models.SET_NULL, null=True, blank=True)

    weightage = models.IntegerField(
        validators=[MinValueValidator(0), MaxValueValidator(100)], default=0)
    over_two_weeks = models.CharField(max_length=10, choices=[(
        'no', 'No'), ('yes', 'Yes')], null=False, default="no")
    assessment_method = models.ForeignKey(
        system_management.Assessment, on_delete=models.SET_NULL, null=True, blank=True)

    assessment_task = models.CharField(max_length=100, null=True, blank=True)
    duration = models.IntegerField(
        null=True, blank=True, help_text="Duration in minutes")
    moderation_method = models.ForeignKey(
        system_management.ModerationMethod, on_delete=models.SET_NULL, null=True, blank=True)

    assessment_type = models.CharField(max_length=100, null=True, blank=True)
    digital_submission = models.CharField(
        choices=DIGITAL_SUBMISSION_CHOICES, max_length=255, null=True, blank=True)

    descriptions = models.TextField(null=True, blank=True)
    topics = models.TextField(null=True, blank=True)
    reasons_change = models.TextField(null=True, blank=True)

    note = models.CharField(max_length=500, null=True, blank=True)

    broken_rules = models.JSONField(null=True, blank=True)
    broken_rules_stage1 = models.JSONField(null=True, blank=True)
    broken_rules_stage2 = models.JSONField(null=True, blank=True)

    updated_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    owned_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, related_name="owned_by")

    # Original code/slug fields
    original_lu_code = models.CharField(max_length=100, null=True, blank=True)
    original_assessment_slug = models.CharField(
        max_length=100, null=True, blank=True)
    original_moderation_slug = models.CharField(
        max_length=100, null=True, blank=True)

    def get_date_from_week(self):
        """Get date from week number"""
        return {
            'week': self.week,
            'date': get_date_by_week(self.week, self.lu)
        }

    def save(self, *args, **kwargs):

        if self.lu:
            self.original_lu_code = self.lu.code

        if self.assessment_method:
            self.original_assessment_slug = self.assessment_method.slug

        if self.moderation_method:
            self.original_moderation_slug = self.moderation_method.slug

        super().save(*args, **kwargs)

    def __str__(self):
        return f"Ica Number {self.ica_number} - Week {self.week} - Lu {self.lu} - Weightage {self.weightage}"


# duplicate of ScheduleAssessment Model.
class ScheduleAssessmentTest(models.Model):
    # The categories a user might want to include
    DIGITAL_SUBMISSION_CHOICES = (
        ('no', 'No'),
        ('yes-in-the-same-week-as-digital-submission',
         'Yes, in the same week as digital submission'),
        ('yes-in-the-following-week-after-digital-submission',
         'Yes, in the following week after digital submission')
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    current_year = models.IntegerField(
        null=False, blank=False, default=int(timezone.now().year))
    current_semester = models.IntegerField(null=False, blank=False, default=1)

    ica_number = models.IntegerField()
    week = models.IntegerField(null=True, blank=True)
    lu = models.ForeignKey(system_management.LearningUnit,
                           on_delete=models.SET_NULL, null=True, blank=True)

    weightage = models.IntegerField(
        validators=[MinValueValidator(0), MaxValueValidator(100)], default=0)
    over_two_weeks = models.CharField(max_length=10, choices=[(
        'no', 'No'), ('yes', 'Yes')], null=False, default="no")
    assessment_method = models.ForeignKey(
        system_management.Assessment, on_delete=models.SET_NULL, null=True, blank=True)

    assessment_task = models.CharField(max_length=100, null=True, blank=True)
    duration = models.IntegerField(
        null=True, blank=True, help_text="Duration in minutes")
    moderation_method = models.ForeignKey(
        system_management.ModerationMethod, on_delete=models.SET_NULL, null=True, blank=True)

    assessment_type = models.CharField(max_length=100, null=True, blank=True)
    digital_submission = models.CharField(
        choices=DIGITAL_SUBMISSION_CHOICES, max_length=255, null=True, blank=True)

    descriptions = models.TextField(null=True, blank=True)
    topics = models.TextField(null=True, blank=True)
    reasons_change = models.TextField(null=True, blank=True)

    broken_rules = models.JSONField(null=True, blank=True)
    broken_rules_stage1 = models.JSONField(null=True, blank=True)
    broken_rules_stage2 = models.JSONField(null=True, blank=True)

    updated_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    owned_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, related_name="test_owned_by")

    # Original code/slug fields
    original_lu_code = models.CharField(max_length=100, null=True, blank=True)
    original_assessment_slug = models.CharField(
        max_length=100, null=True, blank=True)
    original_moderation_slug = models.CharField(
        max_length=100, null=True, blank=True)

    def get_date_from_week(self):
        """Get date from week number"""
        return {
            'week': self.week,
            'date': get_date_by_week(self.week, self.lu)
        }

    def save(self, *args, **kwargs):

        if self.lu:
            self.original_lu_code = self.lu.code

        if self.assessment_method:
            self.original_assessment_slug = self.assessment_method.slug

        if self.moderation_method:
            self.original_moderation_slug = self.moderation_method.slug

        super().save(*args, **kwargs)

    def __str__(self):
        return f"Ica Number {self.ica_number} - Week {self.week} - Lu {self.lu} - Weightage {self.weightage}"
