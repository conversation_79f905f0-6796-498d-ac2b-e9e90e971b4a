{% load static %}  
{% load mathfilters %}
{% load custom_tags %}
<div id="activity-container">  
    <div class="row">
        <div class="col-12">
            <div class="breadcrumb">
                <a href="/">Back</a> <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M7.07586 4.51043C7.18836 4.39807 7.34086 4.33496 7.49986 4.33496C7.65886 4.33496 7.81136 4.39807 7.92386 4.51043L11.3239 7.91043C11.4362 8.02293 11.4993 8.17543 11.4993 8.33443C11.4993 8.49343 11.4362 8.64593 11.3239 8.75843L7.92386 12.1584C7.81012 12.2644 7.65969 12.3221 7.50424 12.3194C7.3488 12.3166 7.2005 12.2537 7.09057 12.1437C6.98064 12.0338 6.91766 11.8855 6.91492 11.7301C6.91218 11.5746 6.96988 11.4242 7.07586 11.3104L10.0519 8.33443L7.07586 5.35843C6.9635 5.24593 6.90039 5.09343 6.90039 4.93443C6.90039 4.77543 6.9635 4.62293 7.07586 4.51043Z" fill="#BBBBBB"/>
                    </svg>
                <span>Activity Log</span>
            </div>  
            <div class="content-system-management">     
                <div class="card card-shadow card-padding">
                    <h2 class="title-section">System Management</h2>
                    <c-menu_system_management current_page="activityLog"></c-menu_system_management>
                    <h2 class="title-card text-black mb-4">Activity Log</h2>
                    
                    <div class="content-activity-log">
                        <div class="d-flex flex-column flex-md-row align-items-md-center gap-4 justify-content-start menu-user-management">   
                            <form action="{% url 'system_management:export_activity_table' %}" method="POST">
                                {% csrf_token %}
                                
                                {% if filter_by %}<input type="hidden" name="filter-by" value={{filter_by}}> {% endif %}
                                {% if lu_code %}<input type="hidden" name="lu-code" value={{lu_code}}> {% endif %}
                                {% if email %}<input type="hidden" name="email" value={{email}}> {% endif %}
                                {% if data_from %}<input type="hidden" name="date-time-from" value={{data_to}}> {% endif %}
                                {% if data_to %}<input type="hidden" name="date-time-to" value={{data_to}}> {% endif %}
                                {% if action %}<input type="hidden" name="action" value={{action}}> {% endif %}
                                {% if module %}<input type="hidden" name="module" value={{module}}> {% endif %}
                                {% if department %}<input type="hidden" name="department" value={{department}}> {% endif %}

                                <input type="hidden" name="search-export-log" value="">
                                <button type="submit" class="btn-text btn-export-log v-center p-0"><img class="icon-image" src="{% static 'img/icon-export.svg' %}" alt="bulk"> Export Log</button>
                            </form>
                            <button type="button" class="btn-text v-center p-0 edit-files show-popup" data-popup="activity-log__popup"><img class="icon-image" src="{% static 'img/icon-filter.svg' %}" alt="bulk"> Filter by {% if filter_by %}<span class="badge badge-primary">{{ filter_by|upper }}</span>{% endif %}</button>
                        </div>
                        
                        <div class="mt-4 row">           
                            <div class="form-group mb-4">
                                <label for="Search">Search </label>
                                <input type="text" name="search" id="Search" class="form-control search-activity-log" placeholder="search" hx-get="{% url 'system_management:activityLog' %}" hx-trigger="keyup changed delay:0.5s" hx-swap="outerHTML" hx-target=".content-activity-log-table" 
                                hx-select=".content-activity-log-table" hx-indicator=".loading-activity-log"  />  
                            </div>
                            
                            <div class="wrapper-content-activity-log p-relative">
                                <div class="content-activity-log-table ">
                                    <div class="table-responsive">
                                        <table class="table-sass table-activity-log table table-borderless">
                                            <thead class="bg-gray text-black">
                                                <tr>                            
                                                    <th hx-get="{% url 'system_management:activityLog' %}?{% if department %}&department={{ department|urlencode }}{% endif %}{% if module %}&module={{ module }}{% endif %}{% if action %}&action={{ action }}{% endif %}{% if data_from %}&date-time-from={{ data_from }}{% endif %}{% if data_to %}&date-time-to={{ data_to }}{% endif %}{% if filter_by %}&filter-by={{ filter_by }}{% endif %}{% if email  %}&email={{ email }}{% endif %}{% if lu_code %}&lu-code={{ lu_code }}{% endif %}{% if search %}&search={{ search }}{% endif %}{% if search_user %}&search-user={{ search_user }}{% endif %}&sort={% if sort == 'asc' or sort == None %}desc{% else %}asc{% endif %}&sort-col=datetime" hx-target=".content-activity-log-table" hx-select=".content-activity-log-table" hx-swap="outerHTML" hx-indicator=".loading-activity-log">
                                                        <div class="wrapper-th-sort">
                                                            <span>TIMESTAMP</span> 
                                                            <div class="wrapper-caret-sort"> <span class="top-caret {% if sort == "desc" and sort_col == "datetime" %}disabled{% endif %}"><i class="bi bi-caret-down-fill"></i></span><span class="buttom-caret {% if sort == "asc" and sort_col == "datetime" %}disabled{% endif %}"><i class="bi bi-caret-up-fill"></i></span> </div>
                                                        </div>
                                                    </th>
                                                    <th hx-get="{% url 'system_management:activityLog' %}?{% if department %}&department={{ department|urlencode }}{% endif %}{% if module %}&module={{ module }}{% endif %}{% if action %}&action={{ action }}{% endif %}{% if data_from %}&date-time-from={{ data_from }}{% endif %}{% if data_to %}&date-time-to={{ data_to }}{% endif %}{% if filter_by %}&filter-by={{ filter_by }}{% endif %}{% if email  %}&email={{ email }}{% endif %}{% if lu_code %}&lu-code={{ lu_code }}{% endif %}{% if search %}&search={{ search }}{% endif %}{% if search_user %}&search-user={{ search_user }}{% endif %}&sort={% if sort == 'asc' or sort == None %}desc{% else %}asc{% endif %}&sort-col=user__email" hx-target=".content-activity-log-table" hx-select=".content-activity-log-table" hx-swap="outerHTML" hx-indicator=".loading-activity-log">
                                                        <div class="wrapper-th-sort">
                                                            <span>EMAIL</span> 
                                                            <div class="wrapper-caret-sort"> <span class="top-caret {% if sort == "desc" and sort_col == "user__email" %}disabled{% endif %}"><i class="bi bi-caret-down-fill"></i></span><span class="buttom-caret {% if sort == "asc" and sort_col == "user__email" %}disabled{% endif %}"><i class="bi bi-caret-up-fill"></i></span> </div>
                                                        </div>
                                                    </th>
                                                    <th>DEPARTMENT </th>
                                                    <th>LU CODE </th>
                                                    <th>MODULE </th>
                                                    <th>ACTION </th>
                                                    <th>DETAILS OF CHANGE </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% if not activity_logs %}
                                                    <tr>
                                                        <td colspan="7" class="py-4 text-center">No log found.</td>
                                                    </tr>
                                                {% else %}
                                                    {% for activity_log in activity_logs %}
                                                        {% if activity_log.content_type.model|lower != "account" and activity_log.content_type.model|lower != "errorlog"  %}
                                                            <tr>                           
                                                                <td>{{activity_log.datetime|date:"d-M-Y H:i"}}</td>
                                                                <td>{{activity_log.user.email}}</td>
                                                                <td>{{activity_log.object_repr|get_department}} </td>
                                                                <td>{{activity_log.object_repr|get_lu_code}} </td>
                                                                <td>{{activity_log.content_type.model|title }} </td>
                                                                <td>{{activity_log.get_event_type_display }} </td>
                                                                <td>
                                                                    <button class="btn btn-primary btn-view-details-log show-popup">View details</button>
                                                                    <c-popup title="DETAILS OF CHANGE" class="popup-log">
                                                                        <div class="text-start">
                                                                            {% if activity_log.event_type == 3 %}{Deleted}{% else %}{{activity_log|clean_changed_fields}}{% endif %} 
                                                                        </div>
                                                                    </c-popup>
                                                                </td>
                                                            </tr>    
                                                        {% endif %} 
                                                    {% endfor %}                                                    
                                                {% endif %}
        
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <div class="d-flex justify-content-center">
                                        {% if page_content.paginator.num_pages > 1 %}
                                            {% with total_pages=page_content.paginator.num_pages %}
                                                <div class="pagination mt-3">
                                                    {% for num in page_content.paginator.page_range %}
                                                        {% if num == 1 or num == total_pages %}
                                                            <a hx-get="{% url 'system_management:activityLog' %}?page={{num}}{% if department %}&department={{ department|urlencode }}{% endif %}{% if module %}&module={{ module }}{% endif %}{% if action %}&action={{ action }}{% endif %}{% if data_from %}&date-time-from={{ data_from }}{% endif %}{% if data_to %}&date-time-to={{ data_to }}{% endif %}{% if filter_by %}&filter-by={{ filter_by }}{% endif %}{% if email  %}&email={{ email }}{% endif %}{% if lu_code %}&lu-code={{ lu_code }}{% endif %}{% if search %}&search={{ search }}{% endif %}&sort={% if sort == None %}desc{% else %}{{ sort }}{% endif %}&sort-col={% if sort_col == None %}datetime{% else %}{{ sort_col }}{% endif %}" class="page-link cursor-pointer {% if num == page_content.number %} active {% endif %}" hx-trigger="click" hx-swap="outerHTML" hx-target=".content-activity-log-table" hx-select=".content-activity-log-table" hx-indicator=".loading-activity-log">{{ num }}</a>
                                                        {% elif num|sub:page_content.number >= -1 and num|sub:page_content.number <= 1  %}
                                                            <a hx-get="{% url 'system_management:activityLog' %}?page={{num}}{% if department %}&department={{ department|urlencode }}{% endif %}{% if module %}&module={{ module }}{% endif %}{% if action %}&action={{ action }}{% endif %}{% if data_from %}&date-time-from={{ data_from }}{% endif %}{% if data_to %}&date-time-to={{ data_to }}{% endif %}{% if filter_by %}&filter-by={{ filter_by }}{% endif %}{% if email  %}&email={{ email }}{% endif %}{% if lu_code %}&lu-code={{ lu_code }}{% endif %}{% if search %}&search={{ search }}{% endif %}&sort={% if sort == None %}desc{% else %}{{ sort }}{% endif %}&sort-col={% if sort_col == None %}datetime{% else %}{{ sort_col }}{% endif %}" class="page-link cursor-pointer {% if num == page_content.number %} active {% endif %}" hx-trigger="click" hx-swap="outerHTML" hx-target=".content-activity-log-table" hx-select=".content-activity-log-table" hx-indicator=".loading-activity-log">{{ num }}</a>
                                                        {% elif num|sub:page_content.number > -3 and num|sub:page_content.number < 3  %}
                                                            <span class="page-link">...</span>
                                                        {% endif %} 
                                                    
                                                    {% endfor %}
                                                </div>
                                            {% endwith %}
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="loading htmx-indicator loading-activity-log loading-absolute">
                                    <img src="{% static 'img/loading.gif' %}" alt="loading" width="50">
                                </div> 
                            </div>
                        </div>
                    </div>
                </div>
            </div>  
        </div>
    </div>
                    
    <c-popup class="popup-wide activity-log__popup">
        <div class="d-flex justify-content-between align-items-center w-100">
            <h2 class="mb-0">Filter Activity Log</h2>
            <button type="button" class="btn-text close-popup"><img src="{% static 'img/icon-close.svg' %}" alt="close"></button>
        </div>

        <div class="form-group w-100 mt-4">
            <label for="filter-by" class="w-100 text-start">Filter by</label>
            <form hx-get="{% url 'system_management:activityLog' %}" hx-trigger="submit" hx-swap="outerHTML" hx-target="#activity-container">
                <div class="wrapper-select">
                    <select id="filter-by" class="form-select-solid form-control" name="filter-by" hx-get="{% url 'system_management:filterLog' %}" hx-trigger="load,htmx-change" hx-swap="innerHTML" hx-target=".content-filter-result">
                        <option value="timestamp">Timestamp</option>
                        <option value="lucode">LU Code</option>
                        <option value="email">Email</option>
                        <option value="department">Department</option>
                        <option value="module">Module</option>
                        <option value="action">Action</option>
                    </select>
                </div>
                <div class="content-filter-result mt-4"></div>
                <div class="d-flex mt-4 justify-content-start gap-2 align-items-center">
                    <button type="submit" class="btn btn-primary">Apply</button>
                    <button type="button" class="btn-text text-danger close-popup">Cancel</button>
                </div>
                
            </form>
           <script>
            // Add event listener
            var selectElement = document.getElementById('filter-by');
            selectElement.addEventListener('change', function(event) {
                selectElement.dispatchEvent(new Event('htmx-change'));
            });
           </script>
        </div>
    </c-popup>
</div>
    
{% if message %}
<div class="popup popup-wide">
    <div class="backdrop"></div>
        <div class="content-popup content-popup-upload">
            <h2>Warning!</h2>                           
            <img src="{% static 'img/sassy-sad.png' %}" alt="sassy sad">
            <p class="text-danger">{{ message }}</p>
            <button class="btn btn-primary close-popup mt-4">Okay!</button>
        </div>
    </div> 
{% endif %}






