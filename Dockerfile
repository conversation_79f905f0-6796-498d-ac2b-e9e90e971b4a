# Use Python 3.12.4 image based on Debian Bullseye in its slim variant as the base image
FROM public.ecr.aws/docker/library/python:3.12-bullseye

LABEL maintainer="<PERSON>ica<PERSON><PERSON>"
# Set an environment variable to unbuffer Python output, aiding in logging and debugging
ENV PYTHONBUFFERED=1

# Define an environment variable for the web service's port, commonly used in cloud services
ENV PORT=8080

# Set the working directory within the container to /app for any subsequent commands
WORKDIR /app

# Copy the entire current directory contents into the container at /app
COPY . /app/

# Copy the .env file into the container (make sure this step is done after it is created)
COPY .env /app/.env

# Upgrade pip to ensure we have the latest version for installing dependencies
RUN pip install --upgrade pip

# Install PostgreSQL client for database backup operations
RUN apt-get update \
    && apt-get install -y curl ca-certificates gnupg \
    && curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc | gpg --dearmor -o /usr/share/keyrings/postgresql-archive-keyring.gpg \
    && echo "deb [signed-by=/usr/share/keyrings/postgresql-archive-keyring.gpg] http://apt.postgresql.org/pub/repos/apt/ bullseye-pgdg main" > /etc/apt/sources.list.d/pgdg.list \
    && apt-get update \
    && apt-get install -y postgresql-client-16

# Install dependencies from the requirements.txt file to ensure our Python environment is ready
RUN pip install -r requirements.txt

# Set the working directory to /app/sass
WORKDIR /app/sass

# Accept build arguments for database configuration
ARG DB_NAME
ARG DB_USER
ARG DB_PASS
ARG DB_HOST
ARG DB_PORT
ARG SYSTEM_RUN_LOCATION
ARG AZURE_CLIENT_ID
ARG AZURE_CLIENT_SECRET
ARG AZURE_TENANT_ID

# Set environment variables from build arguments
ENV DB_NAME=${DB_NAME}
ENV DB_USER=${DB_USER}
ENV DB_PASS=${DB_PASS}
ENV DB_HOST=${DB_HOST}
ENV DB_PORT=${DB_PORT}
ENV SYSTEM_RUN_LOCATION=${SYSTEM_RUN_LOCATION}
ENV AZURE_CLIENT_ID=${AZURE_CLIENT_ID}
ENV AZURE_CLIENT_SECRET=${AZURE_CLIENT_SECRET}
ENV AZURE_TENANT_ID=${AZURE_TENANT_ID}

# Collect static files
RUN python manage.py collectstatic --no-input

# Database Makemigrations
RUN python manage.py makemigrations

# Database Migrations
RUN python manage.py migrate

# Set the command to run our web service using Daphne, binding it to 0.0.0.0 and the PORT environment variable
CMD daphne -b 0.0.0.0 -p $PORT sass.asgi:application
# RUN python manage.py runserver

# Inform Docker that the container listens on the specified network port at runtime
EXPOSE ${PORT}
