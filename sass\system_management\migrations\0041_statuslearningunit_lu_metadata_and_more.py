# Generated by Django 5.1 on 2024-11-05 05:21

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("system_management", "0040_rename_departments_learningunit_department"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="statuslearningunit",
            name="lu_metadata",
            field=models.J<PERSON><PERSON>ield(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="statuslearningunit",
            name="lu",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="system_management.learningunit",
            ),
        ),
        migrations.AlterField(
            model_name="statuslearningunit",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
