from django.conf import settings
from django.contrib import admin
from django.urls import include, path
from django.conf.urls.i18n import i18n_patterns
from django.conf.urls.static import static
from .mediaControl import serve_media

urlpatterns = i18n_patterns(
    path("admin/", admin.site.urls),
    path('', include('sass_app.urls', namespace='app')),
    path('', include('schedule_assessment.urls')),
    path('', include('system_management.urls')),
    path('', include('report.urls')),
    path('', include('live_schedule_status.urls')),

    # System management
    # internationalizations
    path('i18n/', include('django.conf.urls.i18n')),
    
    path('serve/<path:path>', serve_media,name='server_media'),

    prefix_default_language=False
)

urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

handler500 = 'sass.errorHandler.handler500'
handler404 = 'sass.errorHandler.handler404'