
from schedule_assessment.models import ScheduleAssessment
from system_management.models import StatusLearningUnit
from django.db.models.signals import pre_save,post_save
from django.dispatch import receiver
from sass_app.email import *
from django.contrib.auth.models import User

from easyaudit.models import CRUDEvent
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
from django.utils.dateparse import parse_datetime
import json
from utility import is_valid_email



@receiver(post_save, sender=CRUDEvent)
def handle_crud_events_for_changing_schedule_assessment(sender, instance, created, **kwargs):
    # Check if it's for ScheduleAssessment
    
    content_type = ContentType.objects.get_for_model(ScheduleAssessment)
    if instance.content_type == content_type:
        
        if instance.event_type == CRUDEvent.CREATE:
           
            schedule = instance.content_type.get_object_for_this_type(pk=instance.object_id)
            if not schedule.owned_by:schedule.owned_by=schedule.updated_by

            if (schedule.owned_by != schedule.updated_by) and schedule.updated_by.role.type == 'admin':
                overide_sass_email_template = OVERIDE_SASS_EMAIL_TEMPLATE.format(name=schedule.owned_by.first_name,
                                                                                admin_name=schedule.updated_by.first_name,
                                                                                timestamp=timezone.localtime(schedule.updated_at, timezone=timezone.get_current_timezone()).strftime("%d-%b-%Y %H:%M"),
                                                                                lu_code=schedule.lu.code,
                                                                                action="Create",
                                                                                change_value="Create Object",
                                                                                support_email=get_SUPPORT_EMAIL())
 
                payload={
                        'subject':OVERIDE_SASS_EMAIL_TEMPLATE_TITLE,
                        'message': dedent(overide_sass_email_template).strip(),
                        'destination_email':[schedule.owned_by.email]
                    }

                send_text_email_bg_jobs(payload=payload)
            
        elif instance.event_type == CRUDEvent.UPDATE:

            # Parse the JSON string from TextField
            data = json.loads(instance.changed_fields)
            # Clean and exclude broken rules/failed conditions
            cleaned_data = {}
            for key, val in data.items():

                if key in ['metadata', 'updated_by','assessment_task']:
                    continue

                # Skip if key contains 'broken_rules' or is 'failed_conditions'
                if not key.startswith('broken_rules') and key != 'failed_conditions':
                    if val not in (["", "None"], None, "", "None", [], ["None"]):
                        # Handle list values
                        if isinstance(val, list):
                            cleaned_val = [v for v in val if v and v != "None"]
                            if cleaned_val:
                                cleaned_data[key] = cleaned_val
                        # Handle string and other values
                        elif val:
                            cleaned_data[key] = val

            if not cleaned_data:cleaned_data=None

            if cleaned_data:
                cleaned_data = {k: v for k, v in cleaned_data.items() if 'original' not in k}
                schedule = instance.content_type.get_object_for_this_type(pk=instance.object_id)
                if not schedule.owned_by:schedule.owned_by=schedule.updated_by
                if (schedule.owned_by != schedule.updated_by) and schedule.updated_by.role.type == 'admin':
                    overide_sass_email_template = OVERIDE_SASS_EMAIL_TEMPLATE.format(name=schedule.owned_by.first_name,
                                                                                    admin_name=schedule.updated_by.first_name,
                                                                                    timestamp=timezone.localtime(schedule.updated_at, timezone=timezone.get_current_timezone()).strftime("%d-%b-%Y %H:%M"),
                                                                                    lu_code=schedule.lu.code,
                                                                                    action="Update",
                                                                                    change_value=cleaned_data,
                                                                                    support_email=get_SUPPORT_EMAIL())
   
                    payload={
                        'subject':OVERIDE_SASS_EMAIL_TEMPLATE_TITLE,
                        'message': dedent(overide_sass_email_template).strip(),
                        'destination_email':[schedule.owned_by.email]
                    }
                    send_text_email_bg_jobs(payload=payload)
            
                
        elif instance.event_type == CRUDEvent.DELETE:

            lu_code=None
            instance.object_json_repr = json.loads(instance.object_json_repr)
            metadata = instance.object_json_repr[0]['fields']
            if 'original_lu_code' in metadata:
                lu_code = metadata['original_lu_code']

            updated_at=None
            metadata = instance.object_json_repr[0]['fields']
            if 'updated_at' in metadata:
                updated_at = parse_datetime(metadata['updated_at'])  # This returns timezone-aware datetime

            updated_by=None
            metadata = instance.object_json_repr[0]['fields']
            if 'updated_by' in metadata:
                updated_by = User.objects.filter(id=metadata['updated_by']).first()
            
            owned_by=None
            metadata = instance.object_json_repr[0]['fields']
            if 'owned_by' in metadata:
                owned_by = User.objects.filter(id=metadata['owned_by']).first()
            
            if updated_by and owned_by:
                if (owned_by != updated_by) and updated_by.role.type == 'admin':
                    overide_sass_email_template = OVERIDE_SASS_EMAIL_TEMPLATE.format(name=owned_by.first_name,
                                                                                    admin_name=updated_by.first_name,
                                                                                    timestamp=timezone.localtime(updated_at, timezone=timezone.get_current_timezone()).strftime("%d-%b-%Y %H:%M"),
                                                                                    lu_code=lu_code,
                                                                                    action="Delete",
                                                                                    change_value="Object Deleted",
                                                                                    support_email=get_SUPPORT_EMAIL())
    
                    payload={
                        'subject':OVERIDE_SASS_EMAIL_TEMPLATE_TITLE,
                        'message': dedent(overide_sass_email_template).strip(),
                        'destination_email':[owned_by.email]
                    }

                    send_text_email_bg_jobs(payload=payload)



@receiver(pre_save, sender=StatusLearningUnit)
def check_ica_status_change(sender, instance, **kwargs):
    if instance.pk:  # Check if existing instance
        old_instance = sender.objects.filter(pk=instance.pk).first()

        flag_send_email=False
        if not old_instance and instance:
            flag_send_email=True
        elif old_instance and instance:
            if old_instance.status != instance.status:
                flag_send_email=True

        if not instance.updated_by:flag_send_email=False
        
        if flag_send_email:
            destination_email = []

            if instance.updated_by.role.type == 'admin':
            
                unit_leader_email = instance.lu.unit_leader
                if unit_leader_email:
                    if is_valid_email(unit_leader_email):
                        if User.objects.filter(email=unit_leader_email,registered_user__status='active').exists():
                            user = User.objects.filter(email=unit_leader_email,registered_user__status='active').first()
                            destination_email.append({'email':unit_leader_email,'name':user.first_name})

                department_owner_email = instance.lu.department.owner
                if department_owner_email:
                    if is_valid_email(department_owner_email):
                        if User.objects.filter(email=department_owner_email,registered_user__status='active').exists():
                            user = User.objects.filter(email=department_owner_email,registered_user__status='active').first()
                            destination_email.append({'email':department_owner_email,'name':user.first_name} )

                if destination_email:
                    for dest_email in destination_email:
                        user_email_template = LU_STATUS_CHANGE_EMAIL_TEMPLATE.format(name=dest_email['name'],
                                                                                    admin_name=instance.updated_by.first_name,
                                                                                    timestamp=timezone.localtime(instance.updated_at, timezone=timezone.get_current_timezone()).strftime("%d-%b-%Y %H:%M"),
                                                                                    lu_code=instance.lu.code,
                                                                                    lu_status=instance.status.title(),
                                                                                    support_email=get_SUPPORT_EMAIL())
                        
                        payload={
                            'subject':LU_STATUS_CHANGE_EMAIL_TITLE,
                            'message': dedent(user_email_template).strip(),
                            'destination_email':[dest_email['email']]
                        }
                        
                        send_text_email_bg_jobs(payload=payload)
                    
