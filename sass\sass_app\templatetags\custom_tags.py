
import re
import json
from django import template
from system_management.models import *
from schedule_assessment.models import *
from sass_app.models import *

from easyaudit.models import CRUDEvent
from django.contrib.contenttypes.models import ContentType
from django.db.models import Sum
from business_rules import export_rule_data
from schedule_assessment.rule import *
from django.utils.html import format_html,format_html_join,escape
from utility import find_lu_course_group
from collections import defaultdict
register = template.Library()


@register.filter()
def get_file_object(file_type: str):
    file_upload = FileUpload.objects.filter(
        type=file_type).order_by('-created_at').first()

    return file_upload


@register.simple_tag()
def get_total_weightage(learning_unit):

    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
    schedule_assements = ScheduleAssessment.objects.filter(
        week__isnull=False,
        lu=learning_unit, 
        current_semester=SYSTEM_CURRENT_YEAR.current_semester, 
        current_year=SYSTEM_CURRENT_YEAR.current_year)

    total_weightage = schedule_assements.aggregate(
        total_weightage=Sum('weightage')
    )['total_weightage'] or 0

    return total_weightage


@register.simple_tag()
def get_total_weightage_test(learning_unit):
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
    schedule_assements = ScheduleAssessmentTest.objects.filter(
        week__isnull=False,
        lu=learning_unit, current_semester=SYSTEM_CURRENT_YEAR.current_semester, current_year=SYSTEM_CURRENT_YEAR.current_year)

    total_weightage = schedule_assements.aggregate(
        total_weightage=Sum('weightage')
    )['total_weightage'] or 0

    return total_weightage


@register.simple_tag()
def get_tooltip(slug):
    tooltip = ToolTip.objects.filter(slug=slug).first()
    
    if tooltip and tooltip.content:
        # First escape the content to prevent XSS
        safe_content = escape(tooltip.content)
        
        # Format with line breaks safely
        formatted_content = format_html(
            # Replace newlines with <br> and preserve spaces
            safe_content.replace('\n', '<br>').replace(' ', '&nbsp;')
        )
        
        return formatted_content
        
    return ""

@register.filter()
def get_lu_code(value):
    try:
        luname = ""
        match = re.search(r'Lu\s+(.*?)-', value)
        if match:
            luname = match.group(1).strip()
        else:
            luname = value

        lu = LearningUnit.objects.filter(name=f'{luname}').first()
        if lu:
            return lu.code
        return ''
    except (TypeError, AttributeError):
        return ''
    # content_type = crud_event.content_type
    # if crud_event.event_type == CRUDEvent.DELETE:
    #     # For deleted objects, we can only access the serialized data
    #     return ''

    # try:
    #     # Get the actual model class
    #     model_class = content_type.model_class()
    #     # Get the object instance
    #     if crud_event.object_id:
    #         if model_class.objects.filter(pk=crud_event.object_id).exists():
    #             changed_object = model_class.objects.get(
    #                 pk=crud_event.object_id)
    #         else:
    #             changed_object = None

    #         if changed_object:
    #             try:
    #                 field_names = [
    #                     field.name for field in changed_object._meta.fields]
    #                 if 'lu' in field_names:
    #                     return changed_object.lu.code
    #                 elif 'code' in field_names and model_class.__name__.lower() == 'learningunit':
    #                     return changed_object.code

    #             except AttributeError:

    #                 lu = LearningUnit.objects.get(name=crud_event.object_repr)
    #                 return lu.code
    #             except Exception as e:
    #                 print(f"Error occurred: {str(e)}")

    #     return ''

    # except Exception as e:
    #     print("get_lu_code error: ", e)
    #     return ''


@register.filter()
def get_department(crud_event):

    try:
        luname = ""
        match = re.search(r'Lu\s+(.*?)-', crud_event)
        if match:
            luname = match.group(1).strip()
        else:
            luname = crud_event

        lu = LearningUnit.objects.filter(name=f'{luname}').first()
        if lu:
            return lu.department
        return ''
    except (TypeError, AttributeError):
        return ''
    # content_type = crud_event.content_type

    # if crud_event.event_type == CRUDEvent.DELETE:
    #     # For deleted objects, we can only access the serialized data
    #     return ''

    # try:
    #     # Get the actual model class
    #     model_class = content_type.model_class()

    #     # Get the object instance
    #     changed_object = model_class.objects.get(pk=crud_event.object_id)

    #     field_names = [field.name for field in changed_object._meta.fields]

    #     if 'lu' in field_names:
    #         return changed_object.lu.department.name
    #     elif 'code' in field_names and model_class.__name__.lower() == 'learningunit':
    #         return changed_object.department

    #     return ''
    # except Exception as e:
    #     print("get_department error: ", e)
    #     return ''


@register.inclusion_tag('components/condition_node.html')
def render_condition_node(condition, level=0, loop_index=0):
    subcondition_length = len(condition.subconditions) if hasattr(
        condition, 'subconditions') else 0
    business_rule = export_rule_data(
        ScheduleAssessmentVariables, ScheduleAssessmentActions)
    return {
        'business_rule': business_rule,
        'condition': condition,
        'level': level,
        'loop_index': loop_index,
        'subcondition_count': subcondition_length,
        'indent': level * 20  # pixels for indentation
    }


@register.filter
def is_condition(condition):
    return isinstance(condition, dict) and 'name' in condition


@register.filter
def replace_underscore(value):
    """Convert underscore operator names to readable format"""
    return value.replace('_', ' ').title()


@register.filter
def get_status(learning_unit: LearningUnit, SYSTEM_CURRENT_YEAR: SystemCurrentYear):
    status_unit = StatusLearningUnit.objects.filter(
        lu=learning_unit, current_year=SYSTEM_CURRENT_YEAR.current_year, current_semester=SYSTEM_CURRENT_YEAR.current_semester).first()
    return status_unit


@register.filter
def get_year_of_study(learning_unit: LearningUnit):
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
    year_studies = CourseLuMap.objects.filter(academic_year=SYSTEM_CURRENT_YEAR.current_year,
                                              semester=SYSTEM_CURRENT_YEAR.current_semester,
                                              lu=learning_unit).distinct().values_list('year_study', flat=True).order_by('year_study')

    return year_studies


@register.simple_tag
def get_valid_week(schedule_assessment, learning_unit: LearningUnit):
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()

    valid_weeks = []
    model_class = schedule_assessment.__class__  # switch between normal and test

    reserved_weeks = model_class.objects.filter(
        current_year=SYSTEM_CURRENT_YEAR.current_year, current_semester=SYSTEM_CURRENT_YEAR.current_semester, lu=learning_unit, week__isnull=False).values_list('week', flat=True)
    reserved_weeks = list(reserved_weeks)

    if schedule_assessment.week in reserved_weeks:
        reserved_weeks.remove(schedule_assessment.week)

    academicWeek = AcademicWeek.objects.filter(
        lutype=learning_unit.lu_type, coursetype=learning_unit.course_type).first()

    if academicWeek:
        weeks = academicWeek.weeks
        for week in range(weeks['start'], weeks['end'] + 1):
            if str(week) not in weeks['status'] and not week in reserved_weeks:
                valid_weeks.append({
                    'week': week,
                    'date': get_date_by_week(week, learning_unit)
                })

    return valid_weeks


@register.simple_tag
def get_weeks_of_max_ica(learning_unit: LearningUnit):
    week_exclude_by_max_ica_count = []

    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
    academic_week = AcademicWeek.objects.get(
        coursetype=learning_unit.course_type, 
        lutype=learning_unit.lu_type
    )
    
    if not academic_week:
        return []

    start, end = academic_week.weeks["start"], academic_week.weeks["end"]
    # weeks_range = range(start, end + 1)

    # filter_conditions=Q(
    #     lu__isnull=False,
    #     week__in=weeks_range,
    #     current_year=SYSTEM_CURRENT_YEAR.current_year,
    #     current_semester=SYSTEM_CURRENT_YEAR.current_semester)
    
    # # Get all ICAs for the date range in a single query
    # all_icas = ScheduleAssessment.objects.filter(
    #     filter_conditions
    # ).select_related('lu').order_by('week')

    # ica_count_in_week = {}
    
    # # Group ICAs by week
    # icas_by_week = defaultdict(list)
    # for ica in all_icas:
    #     icas_by_week[ica.week].append(ica)

    # for week in weeks_range:
    #     ica_count = {}
    #     # checked_course_groups = set()  # Using set for faster lookups
    #     available_lu_course_groups=[]

    #     for ica in icas_by_week[week]:

    #         if ica.lu.code in available_lu_course_groups:
    #             continue

    #         lu_codes_course_group = find_lu_course_group(ica.lu.code)

    #         # safety
    #         available_lu_course_groups.extend(lu_codes_course_group)

    #         count_ica = ScheduleAssessment.objects.filter(lu__code__in=lu_codes_course_group,
    #                                             week=week,
    #                                             current_year=SYSTEM_CURRENT_YEAR.current_year,
    #                                             current_semester=SYSTEM_CURRENT_YEAR.current_semester).order_by("-created_at").count()
            
    #         # Update counts for each course in the group
    #         for lu_code in lu_codes_course_group:
    #             ica_count[lu_code] = ica_count.get(lu_code, 0) + count_ica
        
    #     ica_count_in_week[week] = ica_count

    # weeks = academic_week.weeks
    # for week in range(weeks['start'], weeks['end'] + 1):
    #     if week in ica_count_in_week:
    #         if learning_unit.code in ica_count_in_week[week]:
    #             if academic_week.maxicacountperweek:
    #                 if ica_count_in_week[week][learning_unit.code] >= academic_week.maxicacountperweek:
    #                     week_exclude_by_max_ica_count.append(week)

    if academic_week.maxicacountperweek:
        lu_codes_course_groups = find_lu_course_group(learning_unit.code)
        
        for week in range(start,end + 1):
            count_ica=0
            for lu_codes_course_group in lu_codes_course_groups:
                count_year_ica = ScheduleAssessment.objects.filter(lu__code__in=lu_codes_course_group['lu_codes'],
                                                            week=week,
                                                            current_year=SYSTEM_CURRENT_YEAR.current_year,
                                                            current_semester=SYSTEM_CURRENT_YEAR.current_semester).order_by("-created_at").count()

                if count_year_ica>=count_ica:
                    count_ica=count_year_ica

            if count_ica >= academic_week.maxicacountperweek:
                week_exclude_by_max_ica_count.append(week)

    return week_exclude_by_max_ica_count



@register.filter
def check_dict(data):
    if type(data) == dict:
        data = list(data.keys())[0]
    return data


@register.filter
def preprocess_broken_rules_log(schedule_assessment: ScheduleAssessment, stage: str = 'general'):
    if stage == 'stage1':
        broken_rules = schedule_assessment.broken_rules_stage1['failed_conditions']
    elif stage == 'stage2':
        broken_rules = schedule_assessment.broken_rules_stage2['failed_conditions']
    else:
        broken_rules = schedule_assessment.broken_rules['failed_conditions']

    new_broken_rules = []
    for idx, broken_rule in enumerate(broken_rules):
        if type(broken_rule) == dict:
            if broken_rule['name'] == 'assessment_method':
                if schedule_assessment.assessment_method.slug == broken_rule['expected']:
                    new_broken_rules.append(broken_rule)
            else:
                if 'group_conditions' in broken_rule:
                    flag = False
                    for group_condition in broken_rule['group_conditions']:
                        if group_condition['name'] == 'assessment_method':
                            if schedule_assessment.assessment_method.slug == group_condition['value']:
                                new_broken_rules.append(broken_rule)
                            flag = True

                    if flag == False:
                        new_broken_rules.append(broken_rule)

    return new_broken_rules


@register.filter
def count_none_weeks(schedule_assessments):
    return sum(1 for assessment in schedule_assessments if assessment.week is None)


@register.filter
def clean_changed_fields(activity_log:CRUDEvent):
    """
    Clean empty values and exclude all broken_rules related fields
    """
    try:


        value = activity_log.changed_fields

        if not value or value == "":
            return "{}"
        

        # Parse the JSON string from TextField
        data = json.loads(value)
        
        if activity_log.content_type.model == 'role':  # This checks the model name
            
            Role = activity_log.content_type.model_class()
        
            # Get the specific Role instance using object_id
            role = Role.objects.get(id=activity_log.object_id)
            
            # Now you can get the user
            user = role.user
            if user:
                data['user'] = user.username

        # Clean and exclude broken rules/failed conditions
        cleaned_data = {}
        for key, val in data.items():

            if 'metadata' in key:
                continue
 
            if type(val) == list:
                if len(val)>=2:
                    val = f"{val[0]} -> {val[1]}" 
            cleaned_data[key] = val

        json_string = json.dumps(cleaned_data, indent=2)
        return format_html('<pre>{}</pre>', json_string)

    except json.JSONDecodeError:
        return "{}"

@register.filter(is_safe=True)
def clean_text(value):
    if not value:
        return ""
    
    # Split into paragraphs
    paragraphs = value.split("<br>")
    
    # Common HTML entities to clean
    entities = {
        "&nbsp;": " ",
        "&quot;": "'",
        "&apos;": "'",
        "&lt;": "<",
        "&gt;": ">",
        "&amp;": "&",
        "&#39;": "'",
        "&#34;": "'",
        "&ldquo;": "'",
        "&rdquo;": "'",
        "&lsquo;": "'",
        "&rsquo;": "'"
    }
    
    # Clean each paragraph
    cleaned_paragraphs = []
    for paragraph in paragraphs:
        # Clean each entity
        cleaned = paragraph
        for entity, replacement in entities.items():
            cleaned = cleaned.replace(entity, replacement)
            
        # Clean whitespace using string methods
        cleaned = ' '.join(cleaned.split())
        
        if cleaned:
            # Use format_html for safe HTML construction
            cleaned_paragraphs.append(
                format_html("<p>{}</p>", cleaned)
            )
    
    # Join paragraphs safely
    value = format_html_join("\n", "{}", ((p,) for p in cleaned_paragraphs))
    
    return value

@register.filter
def rule_error_cleanup_message(string:str):
    try:
        if string:
            string = string.replace("_", " ")
            string = string.title()
            return string
    except Exception as e:
        print("rule_error_cleanup_message error: ", e)
    return string
