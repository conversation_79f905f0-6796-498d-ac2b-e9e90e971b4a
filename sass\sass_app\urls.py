from django.urls import path
from . import views, account, api

app_name = 'sass_app'

urlpatterns = [
    path('', views.main, name='main'),
    path('heatmap/', views.heatmap, name='heatmap'),
    path('heatmap_container/', views.heatmap_container, name='heatmap_container'),
    path('login', account.login_view, name='login'),
    path('login/submit', account.login_submit, name='login'),
    path('logout', account.logout_submit, name='logout'),
    
    path('health', views.health, name='health'),

    # redirect
    path('microsoft-auth', account.microsoft_callback, name='microsoft_callback'),

    path('get-activity-log/<uuid:id>',views.get_activity_log,name="getActivityLog"),

    #API
    path('check-ms-connection/', api.check_microsoft_connection, name='check_ms_connection'),
    path('users/', api.users, name='users'),
    path('user/manage', api.manage_user, name='create_user'),
    path('user/manage/<int:user_id>', api.manage_user, name='manage_user'),
    path('user/delete/<int:user_id>', api.delete_user, name='delete_user'),

    path('api/run-migrations/', api.run_migrations, name='run_migrations'),
    path('api/explore-directory/', api.explore_directory, name='explore_directory'),
    path('api/delete/file', api.delete_file, name='delete_file'),
    path('api/test-s3-connection', api.test_s3_connection, name='test_s3_connection'),
    path('api/upload', api.upload_file, name='upload_file'),
    path('api/s3_upload_v2', api.s3_upload_v2, name='s3_upload_v2'),

    path('api/get_file', api.get_file, name='get_file'),
    path('api/delete_file_s3', api.delete_file_s3, name='delete_file_s3'),
    path('api/run-dbbackup/', api.run_dbbackup, name='run_dbbackup'),
    path('api/list-s3-files/', api.list_s3_files, name='list_s3_files'),
    path('api/serve/<path:path>', api.serve_api_media, name='serve_api_media')
    
    
]