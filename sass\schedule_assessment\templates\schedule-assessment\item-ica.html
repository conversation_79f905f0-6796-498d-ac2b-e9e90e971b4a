{% load static %}
{% load custom_tags %}


<div id="form-{{schedule_assessment.id}}" 
    hx-trigger="change from:#form-{{schedule_assessment.id}} :input" 
    hx-swap='none' 
    hx-post="{% url 'schedule_assessment:update_ica_schedule' %}" 
    class="input"> 

    {% csrf_token %}

    
    <div class="card item-ica-card item-ica-card-form mb-4">
        {% if test_mode %}<input type="hidden" name="test_mode" value="{{test_mode}}" />{% endif %}
        <input type="hidden" name="schedule_assessment" value="{{schedule_assessment.id}}" />
        <input type="hidden" name="moderation_method" value="{{ schedule_assessment.moderation_method }}"/>
        <input type="hidden" name="duration" value="{{ schedule_assessment.duration }}"/>
        <input type="hidden" name="assessment_type" value="{{ schedule_assessment.assessment_type }}"/>     
        <input type="hidden" name="learning_unit_id" value="{{learning_unit.id}}"></input>  

        <div class="d-flex justify-content-between align-items-center">
            <h2 class="title-card {% if test_mode %}title-card-test{% endif %}">ICA <span class="number-ica"></span> <span class="value-weightage">(0%)</span></h2>
            <button data-url="{% url 'schedule_assessment:delete_ica' schedule_assessment.id %}" type="button" class="btn-text show-confirm-delete-popup"><img src="{% static 'img/icon-trash.svg' %}" alt="trash"></button>
        </div>
        
        <div class="row-form-ica outer-ica-schedule">
            <div class="form-group">
                <label class="text-gray has-tooltips">Week
                    <div class="tooltips">
                        <img src="{% static 'img/icon-info.svg' %}" alt="info">
                        <div class="tooltips-content ">

                            {% get_tooltip 'ica-week' as tooltips %}
                            
                            <p>{{tooltips|clean_text}}</p>

                        </div>
                    </div>
                </label>
                <div class="wrapper-select">
                    <select id="week-{{schedule_assessment.id}}" name="week" id="week" class="form-control week" 
                    {% comment %} onchange='assm_methodSubmit(document.getElementById("assm-method-{{schedule_assessment.id}}"))' {% endcomment %}
                    >
                        <option value="" >Week</option>
                        {% get_valid_week schedule_assessment learning_unit as valid_weeks %}
                        {% for valid_week in valid_weeks %}
                            {% if not valid_week.week in weeks_of_max_ica or schedule_assessment.week in weeks_of_max_ica %}    
                            <option value="{{valid_week.week}}">{{valid_week.week}} ({{valid_week.date|date:"d-M-Y"}}) </option>
                            {% endif %}
                        {% endfor %}
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label class="text-gray has-tooltips">Assm. Weightage
                    <div class="tooltips">
                        <img src="{% static 'img/icon-info.svg' %}" alt="info">
                        <div class="tooltips-content align-right">

                            {% get_tooltip 'ica-assm-weightage' as tooltips %}
                            
                            <p>{{tooltips|clean_text }}</p>

                        </div>
                    </div>
                </label>
                <div class="form-ica-schedule wrapper-percent">
                    <input type="number" name="weightage" class="form-control input-weightage" placeholder="25" inputmode="numeric" oninput="this.value = this.value.replace(/\D+/g, '')" />                
                </div>
                
            </div>
            <div class="form-group">
                <label class="text-gray has-tooltips">Over 2 weeks
                    <div class="tooltips">
                        <img src="{% static 'img/icon-info.svg' %}" alt="info">
                        <div class="tooltips-content align-right">

                            {% get_tooltip 'ica-over2week' as tooltips %}
                            
                            <p>{{tooltips|clean_text}}</p>

                        </div>
                    </div>
                </label>
                <div class="wrapper-select">
                    <select name="assm-2-weeks" class="form-control">
                        <option value="">--</option>
                        <option value="yes">Yes</option>
                        <option value="no">No </option>                    
                    </select>
                </div>
            </div>
            
            
            <div class="form-group">
                <label class="text-gray has-tooltips">Assm. Method
                    <div class="tooltips">
                        <img src="{% static 'img/icon-info.svg' %}" alt="info">
                        <div class="tooltips-content align-right">

                            {% get_tooltip 'ica-assm-method' as tooltips %}
                            
                            <p>{{tooltips|clean_text}}</p>

                        </div>
                    </div>
                </label>
                <div class="wrapper-select">
                    <select name="assm-method" id="assm-method-{{schedule_assessment.id}}" class="form-control">
                        {% for assessement in assessements %}
                        <option value="{{assessement.slug}}">{{assessement.method}}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>
        

        <div id="messasge-specific-{{schedule_assessment.id}}"></div>

    </div>
</div>

<script>
    jQuery(document).ready(function ($) {
        let selectElement = document.getElementById('assm-method-{{schedule_assessment.id}}');
        assm_methodSubmit(selectElement);
    });
</script>