from django.shortcuts import render
import traceback
from sass_app.middleware import ErrorLoggingMiddleware 

def handler500(request, exception=None):

    try:
        raise ValueError(str(traceback.format_exc()))
    except Exception as e: #catched by Error Log MiddleWare
        ErrorLoggingMiddleware.log(error=e,request=request)      

    context = {}  # You can add any context data you want to pass to template
    return render(request, '500.html', context, status=500)


def handler404(request, exception=None):
    context = {}  # You can add any context data you want to pass to template
    return render(request, '404.html', context, status=404)
