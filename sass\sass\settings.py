from pathlib import Path
from django.utils.translation import gettext_lazy as _
import os
import environ
import uuid
env = environ.Env()


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent
PAREMT_DIR = os.path.dirname(BASE_DIR)

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/
env.read_env(env_file=str(PAREMT_DIR)+'/.env')

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-h$hbh(vn-eg^nde0y(6v15(%55i6f+$d)imo!b5crx7^b(+fx7"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ["*"]

# Deployment ID for logging purpose
DEPLOYMENT_ID=str(uuid.uuid4())

CSRF_COOKIE_HTTPONLY = True
SESSION_COOKIE_HTTPONLY = True

SYSTEM_RUN_LOCATION = env('SYSTEM_RUN_LOCATION')
AZURE_CLIENT_ID = env('AZURE_CLIENT_ID')
AZURE_CLIENT_SECRET = env('AZURE_CLIENT_SECRET')
AZURE_TENANT_ID = env('AZURE_TENANT_ID')


AZURE_AUTHORITY = f"https://login.microsoftonline.com/{AZURE_TENANT_ID}"
AZURE_SCOPES = ["user.read", "mailboxsettings.read", "calendars.readwrite"]
AZURE_REDIRECT = "http://localhost:8000/microsoft-auth"
AZURE_LOGOUT = f"https://login.microsoftonline.com/{AZURE_TENANT_ID}/oauth2/v2.0/logout"
AZURE_SSL_VERIFICATION=False  # Disable SSL verification


AWS_STORAGE_BUCKET_NAME = 'sass-data'

if SYSTEM_RUN_LOCATION == 'PROD':
    AWS_STORAGE_BUCKET_NAME = 'sass-data'
    DEBUG = False
    CSRF_COOKIE_SECURE = True
    SESSION_COOKIE_SECURE = True
    AZURE_REDIRECT = "https://sass-dev.deeeplabs.com/microsoft-auth"

elif SYSTEM_RUN_LOCATION == 'CSTACK-PROD':
    DEBUG = False
    AWS_STORAGE_BUCKET_NAME = 't-prd-sass-s3-bucket-prd'
    CSRF_COOKIE_SECURE = True
    SESSION_COOKIE_SECURE = True
    AZURE_REDIRECT = "https://nyp-sbm-sass-app.e01.app.gov.sg/microsoft-auth"

elif SYSTEM_RUN_LOCATION == 'CSTACK':
    AWS_STORAGE_BUCKET_NAME = 't-stg-sass-s3-bucket'
    DEBUG = False
    CSRF_COOKIE_SECURE = True
    SESSION_COOKIE_SECURE = True
    AZURE_REDIRECT = "https://nyp-sbm-sass-app.stg.e01.app.gov.sg/microsoft-auth"
 
# CSRF_COOKIE_SECURE = True  # for HTTPS
CSRF_TRUSTED_ORIGINS = ['https://sass-dev.deeeplabs.com',
                        'https://nyp-sbm-sass-app.stg.e01.app.gov.sg',
                        'https://nyp-sbm-sass-app.e01.app.gov.sg',
                        'https://sass.nyp.edu.sg']

# Application definition
INSTALLED_APPS = [
    "daphne",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # 'storages',  # Add django-storages
    'dbbackup',
    'easyaudit',
    'mathfilters',
    'sass_app',
    'channels',
    'corsheaders',
    'django_cotton',
    'system_management',
    'schedule_assessment',
    'report',
    'live_schedule_status',
]


APPEND_SLASH = True  # Ensures URLs without a trailing slash get redirected if a match exists with a slash
MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    'whitenoise.middleware.WhiteNoiseMiddleware',
    "django.contrib.sessions.middleware.SessionMiddleware",
    'django.middleware.locale.LocaleMiddleware',
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    'easyaudit.middleware.easyaudit.EasyAuditMiddleware',

    'corsheaders.middleware.CorsMiddleware',  # prevent error due CORS rules

    'sass_app.middleware.ErrorLoggingMiddleware',  # Add middleware here
]
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
ROOT_URLCONF = "sass.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / 'templates'],
        "APP_DIRS": False,  # updated
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
                'sass_app.custom_context_processor.custom_list',  # custom_list

            ],
            "loaders": [(
                "django.template.loaders.cached.Loader",
                [
                    "django_cotton.cotton_loader.Loader",
                    "django.template.loaders.filesystem.Loader",
                    "django.template.loaders.app_directories.Loader",
                ],
            )],
            "builtins": [
                "django_cotton.templatetags.cotton"
            ],

        },
    },
]

WSGI_APPLICATION = "sass.wsgi.application"
ASGI_APPLICATION = 'sass.asgi.application'


SESSION_COOKIE_AGE = 28800
# Set SESSION_SAVE_EVERY_REQUEST to True to extend the session expiration with each request
SESSION_SAVE_EVERY_REQUEST = True


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': env('DB_NAME'),
        'USER': env('DB_USER'),
        'PASSWORD': env('DB_PASS'),
        'HOST': env('DB_HOST'),
        'PORT': env('DB_PORT'),
        'CONN_MAX_AGE': 300,
        'OPTIONS': {
            'options': '-c search_path=saas_data',
            'keepalives': 1,
            'keepalives_idle': 60,
            'keepalives_interval': 10,
            'keepalives_count': 5,
        },
    }
}

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels.layers.InMemoryChannelLayer"
    }
}

# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/
USE_I18N = True
LANGUAGE_CODE = 'en'

LANGUAGES = [
    ('en', _('English')),
    ('id', _('Indonesia')),
]

USE_I18N = True

USE_TZ = True


LOGIN_URL = '/login'


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/
# Add this line to your settings
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATIC_URL = "static/"
STATICFILES_DIRS = [
    BASE_DIR / "static",
]

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"


MEDIA_URL = '/media/'
DEFAULT_FILE_STORAGE = 'django.core.files.storage.FileSystemStorage'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

AWS_S3_REGION_NAME = 'ap-southeast-1'



TIME_ZONE = "Asia/Singapore"
# Django Easy audit
DJANGO_EASY_AUDIT_WATCH_REQUEST_EVENTS = False
DJANGO_EASY_AUDIT_CRUD_EVENT_NO_CHANGED_FIELDS_SKIP = True
DJANGO_EASY_AUDIT_READONLY_EVENTS = False
DJANGO_EASY_AUDIT_CRUD_EVENT_INVALID_SKIP = True
DJANGO_EASY_AUDIT_UNREGISTERED_CLASSES_EXTRA = ["sass_app.ErrorLog"]

# Email settings
DEFAULT_FROM_EMAIL = '<EMAIL>'
AWS_SES_ACCESS_KEY_ID = env('AWS_SES_ACCESS_KEY_ID')
AWS_SES_SECRET_ACCESS_KEY= env('AWS_SES_SECRET_ACCESS_KEY')
AWS_SES_REGION_NAME = 'ap-southeast-1'  # e.g., 'us-east-1'

if SYSTEM_RUN_LOCATION in ['PROD','CSTACK','CSTACK-PROD']:
    DBBACKUP_STORAGE = 'sass.storage.S3Storage'
    DBBACKUP_STREAM = True
else:
    DBBACKUP_STORAGE = 'django.core.files.storage.FileSystemStorage'

DBBACKUP_STORAGE_OPTIONS = {'location': 'backup'}
DBBACKUP_CONNECTORS = {
    'default': {
        'CONNECTOR': 'dbbackup.db.postgresql.PgDumpConnector',
        'DUMP_SUFFIX': '--no-owner --no-privileges --clean --verbose --format=custom',  # Changed to custom
        'EXCLUDE': ['saas_data.sass_app_errorlog'],
        'EXTENSION': 'sql',  # Add this line
    }
}
DBBACKUP_FILENAME_TEMPLATE = 'DBBACKUP-SASS-{datetime}.{extension}'
