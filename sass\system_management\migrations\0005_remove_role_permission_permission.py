# Generated by Django 5.1 on 2024-10-16 08:44

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("system_management", "0004_alter_role_permission_alter_role_status"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="role",
            name="permission",
        ),
        migrations.CreateModel(
            name="Permission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("page", models.CharField(blank=True, max_length=500, null=True)),
                ("create", models.BooleanField(default=True)),
                ("update", models.<PERSON>olean<PERSON>ield(default=True)),
                ("delete", models.BooleanField(default=True)),
                ("view", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "role",
                    models.Foreign<PERSON>ey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="system_management.role",
                    ),
                ),
            ],
        ),
    ]
