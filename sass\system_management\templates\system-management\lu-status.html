{% load static %}
{% load mathfilters %}
{% load custom_tags %}
<div class="row">
    <div class="col-12">
        <div class="breadcrumb">
            <a href="/">Back</a> <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M7.07586 4.51043C7.18836 4.39807 7.34086 4.33496 7.49986 4.33496C7.65886 4.33496 7.81136 4.39807 7.92386 4.51043L11.3239 7.91043C11.4362 8.02293 11.4993 8.17543 11.4993 8.33443C11.4993 8.49343 11.4362 8.64593 11.3239 8.75843L7.92386 12.1584C7.81012 12.2644 7.65969 12.3221 7.50424 12.3194C7.3488 12.3166 7.2005 12.2537 7.09057 12.1437C6.98064 12.0338 6.91766 11.8855 6.91492 11.7301C6.91218 11.5746 6.96988 11.4242 7.07586 11.3104L10.0519 8.33443L7.07586 5.35843C6.9635 5.24593 6.90039 5.09343 6.90039 4.93443C6.90039 4.77543 6.9635 4.62293 7.07586 4.51043Z" fill="#BBBBBB"/>
                </svg>
            <span>Learning Unit Status</span>
        </div>  
        <div class="content-system-management">
            <div class="card card-shadow card-padding">
                <h2 class="title-section">System Management</h2>
                <c-menu_system_management current_page="luStatus"></c-menu_system_management>
                <h2 class="title-card text-black mb-4">Learning Unit Status</h2>
                <div class="d-flex flex-column flex-md-row align-items-md-center gap-2 justify-content-start menu-user-management">   
                    <button type="button" class="btn-text v-center show-popup" data-popup="popup-filter"><img class="icon-image" src="{% static 'img/icon-filter.svg' %}" alt="bulk"> Filter        
                        {% if department_filter %}
                        <span>By Department: <span class="badge badge-primary">{{ department_filter|upper }}</span> </span>                            
                        {% endif %}
                        
                        {% if filter_status %}
                        <span>By Status: <span class="badge badge-primary">{{ filter_status|upper }}</span> </span>                            
                        {% endif %}
                    </button>
                    <button {% if not permission.update %} disabled {% endif %} type="button" class="btn-text v-center p-0 show-popup disabled btn-bulk-action" data-popup="bulk-action"><img class="icon-image" src="{% static 'img/separate-dots.svg' %}" alt="bulk"> Bulk action</button>
                </div>
                
                <div class="mt-4">
                    <div class="form-group">
                        <label for="search-lu-code">LU Code</label>
                        <input autocomplete="off" type="text" name="search-lu-code" id="search-lu-code" class="form-control" placeholder="search LU code"  hx-get="{% url 'system_management:luStatus' %}" hx-trigger="keyup changed delay:0.5s" hx-swap="outerHTML" hx-vals='{"department-filter": "{{ department_filter }}"}' hx-target=".content-lu-status" hx-select=".content-lu-status" hx-indicator=".loading-lu-status">
                    </div>
                </div>
                <div class="wrapper-content-lu-status p-relative">
                    <div class="content-lu-status">
                        <div class="table-responsive">
                            <p class="mt-2">Academic Year {{system_current_year.current_year}} - Semester {{system_current_year.current_semester}}</p>
                            
                            <table class="table-sass table-lu-status table table-borderless">
                                <thead class="bg-gray text-black">
                                    <tr>
                                        <th>
                                            <label class="wrapper-checkbox" for="checked-all">
                                                <input type="checkbox" title="checked all" id="checked-all">
                                                <span class="checkmark"></span>
                                            </label>
                                        </th>
                                        <th hx-get="{% url 'system_management:luStatus' %}?{% if filter_status %}&filter-status={{ filter_status }}{% endif %}{% if search_lu_code %}&search-lu-code={{ search_lu_code }}{% endif %}{% if department_filter %}&department-filter={{ department_filter|urlencode  }}{% endif %}&sort={% if sort == None or sort == 'asc' %}desc{% else %}asc{% endif %}&sort-col=code" hx-trigger="click" hx-swap="outerHTML" hx-target=".content-lu-status" hx-select=".content-lu-status" hx-indicator=".loading-lu-status">
                                        <div class="wrapper-th-sort">
                                            <span>LU CODE</span> 
                                            <div class="wrapper-caret-sort"> <span class="top-caret {% if sort == "desc" and sort_col == "code" %}disabled{% endif %}"><i class="bi bi-caret-down-fill"></i></span><span class="buttom-caret {% if sort == "asc" and sort_col == "code" %}disabled{% endif %}"><i class="bi bi-caret-up-fill"></i></span> </div>
                                        </div>
                                        </th>
                                        <th hx-get="{% url 'system_management:luStatus' %}?{% if filter_status %}&filter-status={{ filter_status }}{% endif %}{% if search_lu_code %}&search-lu-code={{ search_lu_code }}{% endif %}{% if department_filter %}&department-filter={{ department_filter|urlencode  }}{% endif %}&sort={% if sort == 'asc' or sort == None %}desc{% else %}asc{% endif %}&sort-col=name" hx-trigger="click" hx-swap="outerHTML" hx-target=".content-lu-status" hx-select=".content-lu-status" hx-indicator=".loading-lu-status"><div class="wrapper-th-sort">
                                            <span>LU NAME</span> 
                                            <div class="wrapper-caret-sort"> <span class="top-caret {% if sort == "desc" and sort_col == "name" %}disabled{% endif %}"><i class="bi bi-caret-down-fill"></i></span><span class="buttom-caret {% if sort == "asc" and sort_col == "name" %}disabled{% endif %}"><i class="bi bi-caret-up-fill"></i></span> </div>
                                        </div> 
                                        </th>
                                        <th hx-get="{% url 'system_management:luStatus' %}?{% if filter_status %}&filter-status={{ filter_status }}{% endif %}{% if search_lu_code %}&search-lu-code={{ search_lu_code }}{% endif %}{% if department_filter %}&department-filter={{ department_filter|urlencode  }}{% endif %}&sort={% if sort == 'asc' or sort == None %}desc{% else %}asc{% endif %}&sort-col=department__code" hx-trigger="click" hx-swap="outerHTML" hx-target=".content-lu-status" hx-select=".content-lu-status" hx-indicator=".loading-lu-status">
                                            <div class="wrapper-th-sort">
                                                <span>DEPARTMENT</span> 
                                                <div class="wrapper-caret-sort"> <span class="top-caret {% if sort == "desc" and sort_col == "department__code" %}disabled{% endif %}"><i class="bi bi-caret-down-fill"></i></span><span class="buttom-caret {% if sort == "asc" and sort_col == "department__code" %}disabled{% endif %}"><i class="bi bi-caret-up-fill"></i></span> </div>
                                            </div> 
                                        </th>                
                                        <th>
                                             
                                                <span>TOTAL WEIGHTAGE </span>
                                            
                                        </th>
                                        <th>                                            
                                            <span>STATUS</span> 
                                        </th>                
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if not learning_units %}
                                        <tr>
                                            <td colspan="6" class="py-5 text-center">No LU Found.</td>
                                        </tr>
                                    {% else %}
                                        {%for learning_unit in learning_units %}
                                            {% with status_unit=learning_unit.status_units|first %}
                                                <tr>
                                                    <td> 
                                                        <label class="wrapper-checkbox">
                                                            <input type="checkbox" title="checkbox user" class="checkbox-tr">
                                                            <span class="checkmark"></span>
                                                        </label>
                                                    </td>
                                                    <td class="d-none value-id">{{learning_unit.id}}</td>
                                                    <td class="field-value">{{learning_unit.code}}</td>
                                                    <td>{{learning_unit.name}}</td>
                                                    <td>{% if learning_unit.department.name %} {{learning_unit.department}} {% else %} {{learning_unit.department.code}} {% endif %}</td>
                                                    <td>
                                                        
                                                        {% get_total_weightage learning_unit as total_weightage %}
                                                        {% if total_weightage %}
                                                            {{total_weightage}}%
                                                        {% else %}
                                                            0%
                                                        {% endif %}
                                                        
                                                    </td>
                                                    <td>
                                                        
                                                        {% if status_unit.status %} {{status_unit.get_status_display|title}} {% else %} Pending {% endif %}
                                                    </td>
                                                </tr>
                                            {% endwith %}
                                        {% endfor %}
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-center">
                            {% if page_content.paginator.num_pages > 1 %}
                                {% with total_pages=page_content.paginator.num_pages %}
                                    <div class="pagination mt-3">
                                        {% for num in page_content.paginator.page_range %}
                                            {% if num == 1 or num == total_pages %}
                                                <a hx-get="{% url 'system_management:luStatus' %}?page={{num}}{% if filter_status %}&filter-status={{ filter_status }}{% endif %}{% if search_lu_code %}&search-lu-code={{ search_lu_code }}{% endif %}{% if department_filter %}&department-filter={{ department_filter|urlencode  }}{% endif %}&sort={% if sort == None %}asc{% else %}{{ sort }}{% endif %}&sort-col={% if sort_col == None %}code{% else %}{{ sort_col }}{% endif %}" class="page-link cursor-pointer {% if num == page_content.number %} active {% endif %}" hx-trigger="click" hx-swap="outerHTML" hx-target=".content-lu-status" hx-select=".content-lu-status" hx-indicator=".loading-lu-status">{{ num }}</a>
                                            {% elif num|sub:page_content.number >= -1 and num|sub:page_content.number <= 1  %}
                                                <a hx-get="{% url 'system_management:luStatus' %}?page={{num}}{% if filter_status %}&filter-status={{ filter_status }}{% endif %}{% if search_lu_code %}&search-lu-code={{ search_lu_code }}{% endif %}{% if department_filter %}&department-filter={{ department_filter|urlencode  }}{% endif %}&sort={% if sort == None %}asc{% else %}{{ sort }}{% endif %}&sort-col={% if sort_col == None %}code{% else %}{{ sort_col }}{% endif %}" class="page-link cursor-pointer {% if num == page_content.number %} active {% endif %}" hx-trigger="click" hx-swap="outerHTML" hx-target=".content-lu-status" hx-select=".content-lu-status" hx-indicator=".loading-lu-status">{{ num }}</a>
                                            {% elif num|sub:page_content.number > -3 and num|sub:page_content.number < 3  %}
                                                <span class="page-link">...</span>
                                            {% endif %} 
                                        {% endfor %}
                                    </div>
                                {% endwith %}
                            {% endif %}
                        </div>
                    </div>
                    <div class="loading htmx-indicator loading-lu-status loading-absolute">
                        <img src="{% static 'img/loading.gif' %}" alt="loading" width="50">
                    </div> 
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Popup bulk action -->
<c-popup class="popup-wide bulk-action" buttonleft >
    <div class="d-flex mb-4 justify-content-between align-items-center w-100">
        <h2 class="mb-0">Bulk Action</h2>
        <button class="btn-text close-popup" type="button"><img src="{% static 'img/icon-close.svg' %}" alt="close"></button>
    </div>
    <div class="my-4 list-selected-user"></div>
    <form  class="form-bulk-status " hx-post="{% url 'system_management:update_lu_status' %}" hx-trigger="submit" hx-swap="outerHTML" hx-target=".content-system-management" hx-select=".content-system-management" hx-indicator=".loading-lu-status">
        {% csrf_token %}
        <input hidden name="current_year" value="{{system_current_year.current_year}}"></input>
        <input hidden name="current_semester" value="{{system_current_year.current_semester}}"></input>
        <div class="w-100">
            <div class="form-group">
                <div class="text-start mt-2">
                    <p class="text-start mb-2">Change status to</p>
                    <div class="wrapper-select ">
                        <select name="status" title="status" class="form-control">
                            <option value="">Choose status</option>
                            <option value="submitted">Submitted</option>
                            <option value="approved">Approved</option>
                            <option value="pending">Pending</option>
                            <option value="rework">Rework</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="d-flex align-items-center gap-3 justify-content-start">          
            <button name="submit-bulk" type="submit" class="btn btn-primary close-popup disabled">Save changes</button>
            <button type="button" class="btn-text text-danger close-popup">Cancel </button>
        </div>
    </form> 
</c-popup>
<!-- Popup Filter -->
<c-popup class="popup-wide popup-filter" buttonleft >
    <div class="d-flex mb-4 justify-content-between align-items-center w-100">
        <h2 class="mb-0">Filter LU Status</h2>
        <button class="btn-text close-popup" type="button"><img src="{% static 'img/icon-close.svg' %}" alt="close"></button>
    </div>   
    
    <form hx-get="{% url 'system_management:luStatus' %}" hx-trigger="submit" hx-swap="outerHTML" hx-target=".content-system-management" hx-select=".content-system-management">
        <div class="w-100">
            <div class="form-group">
                <div class="text-start mt-2">
                        <p class="text-start mb-2">Filter by </p>                        
                        <div class="wrapper-select mb-4">
                            <select name="filter-by-lu-status" title="filter-by-lu-status" hx-get="{% url 'system_management:filterByLuStatus' %}" hx-target="#filter-results" hx-swap="innerHTML" hx-trigger="load,change" class="form-control">
                                <option value="department">Department</option>
                                <option value="status">Status</option>
                            </select>
                        </div>
                        <div class="ok" id="filter-results">
                            <h1>ok</h1>
                        </div>
                </div>
            </div>
        </div>
        <div class="d-flex align-items-center gap-3 justify-content-start mt-5">            
            <button type="submit" class="btn btn-primary close-popup save-filter-lustatus">Save changes</button>
            <button type="button" class="btn-text text-danger close-popup">Cancel </button>
        </div>
    </div>
    </form>
</c-popup>
{% block script %}
    
{% endblock script %}