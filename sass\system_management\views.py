from django.shortcuts import render, redirect
from system_management.models import *
from django.contrib.auth.decorators import login_required
from easyaudit.models import CRUDEvent
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from schedule_assessment.rule import *
from django.core.paginator import Paginator
from django.http import HttpResponse
from django.db.models import F
from openpyxl.utils import get_column_letter
from django.db.models import Q, Case, When, Value, CharField
from sass_app.microsoft_auth.graph_helper import *
from utility import *
from constant import *
from django.db.models import Prefetch, Window, F, Exists, OuterRef
from dateutil import parser
from business_rules import export_rule_data
import ast
from django.http import JsonResponse
import json
from django.db.models.functions import Coalesce, FirstValue, RowNumber
from django.contrib.contenttypes.models import ContentType
from datetime import datetime
from django.utils import timezone
from django.urls import reverse
from sass_app.email import *



# Create your views here.
POSTS_PER_PAGE = 20
POSTS_PER_PAGE_BEGIN = POSTS_PER_PAGE - 1


@login_required
@check_access(page='system_management')
def system_management(request):
    permission = check_permission(request.user, 'system_management')
    if not permission.view:
        return redirect('sass_app:main')
    
    # Upload Context
    context={"upload_user_upload_status": request.GET.get('upload_user_upload_status', None),
             "upload_user_number_created": request.GET.get('upload_user_number_created', 0),
             "upload_user_number_updated": request.GET.get('upload_user_number_updated', 0),
             "upload_user_number_failed": request.GET.get('upload_user_number_failed', 0),}
    return render(request, 'system-management/index.html',context)


@login_required
def user_management(request):
    search_user = request.GET.get("search-user", None)
    sort = request.GET.get('sort', None)
    sort_col = request.GET.get('sort-col', '-date_joined')
    

    if sort == "desc":
        sortvalue = '-' + sort_col
    else:
        sortvalue = sort_col

    filter_condition = Q()
    if search_user:
        filter_condition &= (Q(first_name__icontains=search_user) | Q(
            email__icontains=search_user))

    registered_users = User.objects.select_related('registered_user').annotate(
        role_type=F('registered_user__type'),
        role_status=F('registered_user__status')
    ).filter(filter_condition).order_by(sortvalue)

    paginator = Paginator(registered_users, POSTS_PER_PAGE)
    page = request.GET.get('page', 1)
    if page:
        page_content = paginator.page(page)
        registered_users = page_content.object_list
    else:
        page_content = paginator.page(1)


    upload_status=request.GET.get('upload_user_upload_status', None)
    context = {
        "registered_users": registered_users,
        "search_user": search_user,
        "sort": sort,
        'sort_col': sort_col,
        "page": page,
        'page_content': page_content,
        'paginator': paginator,
        "permission": check_permission(request.user, 'system_management'),
        "upload_user_upload_status":True if upload_status == 'true' else False,
        "upload_user_number_created":request.GET.get('upload_user_number_created', 0),
        "upload_user_number_updated":request.GET.get('upload_user_number_updated', 0),
        "upload_user_number_failed":request.GET.get('upload_user_number_failed', 0),
    }
    return render(request, 'system-management/user-management.html', context)


@login_required
def get_edit_user(request, id=None):
    registered_user = None
    if id:
        registered_user = User.objects.get(id=id)
    context = {
        "registered_user": registered_user,
    }
    return render(request, 'system-management/user-management.html', context)


@login_required
def create_or_update_user(request, id=None):
    if request.method == "POST":
        if "submit-bulk" in request.POST:

            users = request.POST.getlist("id-user")
            role_checkbox = request.POST.get("checkbox-change-role")
            status_checkbox = request.POST.get("checkbox-change-status")
            role = request.POST.get("change-role")
            status = request.POST.get("change-status")

            if users:
                users = list(set(users))
                values = {}
                if role_checkbox:
                    if role:
                        values['type'] = role
                if status_checkbox:
                    if status:
                        values['status'] = status
                Role.objects.filter(user_id__in=users).update(**values)

        else:
            email = request.POST.get("email", None)
            role = request.POST.get("role", None)
            status = request.POST.get("status", None)

            user = None

            if id:
                user = User.objects.get(id=id)
            else:
                if is_valid_email(email):
                    user, _ = User.objects.get_or_create(
                        username=email.split("@")[0], email=email)
                    user.first_name = email.split("@")[0].title()
                    username = email.split("@")[0]
                    user.username = username
                    user.save()

            if user:
                username = email.split("@")[0]
                user.username = username
                user.email = email
                user.save()

                role_, _ = Role.objects.get_or_create(user=user)
                role_.status = status
                role_.type = role
                role_.save()

                if not id:
                    # Send EMAIL
                    if User.objects.filter(email=email).exists():
                        user_email_template = CREATE_USER_EMAIL_TEMPLATE.format(
                            name=user.first_name, support_email=get_SUPPORT_EMAIL())
                        payload = {
                            'subject': CREATE_USER_EMAIL_TITLE,
                            'message': dedent(user_email_template).strip(),
                            'destination_email': [email]
                        }
                        send_text_email_bg_jobs(payload=payload)

    return redirect('system_management:userManagement')


@login_required
@sending_email_if_timeout(timeout_seconds=20)
def export_user_table(request):

    if request.method == "POST":
        search_user = request.POST.get("search_query", None)
        wb = Workbook()
        sheet = wb.active

        sheet.title = "User List"

        # Create a bold font style
        bold_font = Font(bold=True)
        headers = ["EMAIL", "NAME", "RULE", "STATUS"]
        row = 1
        for col, value in enumerate(headers, start=1):
            cell = sheet.cell(row=row, column=col, value=value)
            cell.font = bold_font

        filter_condition = Q()
        if search_user:
            filter_condition &= (Q(first_name__icontains=search_user) | Q(
                email__icontains=search_user))

        users_with_roles = User.objects.select_related('registered_user').annotate(
            role_type=F('registered_user__type'),
            role_status=F('registered_user__status')
        ).filter(filter_condition).values_list('email', 'first_name', 'role_type', 'role_status')

        for row, item in enumerate(users_with_roles, start=2):
            for col, value in enumerate(item, start=1):
                sheet.cell(row=row, column=col, value=value)

        # Adjust column widths to fit content
        for col in range(1, len(headers) + 1):
            max_length = 0
            column_letter = get_column_letter(col)

            # Iterate through all cells in the column
            for cell in sheet[column_letter]:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass

            adjusted_width = (max_length + 2) * 1.2
            sheet.column_dimensions[column_letter].width = adjusted_width

        # Create the HttpResponse object
        file_name = "Registered User"
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = f'attachment; filename={
            file_name}.xlsx'

        wb.save(response)

        system_management_email_template = SYSTEM_MANAGEMENT_EMAIL_TEMPLATE.format(
            name=request.user.first_name, file_name=file_name, support_email=get_SUPPORT_EMAIL())
        system_management_email_template = dedent(
            system_management_email_template).strip()
        return response, {"wb": wb,
                          "file_name": f"{file_name}.xlsx",
                          "email_subject": f"{file_name} - is ready for Download",
                          "email_body": system_management_email_template}

    return HttpResponse(200)


def lu_status(request):
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()

    search_lu_code = request.GET.get('search-lu-code', None)
    department_filter = request.GET.get("department-filter", None)
    filter_status = request.GET.get("filter-status", None)
    sort = request.GET.get('sort', None)
    sort_col = request.GET.get('sort-col', 'code')
    if sort == "desc":
        sortvalue = '-' + sort_col
    else:
        sortvalue = sort_col

    if department_filter == 'None':
        department_filter = None
    if filter_status == "None":
        filter_status = None

    
    filter_condition = (
        Q(courselumap__academic_year=SYSTEM_CURRENT_YEAR.current_year,
          courselumap__semester=SYSTEM_CURRENT_YEAR.current_semester)
    )
    if search_lu_code:
        filter_condition &= Q(code__icontains=search_lu_code)

    if department_filter:
        filter_condition &= Q(department__code=department_filter)

    if filter_status:
        if filter_status == 'pending':
            filter_condition &= (
                Q(latest_status='pending') |
                Q(latest_status__isnull=True)
            )
        else:
            filter_condition &= Q(latest_status=filter_status)

    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()

    learning_units = (
        LearningUnit.objects.annotate(
            row_num=Window(
                expression=RowNumber(),
                partition_by=F('code'),
                order_by=sortvalue
            ),
            latest_status=Subquery(
                StatusLearningUnit.objects.filter(
                    lu_id=OuterRef('id'),
                    current_year=SYSTEM_CURRENT_YEAR.current_year,
                    current_semester=SYSTEM_CURRENT_YEAR.current_semester
                ).order_by('-created_at')
                .values('status')[:1]
            )
        )
        .filter(
            filter_condition,
            row_num=1
        )
        .prefetch_related(
            Prefetch(
                'statuslearningunit_set',
                queryset=StatusLearningUnit.objects.filter(
                    current_year=SYSTEM_CURRENT_YEAR.current_year,
                    current_semester=SYSTEM_CURRENT_YEAR.current_semester
                ).select_related('updated_by')
                .order_by('-created_at'),
                to_attr='status_units'
            )
        )
        .order_by(sortvalue, 'id')
    )

    paginator = Paginator(learning_units, POSTS_PER_PAGE)
    page = request.GET.get('page', 1)

    if page:
        page_content = paginator.page(page)
        learning_units = page_content.object_list
    else:
        page_content = paginator.page(1)

    departments = Department.objects.all().order_by('name')
    context = {
        "system_current_year": SYSTEM_CURRENT_YEAR,
        "learning_units": learning_units,
        "search_lu_code": search_lu_code,
        "page": page,
        "sort": sort,
        'sort_col': sort_col,
        'page_content': page_content,
        'paginator': paginator,
        'department_filter': department_filter,
        'filter_status': filter_status,
        'departments': departments,
        'permission': check_permission(request.user, 'system_management')
    }
    return render(request, 'system-management/lu-status.html', context)


def update_lu_status(request):

    if request.method == "POST":
        if "submit-bulk" in request.POST:
            status = request.POST.get("status", None)
            current_year = request.POST.get("current_year", None)
            current_semester = request.POST.get("current_semester", None)
            id_lus = request.POST.getlist("id-lu", None)
            if status and current_year and current_semester:
                for id_lu in id_lus:
                    status_learning_unit = StatusLearningUnit.objects.filter(
                        lu_id=id_lu, current_year=current_year, current_semester=current_semester).first()
                    if not status_learning_unit:
                        status_learning_unit, _ = StatusLearningUnit.objects.get_or_create(
                            lu_id=id_lu, current_year=current_year, current_semester=current_semester)

                    learning_unit_dict = LearningUnit.objects.filter(
                        id=id_lu).values().first()
                    learning_unit_dict.pop('id')  # Remove ID
                    department = Department.objects.get(
                        id=learning_unit_dict['department_id'])
                    if department:
                        learning_unit_dict['department_name'] = department.name
                    # Remove department_id
                    learning_unit_dict.pop('department_id')
                    learning_unit_dict['updated_at'] = learning_unit_dict['updated_at'].isoformat(
                    )
                    learning_unit_dict['created_at'] = learning_unit_dict['created_at'].isoformat(
                    )

                    status_learning_unit.lu_metadata = learning_unit_dict
                    status_learning_unit.status = status
                    status_learning_unit.updated_by = request.user
                    status_learning_unit.updated_at = timezone.now()
                    status_learning_unit.save()

    return redirect('system_management:luStatus')


# ============Set Current=================
def set_current(request):
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
    systemeditmode = SystemEditMode.objects.all().order_by('group')
    context = {"system_current_year": SYSTEM_CURRENT_YEAR, "editMode": request.GET.get(
        "editMode", None), "systemeditmodes": systemeditmode, "permission": check_permission(request.user, 'system_management')}
    return render(request, 'system-management/set-current.html', context)


def update_set_current(request):
    if request.method == "POST":

        # ============= current year
        current_year = request.POST.get("current-year", None)
        current_semester = request.POST.get("current-semester", None)

        SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
        if current_year:
            SYSTEM_CURRENT_YEAR.current_year = current_year
        if current_semester:
            SYSTEM_CURRENT_YEAR.current_semester = current_semester
        SYSTEM_CURRENT_YEAR.save()

        # ============== Lu Phase Edit Date
        systemeditmode = request.POST.getlist("systemeditmode", None)
        edit_mode = request.POST.getlist("edit-mode", None)
        start_date = request.POST.getlist("start-date", None)
        end_date = request.POST.getlist("end-date", None)
        edit_mode_after = request.POST.getlist("edit-mode-after", None)
        data_system_edit = [
            {
                "system_edit_id": syseditmode,
                "edit_mode": edit_mode,
                "start_date": start,
                "end_date": end,
                "edit_mode_after": editafter,
            }
            for syseditmode, edit_mode, start, end, editafter in zip(systemeditmode, edit_mode, start_date, end_date, edit_mode_after)
        ]

        for data in data_system_edit:
            system_edit_id = data["system_edit_id"]
            edit_mode = data["edit_mode"]
            start_date = data["start_date"]
            end_date = data["end_date"]
            edit_mode_after = data["edit_mode_after"]

            if system_edit_id:
                system_edit = SystemEditMode.objects.get(id=system_edit_id)
                if edit_mode:
                    system_edit.mode = edit_mode
                if start_date:
                    start_date_parser = parser.parse(start_date)
                    system_edit.start = start_date_parser
                else:
                    system_edit.start = None
                if end_date:
                    end_date_parser = parser.parse(end_date)
                    system_edit.end = end_date_parser
                else:
                    system_edit.end = None
                if edit_mode_after:
                    system_edit.edit_after = edit_mode_after
                else:
                    system_edit.edit_after = None

                system_edit.save()

    return redirect('system_management:setCurrent')


# ============Upload File=================
@ login_required
def form_file_view(request):
    context = {"permission": check_permission(
        request.user, 'system_management')}
    return render(request, 'system-management/form-file-view.html', context)


@ login_required
def form_upload(request):
    return render(request, 'system-management/form-upload.html')


@ login_required
def upload_file(request):
    if request.method == "POST":
        moderation_method = request.FILES.get("moderation-method", None)
        assessment = request.FILES.get("assessment", None)
        course = request.FILES.get("course", None)
        courselumap = request.FILES.get("courselumap", None)
        lulist = request.FILES.get("lulist", None)
        coursetypelutypecalendar = request.FILES.get("coursetypelutypecalendar", None)
        department = request.FILES.get("department", None)

        user = request.FILES.get("upload-bulk-user", None)

        if 'validation' in request.POST:
            validations_data = []
            if department:
                department = excel_reader(department)
                header = department.columns.tolist()
                if len(header) != EXPECTED_DEPARTMENT_COLUMN:
                    validations_data.append({"file_type": "Department", "num_col": len(
                        header), "expected_num_col": EXPECTED_DEPARTMENT_COLUMN})

            if moderation_method:
                moderation_method = excel_reader(moderation_method)
                header = moderation_method.columns.tolist()
                if len(header) != EXPECTED_MODERATION_METHOD_COLUMN:
                    validations_data.append({"file_type": "Moderation Method", "num_col": len(
                        header), "expected_num_col": EXPECTED_MODERATION_METHOD_COLUMN})

            if assessment:
                assessment = excel_reader(assessment)
                header = assessment.columns.tolist()
                if len(header) != EXPECTED_ASSESSEMENT_COLUMN:
                    validations_data.append({"file_type": "Assessement", "num_col": len(
                        header), "expected_num_col": EXPECTED_ASSESSEMENT_COLUMN})
            if course:
                course = excel_reader(course)
                header = course.columns.tolist()
                if len(header) != EXPECTED_COURSE_COLUMN:
                    validations_data.append({"file_type": "Course", "num_col": len(
                        header), "expected_num_col": EXPECTED_COURSE_COLUMN})
            if courselumap:
                courselumap = excel_reader(courselumap)
                header = courselumap.columns.tolist()
                if len(header) != EXPECTED_COURSELUMAP_COLUMN:
                    validations_data.append({"file_type": "CourseLUMap", "num_col": len(
                        header), "expected_num_col": EXPECTED_COURSELUMAP_COLUMN})
            if lulist:
                lulist = excel_reader(lulist)
                header = lulist.columns.tolist()
                if len(header) != EXPECTED_LEARNING_UNIT_COLUMN:
                    validations_data.append({"file_type": "LUList", "num_col": len(
                        header), "expected_num_col": EXPECTED_LEARNING_UNIT_COLUMN})

            if coursetypelutypecalendar:
                coursetypelutypecalendar = excel_reader(
                    coursetypelutypecalendar)
                header = [col for col in coursetypelutypecalendar.columns if not (
                    col.lower().startswith('week') and any(char.isdigit() for char in col))]
                if len(header) != EXPECTED_ACADEMIC_CALENDAR_COLUMN:
                    validations_data.append({"file_type": "CourseTypeLUTypeCalendar", "num_col": len(
                        header), "expected_num_col": EXPECTED_ACADEMIC_CALENDAR_COLUMN})

            if user:
                user = excel_reader(user)
                header = [col for col in user.columns if not (
                    col.lower().startswith('week') and any(char.isdigit() for char in col))]
                if len(header) != EXPECTED_USER_COLUMN:
                    validations_data.append({"file_type": "User", "num_col": len(
                        header), "expected_num_col": EXPECTED_USER_COLUMN})

                context = {"validations_data": validations_data} #special for user
                return render(request, 'system-management/user-upload-validation-pop-up.html', context)

            context = {"validations_data": validations_data}
            return render(request, 'system-management/form-upload-validation-pop-up.html', context)
        else:
            success_files = []
            error_files = []
            if department:
                file_name = department.name
                
                for file in FileUpload.objects.filter(type="department"):
                    file.delete()

                file_upload, created = FileUpload.objects.get_or_create(
                    name=file_name, type="department", file=department, uploaded_by=request.user)
                try:
                    # Populate and Update
                    if created:
                        department = excel_reader(department)
                        populate_data(department, "department")
                        success_files.append(file_name)
                except:
                    error_files.append(file_name)
                    file_upload.delete()

            if moderation_method:
                file_name = moderation_method.name
    
                for file in FileUpload.objects.filter(type="moderation_method"):
                    file.delete()

                file_upload, created = FileUpload.objects.get_or_create(
                    name=file_name, type="moderation_method", file=moderation_method, uploaded_by=request.user)
                try:
                    # Populate and Update
                    if created:
                        moderation_method = excel_reader(moderation_method)
                        populate_data(moderation_method, "moderation_method")
                        success_files.append(file_name)
                except:
                    error_files.append(file_name)
                    file_upload.delete()

            if assessment:
                file_name = assessment.name
                
                for file in FileUpload.objects.filter(type="assessment"):
                    file.delete()
                    
                file_upload, created = FileUpload.objects.get_or_create(
                    name=file_name, type="assessment", file=assessment, uploaded_by=request.user)
                try:
                    # Populate and Update
                    if created:
                        assessment = excel_reader(assessment)
                        populate_data(assessment, "assessment")
                        success_files.append(file_name)
                except Exception as e:
                    print("e: ", e)
                    error_files.append(file_name)
                    file_upload.delete()

            if course:
                file_name = course.name

                for file in FileUpload.objects.filter(type="course"):
                    file.delete()
                    
                file_upload, created = FileUpload.objects.get_or_create(
                    name=file_name, type="course", file=course, uploaded_by=request.user)
                try:
                    # Populate and Update
                    if created:
                        course = excel_reader(course)
                        populate_data(course, "course")
                        success_files.append(file_name)
                except:
                    error_files.append(file_name)
                    file_upload.delete()

            if lulist:
                file_name = lulist.name
                
                for file in FileUpload.objects.filter(type="learning_unit"):
                    file.delete()

                file_upload, created = FileUpload.objects.get_or_create(
                    name=file_name, type="learning_unit", file=lulist, uploaded_by=request.user)
                try:
                    # Populate and Update
                    if created:
                        lulist = excel_reader(lulist)
                        populate_data(lulist, "learning_unit")
                        success_files.append(file_name)
                except Exception as e:
                    print("===== e: ", e)
                    error_files.append(file_name)
                    file_upload.delete()

            if courselumap:
                file_name = courselumap.name
                
                for file in FileUpload.objects.filter(type="course_lu_map"):
                    file.delete()

                file_upload, created = FileUpload.objects.get_or_create(
                    name=file_name, type="course_lu_map", file=courselumap, uploaded_by=request.user)
                try:
                    # Populate and Update
                    if created:
                        courselumap = excel_reader(courselumap)
                        populate_data(courselumap, "course_lu_map")
                        success_files.append(file_name)
                except Exception as e:
                    print("e: ", e)
                    error_files.append(file_name)
                    file_upload.delete()

            if coursetypelutypecalendar:
                file_name = coursetypelutypecalendar.name
              
                for file in FileUpload.objects.filter(type="academic_week"):
                    file.delete()

                file_upload, created = FileUpload.objects.get_or_create(
                    name=file_name, type="academic_week", file=coursetypelutypecalendar, uploaded_by=request.user)
                try:
                    # Populate and Update
                    if created:
                        coursetypelutypecalendar = excel_reader(
                            coursetypelutypecalendar)
                        populate_data(
                            coursetypelutypecalendar, "academic_week")
                        success_files.append(file_name)
                except Exception as e:
                    print("e: ", e)
                    error_files.append(file_name)
                    file_upload.delete()

    context = {
        'success_files': success_files,
        'error_files': error_files,
        "permission": check_permission(request.user, 'system_management')
    }

    return render(request, 'system-management/form-file-view.html', context)


@ login_required
def import_bulk_user(request):
    success_files=[]
    error_files=[]
    user = request.FILES.get("upload-bulk-user", None)
    number_created=0
    number_updated=0
    number_failed=0
    if user:
        file_name = user.name
        FileUpload.objects.filter(type="user").delete()
        file_upload, created = FileUpload.objects.get_or_create(name=file_name, type="user", file=user, uploaded_by=request.user)
        try:
            # Populate and Update
            if created:
                user = excel_reader(user)
                number_created,number_updated,number_failed = populate_data(user, "user")
                success_files.append(file_name)
        except Exception as e:
            print("e: ", e)
            error_files.append(file_name)

        # After Success Delete > No Retain
        file_upload.delete()

    if success_files:url_parameter = f"?upload_user_upload_status=true&upload_user_number_created={number_created}&upload_user_number_updated={number_updated}&upload_user_number_failed={number_failed}"
    return redirect(reverse('system_management:systemManagement')+url_parameter)



# ============END Upload File=================


def populate_data(data, file_type):
    if file_type == 'learning_unit':
        # Convert to a list of dictionaries
        key_mapping = {'LUCode': 'code', 'LUName': 'name', 'LUCredit': 'credit', 'Examinable': 'is_examinable', 'UL': 'unit_leader',
                       'CourseType': 'course_type', 'LUType': 'lu_type', 'Department': 'department', 'LUPhase': 'phase', 'LUMessage': 'message'}
        df_renamed = data.rename(columns=key_mapping)
        df_cleaned = df_renamed.dropna(how='all')
        df_cleaned = df_cleaned.drop_duplicates()
        df_cleaned = df_cleaned.reset_index(drop=True)

        df_cleaned['is_examinable'] = df_cleaned['is_examinable'].map(
            {'Y': 'yes', 'N': 'no'})

        df_cleaned['department'] = df_cleaned['department'].apply(
            lambda code: Department.objects.filter(code=code).first()
        )

        # Columns to set to None if null
        none_columns = ['code', 'name', 'is_examinable', 'unit_leader',
                        'course_type', 'lu_type', 'department', 'message']
        # Apply transformations
        df_cleaned[none_columns] = df_cleaned[none_columns].apply(
            lambda col: col.apply(convert_to_none_if_null)
        )
        # Columns to set to None if null
        zero_columns = ['credit', 'phase']
        # Apply transformations
        df_cleaned[zero_columns] = df_cleaned[zero_columns].apply(
            lambda col: col.apply(convert_to_zero_if_null)
        )

        # Create records, skipping None and NaN values
        records = df_cleaned.to_dict('records')

        active_records = []
        number_created = 0
        number_updated = 0
        number_deleted = 0
        for record in records:
            learning_unit = LearningUnit.objects.filter(
                code=record['code']).first()
            if not learning_unit:
                learning_unit = LearningUnit.objects.create(**record)
                number_created += 1
            else:
                number_updated += 1

            for key, value in record.items():
                setattr(learning_unit, key, value)
            learning_unit.save()

            active_records.append(learning_unit.code)

        total_number = LearningUnit.objects.all().count()
        if len(active_records) < total_number:
            number_deleted = LearningUnit.objects.all().count() - len(active_records)

        # Delete Unecessary Lu's
        LearningUnit.objects.all().exclude(code__in=active_records).delete()

        # Populate group
        phases = list(LearningUnit.objects.all().values_list(
            'phase', flat=True).distinct())

        # Delete phases that don't exist in LearningUnit
        SystemEditMode.objects.exclude(group__in=phases).delete()

        # Create new phases that don't exist in SystemEditMode
        existing_phases = SystemEditMode.objects.values_list(
            'group', flat=True)
        for phase in phases:
            if phase not in existing_phases:
                SystemEditMode.objects.get_or_create(group=phase)

        RelationshipReconnector.reconnect_lu(ScheduleAssessment)
        RelationshipReconnector.reconnect_lu(StatusLearningUnit)

        print("Learning Unit")
        print(f"""Created {number_created} new records.\nUpdated {
              number_updated} existing records.\nDeleted {number_deleted} existing records.""")

    elif file_type == 'moderation_method':
        # Convert to a list of dictionaries
        data.columns = data.columns.str.lower()
        key_mapping = {'moderation method': 'description'}
        df_renamed = data.rename(columns=key_mapping)
        df_renamed['slug'] = df_renamed['description'].apply(slugify)
        df_cleaned = df_renamed.dropna(how='all')
        df_cleaned = df_cleaned.drop_duplicates()
        df_cleaned = df_cleaned.reset_index(drop=True)

        # Columns to set to None if null
        none_columns = ['description']

        # Apply transformations
        df_cleaned[none_columns] = df_cleaned[none_columns].apply(
            lambda col: col.apply(convert_to_none_if_null)
        )

        # Create records, skipping None and NaN values
        records = df_cleaned.to_dict('records')

        active_records = []
        number_created = 0
        number_updated = 0
        number_deleted = 0
        for record in records:
            moderationmethod = ModerationMethod.objects.filter(
                slug=record['slug']).first()
            if not moderationmethod:
                moderationmethod = ModerationMethod.objects.create(**record)
                number_created += 1
            else:
                number_updated += 1

            for key, value in record.items():
                setattr(moderationmethod, key, value)
            moderationmethod.save()

            active_records.append(moderationmethod.slug)

        total_number = ModerationMethod.objects.all().count()
        if len(active_records) < total_number:
            number_deleted = ModerationMethod.objects.all().count() - len(active_records)

        # Delete Unecessary Lu's
        ModerationMethod.objects.all().exclude(slug__in=active_records).delete()

        RelationshipReconnector.reconnect_moderation(ScheduleAssessment)

        print("Moderation Method")
        print(f"""Created {number_created} new records.\nUpdated {
              number_updated} existing records.\nDeleted {number_deleted} existing records.""")

    elif file_type == 'course':
        # Convert to a list of dictionaries
        data.columns = data.columns.str.lower()
        key_mapping = {'coursecode': 'code', 'coursename': 'name'}
        df_renamed = data.rename(columns=key_mapping)
        df_cleaned = df_renamed.dropna(how='all')
        df_cleaned = df_cleaned.drop_duplicates()
        df_cleaned = df_cleaned.reset_index(drop=True)

        # Columns to set to None if null
        none_columns = ['code', 'name']
        # Apply transformations
        df_cleaned[none_columns] = df_cleaned[none_columns].apply(
            lambda col: col.apply(convert_to_none_if_null)
        )

        # Create records, skipping None and NaN values
        records = df_cleaned.to_dict('records')

        active_records = []
        number_created = 0
        number_updated = 0
        number_deleted = 0
        for record in records:
            course = Course.objects.filter(code=record['code']).first()
            if not course:
                course = Course.objects.create(**record)
                number_created += 1
            else:
                number_updated += 1

            for key, value in record.items():
                setattr(course, key, value)
            course.save()

            active_records.append(course.code)

        total_number = Course.objects.all().count()
        if len(active_records) < total_number:
            number_deleted = Course.objects.all().count() - len(active_records)

        # Delete Unecessary Lu's
        Course.objects.all().exclude(code__in=active_records).delete()

        print("Course")
        print(f"""Created {number_created} new records.\nUpdated {
              number_updated} existing records.\nDeleted {number_deleted} existing records.""")

    elif file_type == 'course_lu_map':
        # Convert to a list of dictionaries
        data.columns = data.columns.str.lower()
        key_mapping = {'acadyear': 'academic_year', 'semester': 'semester',
                       'coursecode': 'course_code', 'yearofstudy': 'year_study', 'lucode': 'lu'}
        df_renamed = data.rename(columns=key_mapping)

        def find_course(x):
            course = Course.objects.filter(code=x).first()
            return course

        df_renamed['course_code'] = df_renamed['course_code'].apply(
            lambda x: find_course(x))
        df_renamed['lu'] = df_renamed['lu'].apply(lambda x: LearningUnit.objects.filter(
            code=x).first() if LearningUnit.objects.filter(code=x).exists() else None)
        df_cleaned = df_renamed.dropna(how='all')
        df_cleaned = df_cleaned.drop_duplicates()
        df_cleaned = df_renamed.dropna(subset=['course_code', 'lu'])
        df_cleaned = df_cleaned.reset_index(drop=True)

        # Columns to set to None if null
        none_columns = ['academic_year', 'semester',
                        'course_code', 'year_study', 'lu']
        # Apply transformations
        df_cleaned[none_columns] = df_cleaned[none_columns].apply(
            lambda col: col.apply(convert_to_none_if_null)
        )

        records = df_cleaned.to_dict('records')

        active_records = []
        number_created = 0
        number_updated = 0
        number_deleted = 0
        for record in records:
            courselumap = CourseLuMap.objects.filter(course_code=record['course_code'],
                                                     lu=record['lu'],
                                                     academic_year=record['academic_year'],
                                                     semester=record['semester'],
                                                     year_study=record['year_study']).first()
            if not courselumap:
                courselumap = CourseLuMap.objects.create(**record)
                number_created += 1
            else:
                number_updated += 1

            for key, value in record.items():
                setattr(courselumap, key, value)
            courselumap.save()

            active_records.append(courselumap.id)

        total_number = CourseLuMap.objects.all().count()

        if len(active_records) < total_number:
            number_deleted = CourseLuMap.objects.all().count() - len(active_records)

        # Delete Unecessary Lu's
        CourseLuMap.objects.all().exclude(id__in=active_records).delete()

        print("CourseLuMap")
        print(f"""Created {number_created} new records.\nUpdated {
              number_updated} existing records.\nDeleted {number_deleted} existing records.""")

    elif file_type == 'department':
        data.columns = data.columns.str.lower()
        key_mapping = {'dept': 'code',
                       'deptname': 'name', 'deptowner': 'owner'}
        df_renamed = data.rename(columns=key_mapping)
        df_cleaned = df_renamed.dropna(how='all')
        df_cleaned = df_cleaned.drop_duplicates()
        df_cleaned = df_cleaned.reset_index(drop=True)

        # Columns to set to None if null
        none_columns = ['code', 'name', 'owner']
        # Apply transformations
        df_cleaned[none_columns] = df_cleaned[none_columns].apply(
            lambda col: col.apply(convert_to_none_if_null)
        )

        records = df_cleaned.to_dict('records')

        active_records = []
        number_created = 0
        number_updated = 0
        number_deleted = 0
        for record in records:
            department = Department.objects.filter(code=record['code']).first()
            if not department:
                department = Department.objects.create(**record)
                number_created += 1
            else:
                number_updated += 1

            for key, value in record.items():
                setattr(department, key, value)
            department.save()

            active_records.append(department.id)

        total_number = Department.objects.all().count()

        if len(active_records) < total_number:
            number_deleted = Department.objects.all().count() - len(active_records)

        # Delete Unecessary Lu's
        Department.objects.all().exclude(id__in=active_records).delete()

        print("Department")
        print(f"""Created {number_created} new records.\nUpdated {
              number_updated} existing records.\nDeleted {number_deleted} existing records.""")

    elif file_type == 'assessment':
        # Convert to a list of dictionaries
        data.columns = data.columns.str.lower()
        key_mapping = {'assmmethod': 'method', 'assmtask': 'task',
                       'durationvalue': 'duration', 'notes': 'notes'}
        df_renamed = data.rename(columns=key_mapping)
        df_renamed['duration'] = df_renamed['duration'].apply(
            lambda x: None if str(x).lower() == 'not required' else x)
        df_renamed['slug'] = df_renamed['method'].apply(slugify)
        df_cleaned = df_renamed.dropna(how='all')
        df_cleaned = df_cleaned.drop_duplicates()
        df_cleaned = df_cleaned.reset_index(drop=True)

        # Columns to set to None if null
        none_columns = ['method', 'task', 'duration', 'notes']
        # Apply transformations
        df_cleaned[none_columns] = df_cleaned[none_columns].apply(
            lambda col: col.apply(convert_to_none_if_null)
        )

        # Create records, skipping None and NaN values
        records = df_cleaned.to_dict('records')

        active_records = []
        number_created = 0
        number_updated = 0
        number_deleted = 0
        for record in records:
            assessment = Assessment.objects.filter(slug=record['slug']).first()
            if not assessment:
                assessment = Assessment.objects.create(**record)
                number_created += 1
            else:
                number_updated += 1

            for key, value in record.items():
                setattr(assessment, key, value)
            assessment.save()

            active_records.append(assessment.id)

        total_number = Assessment.objects.all().count()

        if len(active_records) < total_number:
            number_deleted = Assessment.objects.all().count() - len(active_records)

        # Delete Unecessary Lu's
        Assessment.objects.all().exclude(id__in=active_records).delete()

        print("Assessment")
        print(f"""Created {number_created} new records.\nUpdated {
              number_updated} existing records.\nDeleted {number_deleted} existing records.""")

        RelationshipReconnector.reconnect_assessment(ScheduleAssessment)

    elif file_type == 'academic_week':
        # Convert to a list of dictionaries
        data.columns = data.columns.str.lower()
        key_mapping = {'coursetype': 'coursetype', 'lutype': 'lutype', 'maxicacountperweek': 'maxicacountperweek',
                       'startdate': 'startdate', 'term1end': 'term1end', 'lastinstructionalweek': 'lastinstructionalweek'}
        df_renamed = data.rename(columns=key_mapping)

        week_aggregate = transform_to_json_column(df_renamed)['week']

        week_columns = [col for col in df_renamed.columns
                        if re.match(r'week\s*\d+', str(col).lower().strip())]
        # Drop the columns and return new DataFrame
        df_renamed = df_renamed.drop(columns=week_columns)
        df_renamed['weeks'] = week_aggregate

        df_renamed['startdate'] = pd.to_datetime(df_renamed['startdate'])

        # Convert term1end to datetime and then format it
        df_renamed['term1end'] = pd.to_datetime(df_renamed['term1end'])

        df_cleaned = df_renamed.dropna(how='all')

        df_cleaned = df_cleaned.reset_index(drop=True)

        # Columns to set to None if null
        none_columns = ['coursetype', 'lutype',
                        'lastinstructionalweek', 'weeks']
        # Apply transformations
        df_cleaned[none_columns] = df_cleaned[none_columns].apply(
            lambda col: col.apply(convert_to_none_if_null)
        )
        df_cleaned['startdate'] = df_cleaned['startdate'].astype(
            object).where(df_cleaned['startdate'].notna(), None)
        df_cleaned['term1end'] = df_cleaned['term1end'].astype(
            object).where(df_cleaned['term1end'].notna(), None)

        df_cleaned['maxicacountperweek'] = df_cleaned['maxicacountperweek'].apply(
            convert_to_zero_if_null)

        # Create records, skipping None and NaN values
        records = df_cleaned.to_dict('records')

        active_records = []
        number_created = 0
        number_updated = 0
        number_deleted = 0
        for record in records:
            academicweek = AcademicWeek.objects.filter(
                coursetype=record['coursetype'], lutype=record['lutype']).first()
            if not academicweek:
                academicweek = AcademicWeek.objects.create(**record)
                number_created += 1
            else:
                number_updated += 1

            for key, value in record.items():
                setattr(academicweek, key, value)
            academicweek.save()

            active_records.append(academicweek.id)

        total_number = AcademicWeek.objects.all().count()

        if len(active_records) < total_number:
            number_deleted = AcademicWeek.objects.all().count() - len(active_records)

        # Delete Unecessary Lu's
        AcademicWeek.objects.all().exclude(id__in=active_records).delete()

        print("CourseTypeLuTypeCalendar")
        print(f"""Created {number_created} new records.\nUpdated {
              number_updated} existing records.\nDeleted {number_deleted} existing records.""")

    elif file_type == 'user':
        data.columns = data.columns.str.lower()
        key_mapping = {'email': 'email', 'role': 'role', 'status': 'status'}
        df_renamed = data.rename(columns=key_mapping)
        df_cleaned = df_renamed.dropna(how='all')
        df_cleaned = df_cleaned.drop_duplicates()
        df_cleaned = df_cleaned.reset_index(drop=True)

        # Columns to set to None if null
        none_columns = ['email', 'role', 'status']
        # Apply transformations
        df_cleaned[none_columns] = df_cleaned[none_columns].apply(
            lambda col: col.apply(convert_to_none_if_null)
        )
        # List of required columns that must have values
        required_columns = ['email', 'role', 'status']
        # Remove rows where any of the required columns is null/empty
        df_cleaned = df_cleaned[df_cleaned[required_columns].notna().all(axis=1)]

        df_cleaned = df_cleaned.apply(lambda x: x.str.strip() if isinstance(x.iloc[0], str) else x)

        # Then apply email validation and remove invalid emails
        df_cleaned = df_cleaned[df_cleaned['email'].apply(is_valid_email)]

        records = df_cleaned.to_dict('records')

        active_records = []
        number_created = 0
        number_updated = 0
        number_failed = 0 
        number_deleted = 0

        for record in records:
            try:
                email = record['email']
                user = User.objects.filter(email=email).first()
                username = email.split("@")[0]
                if not user:
                    user = User.objects.create(email=record['email'],username=username)
                    number_created += 1

                    #send email for new user
                    user_email_template = CREATE_USER_EMAIL_TEMPLATE.format(
                        name=user.first_name, support_email=get_SUPPORT_EMAIL())
                    payload = {
                        'subject': CREATE_USER_EMAIL_TITLE,
                        'message': dedent(user_email_template).strip(),
                        'destination_email': [email]
                    }
                    send_text_email_bg_jobs(payload=payload)

                else:
                    number_updated += 1

                user.first_name = username

                role_, _ = Role.objects.get_or_create(user=user)
                role_.status = record['status']
                role_.type = record['role']
                role_.save()

                user.save()

                active_records.append(user.id)
            except:
                number_failed += 1

        print(f"""Created {number_created} new records.\nUpdated {number_updated} existing records.""")
        
        return number_created,number_updated,number_failed


# Define functions for transformations
def convert_to_none_if_null(x):
    return None if pd.isna(x) or str(x).strip() == 'NaT' else x


def convert_to_zero_if_null(x):
    return 0 if pd.isna(x) or str(x).strip() in ['NaT', 'NaN'] else x


def activity_log(request):

    filter_by = request.GET.get("filter-by", None)
    print(filter_by)
    search = request.GET.get("search", None)
    sort = request.GET.get('sort', None)
    sort_col = request.GET.get('sort-col', 'datetime')

    if sort == "asc":
        sortvalue = sort_col
    else:
        sortvalue = '-' + sort_col

    filter_condition = Q()
    message = ""
    if search:
        filter_condition_search = Q(content_type__model__icontains=search) | Q(
            user__email__icontains=search)

        # # Models that have LU field
        # models_with_lu = ['model1', 'model2', 'model3']
        # # Add LU condition only for specific models
        # filter_condition_search |= Q(
        #     Q(content_type__model__in=models_with_lu) &
        #     Q(content_type__model_lu__code=search)
        # )

        # filter_condition_search |= Q(
        #     Q(content_type__model__in=models_with_lu) &
        #     Q(content_type__model_lu__department=search)
        # )

        filter_condition &= filter_condition_search

    data_from = request.GET.get("date-time-from", None)
    data_to = request.GET.get("date-time-to", None)
    if filter_by == 'timestamp':
        if data_from:
            date_from = parse_date(data_from)
            date_from = timezone.make_aware(
                datetime.combine(date_from, datetime.min.time())
            )
            filter_condition &= Q(datetime__gte=date_from)

        if data_to:
            date_to = parse_date(data_to)
            date_to = timezone.make_aware(
                datetime.combine(date_to, datetime.max.time())
            )
            filter_condition &= Q(datetime__lte=date_to)

    lucode = request.GET.get("lu-code", None)
    if lucode:
        lu = LearningUnit.objects.filter(code=lucode).first()
        if lu:
            filter_condition &= Q(
                object_repr__icontains=f'{lu.name}'
            )
        else:
            message = f'No log found for LU - {lucode}'

    email = request.GET.get("email", None)
    if filter_by == "email":
        if email:
            filter_condition &= Q(user__email__icontains=email)
        else:
            message = f'No log found for email - {email}'

    department = request.GET.get("department", None)
    if filter_by == 'department':
        if department:
            selected_department = Department.objects.filter(
                code=department).first()

            if selected_department:
                learning_unit_names = selected_department.learning_units.all().values_list('name',
                                                                                           flat=True)

                # Create a filter condition that matches any of the learning unit names
                name_conditions = Q()
                for name in learning_unit_names:
                    name_conditions |= Q(object_repr__icontains=name)

                filter_condition &= name_conditions
            else:
                message = f'No log found for Department - {department}'

    module = request.GET.get("module", None)
    if filter_by == 'module':
        if module:
            filter_condition &= Q(content_type__model__icontains=module)

    action = request.GET.get("action", None)
    if filter_by == 'action':
        if action:
            filter_condition &= Q(event_type=action)

    crud_events = CRUDEvent.objects.filter(Q(filter_condition) & Q(
        user__isnull=False) & Q(user__email__isnull=False)).order_by(sortvalue)

    paginator = Paginator(crud_events, POSTS_PER_PAGE)
    page = request.GET.get('page', 1)
    if page:
        page_content = paginator.page(page)
        crud_events = page_content.object_list
    else:
        page_content = paginator.page(1)
    context = {
        "activity_logs": crud_events,
        "search": search,
        "message": message,
        "page": page,
        "sort": sort,
        'sort_col': sort_col,
        'page_content': page_content,
        'paginator': paginator,

        'filter_by': filter_by,
        'lu_code': lucode,
        'email': email,
        'data_from': data_from,
        'data_to': data_to,
        'action': action,
        'module': module,
        'department': department,
    }
    return render(request, 'system-management/activity-log.html', context)


@login_required
@sending_email_if_timeout(timeout_seconds=20)
def export_activity_table(request):
    if request.method == "POST":
        search_activity = request.POST.get('search-export-log', None)

        filter_by = request.POST.get("filter-by", None)
        data_from = request.POST.get("date-time-from", None)
        data_to = request.POST.get("date-time-to", None)
        filter_condition=Q()
        if filter_by == 'timestamp':
            if data_from:
                date_from = parse_date(data_from)
                date_from = timezone.make_aware(
                    datetime.combine(date_from, datetime.min.time())
                )
                filter_condition &= Q(datetime__gte=date_from)

            if data_to:
                date_to = parse_date(data_to)
                date_to = timezone.make_aware(
                    datetime.combine(date_to, datetime.max.time())
                )
                filter_condition &= Q(datetime__lte=date_to)

        lucode = request.POST.get("lu-code", None)
        if lucode:
            lu = LearningUnit.objects.filter(code=lucode).first()
            if lu:
                filter_condition &= Q(
                    object_repr__icontains=f'{lu.name}'
                )
            else:
                message = f'No log found for LU - {lucode}'

        email = request.POST.get("email", None)
        if filter_by == "email":
            if email:
                filter_condition &= Q(user__email__icontains=email)
            else:
                message = f'No log found for email - {email}'

        department = request.POST.get("department", None)
        if filter_by == 'department':
            if department:
                selected_department = Department.objects.filter(
                    code=department).first()

                if selected_department:
                    learning_unit_names = selected_department.learning_units.all().values_list('name',
                                                                                            flat=True)

                    # Create a filter condition that matches any of the learning unit names
                    name_conditions = Q()
                    for name in learning_unit_names:
                        name_conditions |= Q(object_repr__icontains=name)

                    filter_condition &= name_conditions
                else:
                    message = f'No log found for Department - {department}'

        module = request.POST.get("module", None)
        if filter_by == 'module':
            if module:
                filter_condition &= Q(content_type__model__icontains=module)

        action = request.POST.get("action", None)
        if filter_by == 'action':
            if action:
                filter_condition &= Q(event_type=action)

        wb = Workbook()
        sheet = wb.active
        sheet.title = "Activity Log"

        # Create a bold font style
        bold_font = Font(bold=True)
        headers = ["TIMESTAMP",	"EMAIL", "DEPARTMENT",
                   "LU CODE", "MODULE", "ACTION", "DETAILS OF CHANGE"]
        row = 1
        for col, value in enumerate(headers, start=1):
            cell = sheet.cell(row=row, column=col, value=value)
            cell.font = bold_font

        if search_activity:
            filter_condition &= Q(content_type__model__icontains=search_activity) | Q(
                user__email__icontains=search_activity)
            crud_events = CRUDEvent.objects.filter(filter_condition).order_by('-datetime').values_list(FormatDateTime(
                'datetime'), 'user__email','object_repr', 'content_type__model', GetDisplayValue('event_type', CRUDEvent), 'changed_fields')
        else:
            crud_events = CRUDEvent.objects.filter(filter_condition).order_by('-datetime').values_list(FormatDateTime(
                'datetime'), 'user__email','object_repr', 'content_type__model', GetDisplayValue('event_type', CRUDEvent), 'changed_fields')

        # Process the results to add the None value
        processed_events = []
        for event in crud_events:
            processed_event = list(event)
            
            department = get_department(event[2])
            if department:department = department.name

            lu_code = get_lu_code(event[2])
            processed_event.insert(2,department )  # Insert None after user__email
            processed_event.insert(3, lu_code)
            processed_events.append(processed_event)

        for row, item in enumerate(processed_events, start=2):
            for col, value in enumerate(item, start=1):
                sheet.cell(row=row, column=col, value=value)

        # Adjust column widths to fit content
        for col in range(1, len(headers) + 1):
            max_length = 0
            column_letter = get_column_letter(col)

            # Iterate through all cells in the column
            for cell in sheet[column_letter]:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass

            adjusted_width = (max_length + 2) * 1.2
            sheet.column_dimensions[column_letter].width = adjusted_width

        # Create the HttpResponse object
        file_name = "Activity Log"
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = f'attachment; filename={
            file_name}.xlsx'

        wb.save(response)

        system_management_email_template = SYSTEM_MANAGEMENT_EMAIL_TEMPLATE.format(
            name=request.user.first_name, file_name='Activity Logs', support_email=get_SUPPORT_EMAIL())
        system_management_email_template = dedent(
            system_management_email_template).strip()
        return response, {"wb": wb,
                          "file_name": f"{file_name}.xlsx",
                          "email_subject": f"{file_name} - is ready for Download",
                          "email_body": system_management_email_template}


def filter_activity_log(request):
    if (request.method == "GET"):
        filterValue = request.GET.get('filter-by', 'timestamp').lower()
        if (filterValue == "timestamp"):
            return render(request, 'system-management/filter-by/timestamps.html')
        elif (filterValue == "lucode"):
            context = {
                "learning_units": LearningUnit.objects.all()
            }
            return render(request, 'system-management/filter-by/lu-code.html', context)
        elif (filterValue == "email"):
            return render(request, 'system-management/filter-by/email.html')
        elif (filterValue == "department"):
            context = {
                'departments': Department.objects.all()
            }
            return render(request, 'system-management/filter-by/department.html', context)
        elif (filterValue == "module"):
            return render(request, 'system-management/filter-by/module.html')
        elif (filterValue == "action"):
            return render(request, 'system-management/filter-by/action.html')


@ login_required
def role_management(request):

    form_type = request.GET.get("form_type")

    permissions_ = Permission.objects.values('page', 'type', 'create', 'update', 'delete', 'view').annotate(
        page_display=Case(
            *[When(page=choice[0], then=Value(choice[1]))
              for choice in Permission._meta.get_field('page').choices],
            default=Value('Unknown'),
            output_field=CharField()
        )
    ).order_by('page')

    combined_permissions = {}
    for perm in permissions_:
        page = perm['page']
        if page not in combined_permissions:
            combined_permissions[page] = {
                "page": page,
                'page_display': perm['page_display'],
                'admin_create': False, 'admin_update': False, 'admin_delete': False, 'admin_view': False,
                'user_create': False, 'user_update': False, 'user_delete': False, 'user_view': False
            }

        prefix = 'admin_' if perm['type'] == 'admin' else 'user_'
        for action in ['create', 'update', 'delete', 'view']:
            combined_permissions[page][f'{prefix}{action}'] = perm[action]
    combined_permissions = list(combined_permissions.values())

    context = {"permissions": combined_permissions, "form_type": form_type,
               "permission": check_permission(request.user, 'system_management')}
    return render(request, 'system-management/role-management.html', context)


@ login_required
def update_role_management(request):
    if request.method == "POST":

        permission_pages = request.POST.getlist("permission-page", [])

        for permission_page in permission_pages:
            permission = Permission.objects.filter(
                page=permission_page, type='admin').first()
            admin_create = request.POST.get(
                f"{permission_page}-admin-create", None)
            admin_update = request.POST.get(
                f"{permission_page}-admin-update", None)
            admin_delete = request.POST.get(
                f"{permission_page}-admin-delete", None)
            admin_view = request.POST.get(
                f"{permission_page}-admin-view", None)
            permission.create = True if admin_create else False
            permission.update = True if admin_update else False
            permission.delete = True if admin_delete else False
            permission.view = True if admin_view else False
            permission.save()

            permission = Permission.objects.filter(
                page=permission_page, type='user').first()
            user_create = request.POST.get(
                f"{permission_page}-user-create", None)
            user_update = request.POST.get(
                f"{permission_page}-user-update", None)
            user_delete = request.POST.get(
                f"{permission_page}-user-delete", None)
            user_view = request.POST.get(f"{permission_page}-user-view", None)
            permission.create = True if user_create else False
            permission.update = True if user_update else False
            permission.delete = True if user_delete else False
            permission.view = True if user_view else False
            permission.save()

    return redirect('system_management:roleManagement')


@ login_required
def export_role_management_table(request):

    wb = Workbook()
    sheet = wb.active

    sheet.title = "User List"

    # Create a bold font style
    # Define headers
    headers = ['MODULE', 'ADMIN', '', '', '', 'USER', '', '', '']
    subheaders = ['Access', 'Create', 'Update', 'Delete',
                  'View', 'Create', 'Update', 'Delete', 'View']

    # Add headers
    for col, header in enumerate(headers, start=1):
        cell = sheet.cell(row=1, column=col)
        cell.value = header
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="D3D3D3",
                                end_color="D3D3D3", fill_type="solid")
        cell.alignment = Alignment(horizontal='center', vertical='center')

    # Add subheaders
    for col, subheader in enumerate(subheaders, start=1):
        cell = sheet.cell(row=2, column=col)
        cell.value = subheader
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal='center', vertical='center')

    # Get unique pages from the Permission model
    pages = Permission.objects.order_by(
        'page').values_list('page', flat=True).distinct()

    # Add data from the Permission model
    for row, page in enumerate(pages, start=3):
        sheet.cell(row=row, column=1, value=page.replace('_', ' ').title())

        for type_index, perm_type in enumerate(['admin', 'user']):
            perms = Permission.objects.filter(
                page=page, type=perm_type).first()
            if perms:
                offset = 2 + type_index * 4
                # sheet.cell(row=row, column=offset, value='✓' if any([perms.create, perms.update, perms.delete, perms.view]) else '')
                sheet.cell(row=row, column=offset + 0,
                           value='✓' if perms.create else '')
                sheet.cell(row=row, column=offset + 1,
                           value='✓' if perms.update else '')
                sheet.cell(row=row, column=offset + 2,
                           value='✓' if perms.delete else '')
                sheet.cell(row=row, column=offset + 3,
                           value='✓' if perms.view else '')

    # Apply borders
    thin_border = Border(left=Side(style='thin'), right=Side(
        style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))
    for row in sheet.iter_rows(min_row=1, max_row=sheet.max_row, min_col=1, max_col=sheet.max_column):
        for cell in row:
            cell.border = thin_border

    # Merge cells for main headers
    sheet.merge_cells('B1:E1')
    sheet.merge_cells('F1:I1')

    # Adjust column widths
    sheet.column_dimensions['A'].width = 20

    # Create the HttpResponse object
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename=Role Management.xlsx'

    wb.save(response)

    return response


def edit_mode(request):
    year = 2

    return render(request, 'system-management/set-edit-mode.html', {"year": year})


def form_edit_mode(request):
    id = request.GET.get('id')
    return render(request, 'system-management/form-edit-mode.html', {"id": id})


def convert_to_frontend_format(backend_data):
    """Convert the exported backend data to frontend fields format"""
    frontend_fields = []
    for variable in backend_data['variables']:
        field = {
            'label': variable['label'],
            # Adding 'Field' suffix to match your format
            'name': variable['name'],
            'operators': []
        }
        # If the variable has options (for select fields), add them
        if variable['options']:
            field['options'] = [
                {'label': opt, 'name': opt.replace(' ', '_')}
                for opt in variable['options']
            ]
        # Add operators based on the field_type
        operators = backend_data['variable_type_operators'].get(
            variable['field_type'], [])
        for op in operators:
            if op['name'] == "is_false" or op['name'] == 'is_true':
                fieldtype = 'boolean'
            elif op['input_type'] == 'numeric':
                fieldtype = 'number'
            else:
                fieldtype = op['input_type']

            operator = {
                'label': op['label'].lower(),
                'name': op['name'],
                'fieldType': fieldtype
            }
            field['operators'].append(operator)
        frontend_fields.append(field)
    return frontend_fields


def rule_configuration(request):
    business_rule = export_rule_data(
        ScheduleAssessmentVariables, ScheduleAssessmentActions)

    business_rule_fields = convert_to_frontend_format(business_rule)
    storage_of_business_rule = BusinessRules.objects.filter(
        slug='rule').first()

    conditions_ica_schedule = {"all": []}
    conditions_ica_detail = {"all": []}

    rules_action = []
    if storage_of_business_rule:
        if storage_of_business_rule.stage1:
            rules = ast.literal_eval(storage_of_business_rule.stage1)
            conditions_ica_schedule = rules[0]["conditions"]

        if storage_of_business_rule.stage2:
            rules = ast.literal_eval(storage_of_business_rule.stage2)
            conditions_ica_detail = rules[0]["conditions"]

    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
    filter_conditions = Q(courselumap__academic_year=SYSTEM_CURRENT_YEAR.current_year,
                          courselumap__semester=SYSTEM_CURRENT_YEAR.current_semester)
    learing_units = LearningUnit.objects.filter(
        filter_conditions).order_by('code')

    context = {
        "business_rule": business_rule,
        "business_rule_fields": json.dumps(business_rule_fields),
        "rules_action": rules_action,

        "conditions_ica_schedule": json.dumps(conditions_ica_schedule),
        "conditions_ica_detail": json.dumps(conditions_ica_detail),

        "learning_units": learing_units,
        "permission": check_permission(request.user, 'system_management'),
        'storage_of_business_rule': storage_of_business_rule
    }
    return render(request, 'system-management/rule-configuration.html', context)


def onchange_business_rule(request):
    valueSelect = request.GET.get('select-variable')
    fieldType = request.GET.get('data-type')
    level = request.GET.get('data-level')
    group = request.GET.get('data-group')
    condition = request.GET.get('data-condition')

    business_rule = export_rule_data(
        ScheduleAssessmentVariables, ScheduleAssessmentActions)

    context = {
        "business_rule": business_rule,
        "fieldType": fieldType,
        "valueSelect": valueSelect,
        "level": level,
        "group": group,
        "condition": condition
    }

    return render(request, 'system-management/onchange-business-rule.html', context)


def add_condition(request):
    business_rule = export_rule_data(
        ScheduleAssessmentVariables, ScheduleAssessmentActions)

    context = {
        "business_rule": business_rule,
    }
    return render(request, 'system-management/add-condition.html', context)


def add_sub_condition(request):
    level = int(request.GET.get('level'))

    business_rule = export_rule_data(
        ScheduleAssessmentVariables, ScheduleAssessmentActions)
    context = {
        "business_rule": business_rule,
        "level": level,
    }
    return render(request, 'system-management/add-sub-condition.html', context)


def ruleSubmit(request):
    if request.method == "POST":

        data = json.loads(request.body)

        # Now you can access your data
        stage = data.get('stage')
        conditions = data.get('conditions')
        actions = data.get('actions')
        status = data.get('status')

        rules = [
            # Main rule combining all validation conditions
            {
                "conditions": conditions,
                "actions": actions
            }
        ]
        storage_of_business_rule = BusinessRules.objects.filter(
            slug='rule').first()

        if stage == 'stage1':
            storage_of_business_rule.stage1 = rules
            storage_of_business_rule.status_admin_break_rule_stage1 = status
        elif stage == 'stage2':
            storage_of_business_rule.stage2 = rules
            storage_of_business_rule.status_admin_break_rule_stage2 = status
        storage_of_business_rule.save()

        # Return a response
        return JsonResponse({
            'status': 'success',
            'message': 'Data received successfully'
        })

    return JsonResponse({'status': 'error', 'message': 'Invalid request method'}, status=400)


def ruleTestSubmit(request):
    if request.method == 'POST':
        data = json.loads(request.body)
        learning_unit_id = data.get('learning_unit_id')
        conditions = data.get('conditions')
        actions = data.get('actions')
        conditions_detail = data.get('conditions_detail')
        actions_detail = data.get('actions_detail')

        learning_unit = LearningUnit.objects.get(id=learning_unit_id)
        storage_of_business_rule = BusinessRules.objects.filter(
            slug='rule').first()

        rules = [
            {
                "conditions": conditions,
                "actions": actions
            }
        ]
        storage_of_business_rule.stage1_test = rules

        rules = [
            {
                "conditions": conditions_detail,
                "actions": actions_detail
            }
        ]
        storage_of_business_rule.stage2_test = rules
        storage_of_business_rule.save()

        # Generate the URL
        redirect_url = reverse('schedule_assessment:icaSchedule', kwargs={
                               'id': learning_unit.id})+"?test_mode=true"
        # Return JSON response with the URL
        return JsonResponse({
            'status': 'success',
            'redirect_url': redirect_url
        })

    return HttpResponse(200)


def process_conditions_data(data, level=0):
    conditions = {}
    group_pattern = f'rules[{level}][group]'

    group_indices = set()
    for key in data.keys():
        if key.startswith(group_pattern):
            try:
                parts = key.split('][')
                if len(parts) >= 3:
                    group_idx = int(parts[2].strip('[]'))
                    group_indices.add(group_idx)
            except (IndexError, ValueError):
                continue

    if group_indices:
        first_group_type_key = f'rules[{
            level}][group][{min(group_indices)}][type]'
        parent_type = data.get(first_group_type_key, 'all').lower()
        conditions[parent_type] = []

        for group_idx in sorted(group_indices):
            group_type_key = f'rules[{level}][group][{group_idx}][type]'
            group_type = data.get(group_type_key, 'all').lower()

            if level == 0:
                next_level_conditions = process_conditions_data(data, level=1)
                if next_level_conditions:
                    conditions[parent_type].extend(
                        next_level_conditions.get(
                            list(next_level_conditions.keys())[0], [])
                    )
                break

            group_conditions = []

            condition_indices = set()
            for key in data.keys():
                if key.startswith(f'rules[{level}][group][{group_idx}][conditions]'):
                    try:
                        idx = int(key.split('][')[4].strip('[]'))
                        condition_indices.add(idx)
                    except (IndexError, ValueError):
                        continue

            for idx in sorted(condition_indices):
                name_key = f'rules[{level}][group][{
                    group_idx}][conditions][{idx}][name]'
                operator_key = f'rules[{level}][group][{
                    group_idx}][conditions][{idx}][operator]'
                value_key = f'rules[{level}][group][{
                    group_idx}][conditions][{idx}][value]'

                if name_key in data and operator_key in data and value_key in data:
                    value = data[value_key]
                    if value.lower() == 'true':
                        value = True
                    elif value.lower() == 'false':
                        value = False
                    elif value.isdigit():
                        value = int(value)

                    condition = {
                        'name': data[name_key],
                        'operator': data[operator_key].replace(' ', '_'),
                        'value': value
                    }
                    group_conditions.append(condition)

            # Check for nested conditions at next level
            if group_idx == 0:  # Only for first group of each level
                next_level = level + 1
                if any(key.startswith(f'rules[{next_level}]') for key in data.keys()):
                    next_level_conditions = process_conditions_data(
                        data, next_level)
                    if next_level_conditions and next_level_conditions.get(list(next_level_conditions.keys())[0]):
                        for nested_group in next_level_conditions.get(list(next_level_conditions.keys())[0]):
                            group_conditions.append(nested_group)

            if group_conditions:
                conditions[parent_type].append({group_type: group_conditions})

    return conditions


def filter_by_lu_status(request):
    filter_by = request.GET.get('filter-by-lu-status', None)
    departments = Department.objects.all().order_by('name')

    if filter_by == "department":
        context = {
            "departments": departments
        }
        html = 'department.html'
    else:
        context = {}
        html = 'status.html'

    return render(request, f'system-management/filter-lu-status/{html}', context)
