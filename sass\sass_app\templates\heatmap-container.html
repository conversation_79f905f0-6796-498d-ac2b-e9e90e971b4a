{% load static %}
{% load mathfilters %}
{% block content %}


<section class="mt-5 week-heatmap">
    <div class="card card-headmap card-shadow week-heatmap-inner">
        <h2 class="title-section">Weeks Heatmap</h2>
        <p class="fst-italic">Academic Year {{system_current_year.current_year}} - Semester {{system_current_year.current_semester}}</p>
   
       
            <div class="row mt-4">
               
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="department">Department</label>
                        <div class="wrapper-select">
                            <select onchange="triggerChangeDepartmentEvent(this)" title="department" class="select-department form-control" name="filter-department" multiple="multiple"
                            
                                hx-get="{% url 'sass_app:heatmap_container' %}" 
                                hx-trigger="htmx-changed" 
                                hx-swap="outerHTML" 
                                hx-target=".week-heatmap"

                                hx-vals='js:{"filter-learning-unit":$(".select-lu").val()}'
                            
                            >

                                {% for department in departments %}
                                    <option value="{{department.code}}" {% if department.code in selected_filter_departement_codes %} selected {% endif %}>{{department.name}}</option>
                                {% endfor %}    
                               
                            </select>
                        </div>

                    </div>
                </div>
               
                <div class="col-md-6 filter-learning-unit">
                    <div class="form-group">
                        <label for="filter-learning-unit">LU Code</label>
                        <div class="wrapper-select">
                            <select onchange="triggerChangeDepartmentEvent(this)" title="Learning Unit" class="select-lu form-control" name="filter-learning-unit" multiple="multiple"
                            
                                hx-get="{% url 'sass_app:heatmap_container' %}" 
                                hx-trigger="htmx-changed" 
                                hx-swap="outerHTML" 
                                hx-target=".week-heatmap"
                                hx-vals='js:{"filter-department":$(".select-department").val()}'
                            
                            >
                               
                                {% for lu_code in lu_codes %}
                                    <option value="{{lu_code.code}}" {% if lu_code.code in selected_filter_lu_codes %} selected {% endif %}>{{lu_code.code}}</option>
                                {% endfor %}      
                                                     
                            </select>
                         
                        </div>
                    </div>
                   
                </div>
               
            </div>

        <div class="position-relative h-200">
            <div class="result-heatmap" 

                hx-get="{% url 'sass_app:heatmap' %}" 
                hx-trigger="load" 
                hx-swap="afterBegin" 
                hx-target="this" 
                hx-indicator=".loading-heatmap"
                
                hx-vals='js:{"filter-learning-unit":$(".select-lu").val(),"filter-department":$(".select-department").val()}'
                >
            </div>
            <div class="loading loading-heatmap htmx-indicator">
                <img src="{% static 'img/loading.gif' %}" alt="loading">
            </div>
        </div>

        <div class="legend-headmap">
            <div class="item-legend"><span class="bg-green"></span> Available</div>
            <div class="item-legend"><span class="bg-yellow"></span> Limited</div>
            <div class="item-legend"><span class="bg-red"></span> Unavailable</div>
            <div class="item-legend"><span class="bg-black"></span> Blocked</div>
        </div> 
        {% comment %} <div class="box-under-construction">
           
                <h5>Under Construction</h4>
                <svg width="46" height="44" viewBox="0 0 46 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M43.4083 12.0644C44.1837 11.2907 44.7568 10.3382 45.0773 9.29079C45.3978 8.24337 45.4557 7.13321 45.246 6.05812L41.0519 10.2459C40.8125 10.4805 40.4906 10.612 40.1554 10.612C39.8202 10.612 39.4983 10.4805 39.2589 10.2459L35.7755 6.76248C35.5409 6.52309 35.4095 6.20124 35.4095 5.86602C35.4095 5.53081 35.5409 5.20895 35.7755 4.96956L39.9633 0.77541C38.792 0.550417 37.5818 0.642583 36.4581 1.04237C35.3344 1.44216 34.338 2.13504 33.572 3.04927C32.806 3.96351 32.2983 5.06587 32.1014 6.24223C31.9045 7.41859 32.0256 8.62619 32.4522 9.74001L21.6307 20.3118L20.587 19.2681L22.2902 17.5648C22.3703 17.4774 22.4148 17.3632 22.4148 17.2447C22.4148 17.1261 22.3703 17.0119 22.2902 16.9245L20.9711 15.6118C20.8852 15.5289 20.7704 15.4825 20.651 15.4825C20.5315 15.4825 20.4168 15.5289 20.3308 15.6118L19.6905 16.2522L18.2626 14.8306L18.8132 14.2799C18.9249 14.1612 18.9871 14.0043 18.9871 13.8413C18.9871 13.6783 18.9249 13.5215 18.8132 13.4027L7.00558 1.59503C6.72432 1.31371 6.39041 1.09055 6.0229 0.938296C5.65539 0.786044 5.26148 0.70768 4.86368 0.70768C4.46588 0.70768 4.07198 0.786044 3.70447 0.938296C3.33696 1.09055 3.00303 1.31371 2.72178 1.59503L1.51797 2.79884C1.23665 3.0801 1.01348 3.41402 0.861232 3.78153C0.708979 4.14904 0.630615 4.54294 0.630615 4.94074C0.630615 5.33854 0.708979 5.73245 0.861232 6.09996C1.01348 6.46747 1.23665 6.80139 1.51797 7.08264L13.3256 18.8903C13.4444 19.002 13.6013 19.0641 13.7643 19.0641C13.9273 19.0641 14.0841 19.002 14.2029 18.8903L14.7472 18.3396L16.1751 19.7676L15.5348 20.4079C15.4518 20.4938 15.4055 20.6086 15.4055 20.728C15.4055 20.8475 15.4518 20.9623 15.5348 21.0482L16.8154 22.3673C16.9028 22.4474 17.017 22.4918 17.1356 22.4918C17.2541 22.4918 17.3684 22.4474 17.4557 22.3673L19.159 20.664L20.1835 21.6885L11.2189 30.4418C10.1051 30.013 8.89676 29.8902 7.71943 30.0862C6.5421 30.2822 5.43871 30.7898 4.52385 31.5564C3.609 32.3229 2.91603 33.3204 2.51694 34.4452C2.11784 35.57 2.02717 36.7812 2.25434 37.9529L6.4485 33.7652C6.68789 33.5305 7.00974 33.3991 7.34495 33.3991C7.68017 33.3991 8.00203 33.5305 8.24142 33.7652L11.7248 37.2485C11.9595 37.4879 12.0909 37.8098 12.0909 38.145C12.0909 38.4802 11.9595 38.8021 11.7248 39.0415L7.53705 43.2356C8.70836 43.4606 9.91851 43.3684 11.0422 42.9686C12.166 42.5689 13.1624 41.876 13.9284 40.9617C14.6944 40.0475 15.2021 38.9452 15.399 37.7688C15.5958 36.5924 15.4747 35.3848 15.0481 34.271L24.0127 25.5241L36.243 37.7544C36.068 38.2072 36.0281 38.7011 36.1281 39.1762C36.2281 39.6512 36.4638 40.0871 36.8065 40.431L39.7584 43.3829L43.2994 39.8419L40.3475 36.89C40.0036 36.5473 39.5678 36.3116 39.0927 36.2116C38.6177 36.1116 38.1238 36.1515 37.6709 36.3265L25.4407 24.1538L36.2558 13.5948C37.4679 14.0614 38.7896 14.1651 40.0596 13.8934C41.3297 13.6216 42.4932 12.9861 43.4083 12.0644Z" fill="#BBBBBB"/>
                </svg>
           
        </div> {% endcomment %}
       
    </div>  

    <script>
        
        $(".select-department").select2();
        $('.select-department').select2({
            placeholder: "Select Department Code",
            width: "100%",
        })
    
        $(".select-lu").select2({
            placeholder: "Select LU Code",
            width: "100%",
        });
    
        function triggerChangeDepartmentEvent(elm){
            htmx.trigger(elm, 'htmx-changed');     
    
        }
    </script>


</section>

{% endblock content %}
