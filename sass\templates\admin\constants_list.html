{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
&rsaquo; {{ title }}
</div>
{% endblock %}

{% block content %}
<div class="module filtered">
    <h2>System Constants</h2>
    <p>Displaying safe system constants from settings.py and constant.py (superuser only)</p>
    
    {% if errors %}
    <div class="messagelist">
        {% for error in errors %}
        <div class="warning">{{ error }}</div>
        {% endfor %}
    </div>
    {% endif %}
    
    <div class="results">
        <table id="result_list">
            <thead>
                <tr>
                    <th scope="col">
                        <div class="text"><span>Constant Name</span></div>
                    </th>
                    <th scope="col">
                        <div class="text"><span>Value</span></div>
                    </th>
                    <th scope="col">
                        <div class="text"><span>Source File</span></div>
                    </th>
                </tr>
            </thead>
            <tbody>
                {% for constant in constants %}
                <tr class="{% cycle 'row1' 'row2' %}">
                    <td><strong>{{ constant.key|default:"Unknown" }}</strong></td>
                    <td style="max-width: 400px; word-wrap: break-word;">{{ constant.value|default:"N/A" }}</td>
                    <td>{{ constant.source|default:"Unknown" }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="3">No constants found</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <p class="paginator">
            Total constants: {{ constants|length }}
        </p>
    </div>
</div>

<style>
    #result_list {
        width: 100%;
        border-collapse: collapse;
    }
    #result_list th, #result_list td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
    }
    #result_list th {
        background-color: #f2f2f2;
        font-weight: bold;
    }
    .row1 {
        background-color: #f9f9f9;
    }
    .row2 {
        background-color: #ffffff;
    }
</style>
{% endblock %}