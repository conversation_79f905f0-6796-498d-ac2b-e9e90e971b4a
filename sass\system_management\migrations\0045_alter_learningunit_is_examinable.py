# Generated by Django 5.1 on 2024-11-25 03:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("system_management", "0044_rename_lu_code_courselumap_lu"),
    ]
    def reset_is_examinable(apps, schema_editor):
        LearningUnit = apps.get_model('system_management', 'LearningUnit')
        for learning_unit in  LearningUnit.objects.filter():
            if learning_unit.is_examinable == 'false':
                learning_unit.is_examinable = "no"
            else:
                learning_unit.is_examinable = "yes"
            learning_unit.save()

    operations = [
        migrations.AlterField(
            model_name="learningunit",
            name="is_examinable",
            field=models.CharField(
                choices=[("no", "No"), ("yes", "Yes")], default="no", max_length=10
            ),
        ),
        migrations.RunPython(reset_is_examinable)

    ]
