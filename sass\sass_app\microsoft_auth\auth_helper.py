import msal
from django.conf import settings
import logging
logger = logging.getLogger(__name__)
import requests
import traceback  # Add this at the top with other imports

def load_cache(request):
  # Check for a token cache in the session
  cache = msal.SerializableTokenCache()
  if request.session.get('token_cache'):
    cache.deserialize(request.session['token_cache'])
  return cache

def save_cache(request, cache):
  # If cache has changed, persist back to session
  if cache.has_state_changed:
    request.session['token_cache'] = cache.serialize()

def get_msal_app(cache=None):
  # Initialize the MSAL confidential client
  auth_app = msal.ConfidentialClientApplication(
    settings.AZURE_CLIENT_ID,
    authority=settings.AZURE_AUTHORITY,
    client_credential=settings.AZURE_CLIENT_SECRET,
    verify=settings.AZURE_SSL_VERIFICATION,
    token_cache=cache)
  return auth_app

# Method to generate a sign-in flow
def get_sign_in_flow():
  auth_app = get_msal_app()
  return auth_app.initiate_auth_code_flow(
    settings.AZURE_SCOPES,
    redirect_uri=settings.AZURE_REDIRECT,
    prompt='select_account')

# Method to exchange auth code for access token
def get_token_from_code(request):
  cache = load_cache(request)
  auth_app = get_msal_app(cache)

  # Get the flow saved in session
  flow = request.session.pop('auth_flow', {})
  result = auth_app.acquire_token_by_auth_code_flow(flow, request.GET)
  save_cache(request, cache)

  return result


def store_user(request, user):
  try:
    request.session['user'] = {
      'is_authenticated': True,
      'name': user['displayName'],
      'email': user['mail'] if (user['mail'] != None) else user['userPrincipalName'],
      'timeZone': user['mailboxSettings']['timeZone'] if (user['mailboxSettings']['timeZone'] != None) else 'UTC'
    }
  except Exception as e:
    print(e)

def get_token(request):
  cache = load_cache(request)
  auth_app = get_msal_app(cache)

  accounts = auth_app.get_accounts()
  if accounts:
    result = auth_app.acquire_token_silent(
      settings.AZURE_SCOPES,
      account=accounts[0])
    save_cache(request, cache)

    return result
  
def refresh_access_token(request):
    cache = load_cache(request)
    auth_app = get_msal_app(cache)

    accounts = auth_app.get_accounts()
    if accounts:
        result = auth_app.acquire_token_silent(settings.AZURE_SCOPES, account=accounts[0])
        if 'access_token' in result:
            save_cache(request, cache)
            return result['access_token']
    return None
    
def remove_user_and_token(request):
  if 'token_cache' in request.session:
    del request.session['token_cache']

  if 'user' in request.session:
    del request.session['user']

def get_sign_out_flow(request):


    id_token = request.session.get('id_token')

    # Add the post-logout redirect URI
    post_logout_redirect_uri = request.build_absolute_uri('/')  # or any other page you want to redirect to after logout
    logout_url = f"{settings.AZURE_LOGOUT}"
    logout_url +=f"?post_logout_redirect_uri={post_logout_redirect_uri}"
    if id_token:
        logout_url += f"&id_token_hint={id_token}"
    
    remove_user_and_token(request)

    return logout_url





def test_microsoft_connection():
    try:
        tenant_id = settings.AZURE_TENANT_ID
        test_url = f"https://login.microsoftonline.com/{tenant_id}/v2.0/.well-known/openid-configuration"
        
        logger.info(f"Attempting to connect to: {test_url}")
        
        response = requests.get(test_url, timeout=10,verify=settings.AZURE_SSL_VERIFICATION)
        logger.info(f"Connection Status Code: {response.status_code}")
        logger.info(f"Response Headers: {response.headers}")
        
        if response.status_code == 200:
            return {
                'status': 'success',
                'message': 'Successfully connected to Microsoft',
                'details': {
                    'status_code': response.status_code,
                    'endpoint_tested': test_url,
                    'response_headers': dict(response.headers),
                    'response_content': response.json()
                }
            }
        else:
            error_details = {
                'status_code': response.status_code,
                'endpoint_tested': test_url,
                'response_headers': dict(response.headers),
                'error_message': response.text,  # Get the error message from response
                'request_headers': dict(response.request.headers),  # What we sent
                'request_method': response.request.method,
            }
            
            logger.error(f"Connection failed with details: {error_details}")
            
            return {
                'status': 'error',
                'message': f'Connection failed with status code: {response.status_code}',
                'details': error_details
            }
            
    except requests.exceptions.SSLError as e:
        logger.error(f"SSL Error: {str(e)}")
        return {
            'status': 'error',
            'message': 'SSL/TLS connection failed',
            'details': {
                'error_type': 'SSL Error',
                'error_description': str(e),
                'endpoint_tested': test_url
            }
        }
    except requests.exceptions.ConnectionError as e:
        logger.error(f"Connection Error: {str(e)}")
        return {
            'status': 'error',
            'message': 'Connection error occurred',
            'details': {
                'error_type': 'Connection Error',
                'error_description': str(e),
                'endpoint_tested': test_url,
                'possible_causes': [
                    'Firewall blocking the connection',
                    'Network connectivity issues',
                    'DNS resolution problems',
                    'Proxy configuration issues'
                ]
            }
        }
    except requests.exceptions.Timeout as e:
        logger.error(f"Timeout Error: {str(e)}")
        return {
            'status': 'error',
            'message': 'Connection timed out',
            'details': {
                'error_type': 'Timeout',
                'error_description': str(e),
                'endpoint_tested': test_url,
                'timeout_value': '10 seconds'
            }
        }
    except Exception as e:
        logger.error(f"Unexpected Error: {str(e)}")
        return {
            'status': 'error',
            'message': 'Unexpected error occurred',
            'details': {
                'error_type': type(e).__name__,
                'error_description': str(e),
                'endpoint_tested': test_url,
                'traceback': traceback.format_exc()
            }
        }
