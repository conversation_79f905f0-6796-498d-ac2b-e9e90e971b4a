# middleware.py
import traceback
from django.utils.deprecation import MiddlewareMixin
from .models import ErrorLog

class ErrorLoggingMiddleware(MiddlewareMixin):
    @classmethod
    def log(cls, error, request=None,request_data={}):
        error_log = ErrorLog.objects.create(
            error_type=error.__class__.__name__,
            error_message=str(error),
            stack_trace=traceback.format_exc(),
            method='MANUAL',
            request_data=request_data
        )
        if request:
            error_log.path=request.path
            error_log.method=request.method
            error_log.user_id=request.user.id if request.user.is_authenticated else None
            error_log.save()
        
    def process_exception(self, request, exception):
        # Safely get request data
        try:
            post_data = dict(request.POST)
            get_data = dict(request.GET)
            # Remove sensitive data
            if 'password' in post_data:
                post_data['password'] = '********'
        except:
            post_data = {}
            get_data = {}

        # Log the error
        ErrorLog.objects.create(
            error_type=exception.__class__.__name__,
            error_message=str(exception),
            stack_trace=traceback.format_exc(),
            path=request.path,
            method=request.method,
            user_id=request.user.id if request.user.is_authenticated else None,
            request_data={
                'GET': get_data,
                'POST': post_data
            }
        )
        return None