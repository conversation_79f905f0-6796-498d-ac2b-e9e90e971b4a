import requests
from .auth_helper import get_token, refresh_access_token


graph_url = 'https://graph.microsoft.com/v1.0'


def get_me(token):
    # Send GET to /me
    user = requests.get('{0}/me'.format(graph_url),
    headers={'Authorization': 'Bearer {0}'.format(token)},
    params={
    '$select':'displayName,givenName,surname,mail,mailboxSettings,userPrincipalName'},
    timeout=30)
    return user.json()


def get_user(email,access_token):
    api_result = requests.get(  # Use access token to call a web api
        'https://graph.microsoft.com/v1.0/users',
        headers={'Authorization': 'Bearer ' + access_token},
        params={'$filter': f"mail eq '{email}'"},
        timeout=30,
        ).json()    
    return api_result


def get_user_metadata(email, request):
    metada_user = {}
    access_token = get_token(request)
    try:
        metada_user = get_user(email, access_token)
    except:  # Replace with the actual exception you expect for an invalid token
        access_token = refresh_access_token(request)
        if access_token:
            metada_user = get_user(email, access_token)
    return metada_user
