from django.contrib import admin
from sass_app.models import *
# Register your models here.
from .models import ToolTip
from django.contrib.auth import get_user_model
from django.urls import path
from django.shortcuts import render
from django.conf import settings

@admin.register(ToolTip)
class ToolTipAdmin(admin.ModelAdmin):
    list_display = ('name', 'content')
    readonly_fields = ('id','slug', 'created_at', 'updated_at')
    search_fields = ('name', 'content')

@admin.register(ErrorLog)
class ErrorLogAdmin(admin.ModelAdmin):
    list_display = ['timestamp', 'error_type', 'truncated_error_message','path', 'method']
    list_filter = ['error_type', 'timestamp']
    search_fields = ['error_message', 'path']
    readonly_fields = ['timestamp', 'error_type', 'error_message', 
                      'stack_trace', 'path', 'method', 'get_username', 
                      'request_data']
    def truncated_error_message(self, obj):
        max_length = 100
        if len(obj.error_message) > max_length:
            return f"{obj.error_message[:max_length]}..."
        return obj.error_message
    truncated_error_message.short_description = 'Error Message'
    
    def get_username(self, obj):
        if obj.user_id:
            User = get_user_model()
            user = User.objects.filter(id=obj.user_id).first()
            return user.username if user else f"{obj.user_id}"
        return None
    get_username.short_description = 'User'

    def has_add_permission(self, request):
        return False


class SystemConstantsAdmin:
    """Custom admin view for system constants without database model"""
    
    def constants_view(self, request):
        """View system constants with comprehensive safety checks"""
        try:
            # Security check
            if not request.user.is_authenticated:
                from django.http import HttpResponseRedirect
                from django.urls import reverse
                return HttpResponseRedirect(reverse('admin:login'))
            
            if not request.user.is_superuser:
                from django.core.exceptions import PermissionDenied
                raise PermissionDenied("Only superusers can view system constants")
            
            all_constants = []
            errors = []
            
            # Get ALL settings from settings.py with security masking
            try:
                # Get all settings attributes
                settings_attrs = [attr for attr in dir(settings) if not attr.startswith('_')]
                
                for setting_name in settings_attrs:
                    try:
                        if hasattr(settings, setting_name):
                            value = getattr(settings, setting_name)
                            
                            if callable(value):
                                display_value = f'<function: {value.__name__}>'
                            elif isinstance(value, (list, tuple, dict)):
                                # For complex objects, show type and length/keys
                                if isinstance(value, dict) and len(value) > 5:
                                    display_value = f'<dict with {len(value)} keys>'
                                elif isinstance(value, (list, tuple)) and len(value) > 10:
                                    display_value = f'<{type(value).__name__} with {len(value)} items>'
                                else:
                                    str_value = str(value)
                                    display_value = str_value[:300] + '...' if len(str_value) > 300 else str_value
                            else:
                                # Regular values - truncate if too long
                                str_value = str(value)
                                display_value = str_value[:200] + '...' if len(str_value) > 200 else str_value
                            
                            all_constants.append({
                                'key': setting_name,
                                'value': display_value,
                                'source': 'settings.py'
                            })
                    except Exception as e:
                        errors.append(f"Error reading setting {setting_name}: {str(e)}")
            except Exception as e:
                errors.append(f"Error accessing settings: {str(e)}")
            
            # Constants from constant.py with error handling
            try:
                import constant
                for attr_name in dir(constant):
                    try:
                        if not attr_name.startswith('_') and attr_name.isupper():
                            value = getattr(constant, attr_name)
                            all_constants.append({
                                'key': attr_name,
                                'value': str(value),
                                'source': 'constant.py'
                            })
                    except Exception as e:
                        errors.append(f"Error reading constant {attr_name}: {str(e)}")
            except ImportError:
                errors.append("Could not import constant.py module")
            except Exception as e:
                errors.append(f"Error accessing constant.py: {str(e)}")
            
            # Sort constants for consistent display
            all_constants.sort(key=lambda x: (x['source'], x['key']))
            
            context = {
                'title': 'System Constants',
                'constants': all_constants,
                'errors': errors,
                'has_permission': True,
            }
            
            # Try to render the template, fallback to simple HTML if template fails
            try:
                return render(request, 'admin/constants_list.html', context)
            except Exception as template_error:
                # Fallback to simple HTML response if template fails
                html = f"""
                <html>
                <head><title>System Constants</title></head>
                <body>
                <h1>System Constants</h1>
                <p><a href="/admin/">← Back to Admin</a></p>
                """
                
                if errors:
                    html += "<h2>Errors:</h2><ul>"
                    for error in errors:
                        html += f"<li>{error}</li>"
                    html += "</ul>"
                
                html += """
                <table border="1" cellpadding="5" cellspacing="0">
                <tr><th>Constant Name</th><th>Value</th><th>Source</th></tr>
                """
                
                for constant in all_constants:
                    html += f"""
                    <tr>
                        <td><strong>{constant.get('key', 'Unknown')}</strong></td>
                        <td>{constant.get('value', 'N/A')}</td>
                        <td>{constant.get('source', 'Unknown')}</td>
                    </tr>
                    """
                
                html += f"""
                </table>
                <p>Total constants: {len(all_constants)}</p>
                <p>Template error: {str(template_error)}</p>
                </body>
                </html>
                """
                
                from django.http import HttpResponse
                return HttpResponse(html)
            
        except Exception as e:
            # Fallback error response
            from django.http import HttpResponse
            return HttpResponse(
                f"<h1>Error</h1><p>Unable to display constants: {str(e)}</p>"
                f"<p><a href='/admin/'>Back to Admin</a></p>",
                status=500
            )
    
    def get_urls(self):
        return [
            path('constants/', self.constants_view, name='system_constants'),
        ]


# Safely add custom admin URLs without overriding the entire admin site
try:
    # Create an instance and try to add custom URLs safely
    constants_admin = SystemConstantsAdmin()
    
    # Add a custom URL pattern to the existing admin site
    original_get_urls = admin.site.get_urls
    
    def custom_get_urls():
        try:
            urls = original_get_urls()
            custom_urls = [
                path('system-constants/', constants_admin.constants_view, name='system_constants'),
            ]
            return custom_urls + urls
        except Exception as e:
            # If there's any error, just return the original URLs
            return original_get_urls()
    
    admin.site.get_urls = custom_get_urls
    
except Exception as e:
    print("Failed to add custom admin URLs: ", e)
    # If anything fails, silently continue without the custom admin view
    pass