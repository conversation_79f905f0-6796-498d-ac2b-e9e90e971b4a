from typing import List, Dict
from business_rules.engine import check_condition
from business_rules import run_all
from business_rules.variables import BaseVariables, numeric_rule_variable, string_rule_variable, boolean_rule_variable, select_rule_variable
from business_rules.actions import BaseActions, rule_action
from django.db.models import Sum
from .models import *
from system_management.models import *
from business_rules.engine import check_condition
from typing import List, Dict
from utility import find_lu_course_group
from collections import defaultdict

SCHEDULE_ASSESSMENT_MODEL={
    "normal":ScheduleAssessment,
    "test":ScheduleAssessmentTest,
}



def calculate_ica_per_course(schedule_assessment:ScheduleAssessment):
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
    icas_lu_code = ScheduleAssessment.objects.filter(lu__isnull=False,
                                            week=schedule_assessment.week,
                                            current_year=SYSTEM_CURRENT_YEAR.current_year,
                                            current_semester=SYSTEM_CURRENT_YEAR.current_semester).exclude(id=schedule_assessment.id).order_by("-created_at").values_list('lu__code', flat=True)

    course_count_dict = {}
    if icas_lu_code:
        courselumaps = CourseLuMap.objects.filter(
            lu__code__in=icas_lu_code, academic_year=SYSTEM_CURRENT_YEAR.current_year, semester=SYSTEM_CURRENT_YEAR.current_semester).order_by('course_code')
        for courselumap in courselumaps:
            # Add to dictionary or increment counter
            if courselumap.course_code.code in course_count_dict:
                course_count_dict[courselumap.course_code.code] += 1
            else:
                course_count_dict[courselumap.course_code.code] = 1

    return course_count_dict

class ScheduleAssessmentVariables(BaseVariables):
    def __init__(self, schedule_assessment: ScheduleAssessment, group_input,test_mode=None):
        self.SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
        self.schedule_assessment = schedule_assessment
        self.schedule_assessments = SCHEDULE_ASSESSMENT_MODEL['test' if test_mode else 'normal'].objects.filter(
            lu=schedule_assessment.lu,
            current_year=schedule_assessment.current_year,
            current_semester=schedule_assessment.current_semester
        ).order_by('week')

        self.group_input = group_input

        if 'weeks' in group_input:
            if group_input['weeks']:
                for schedule_assessment in self.schedule_assessments:
                    schedule_assessment.week = group_input['weeks'][str(schedule_assessment.id)]
                    if str(self.schedule_assessment.id) == str(schedule_assessment.id):
                        self.schedule_assessment.week = group_input['weeks'][str(schedule_assessment.id)]

    @numeric_rule_variable
    def total_weightage(self):
        return self.schedule_assessments.aggregate(Sum('weightage'))['weightage__sum'] or 0

    @numeric_rule_variable
    def minimum_ica_count(self):
        return self.schedule_assessments.count()

    @numeric_rule_variable
    def weightage(self):
        return self.schedule_assessment.weightage

    @string_rule_variable
    def assessment_type(self):
        return self.schedule_assessment.assessment_type or ''

    @numeric_rule_variable
    def duration(self):
        return self.schedule_assessment.duration or 0

    @numeric_rule_variable
    def week(self):
        return self.schedule_assessment.week or 0

    @numeric_rule_variable
    def semester(self):
        return self.schedule_assessment.current_semester


    @string_rule_variable
    def is_examinable(self):
        return self.schedule_assessment.lu.is_examinable

    @numeric_rule_variable
    def project_count(self):
        return self.schedule_assessments.filter(assessment_method__slug='project').count()

    @numeric_rule_variable
    def gap_of_week_to_other_ica(self):

        #GET all week in LU
        list_of_weeks=[]
        for schedule in self.schedule_assessments.order_by('week'):
            if str(schedule.id) in self.group_input['weeks']:
                schedule.week = self.group_input['weeks'][str(schedule.id)]
                if schedule != self.schedule_assessment: #exclude self
                  list_of_weeks.append(schedule.week)

        # Get blocked weeks from AcademicWeek
        blocked_weeks_set = set()
        acadweek = AcademicWeek.objects.filter(
            lutype=self.schedule_assessment.lu.lu_type,
            coursetype=self.schedule_assessment.lu.course_type
        ).first()

        if acadweek and acadweek.weeks and 'status' in acadweek.weeks:
            blocked_weeks_set = {int(week) for week in acadweek.weeks['status'].keys()}

        gap_number = 999
        self.schedule_assessment.week = int(self.schedule_assessment.week)

        for list_of_week in list_of_weeks:
            list_of_week = int(list_of_week)

            # Calculate the range between current week and other ICA week
            start_week = min(self.schedule_assessment.week, list_of_week)
            end_week = max(self.schedule_assessment.week, list_of_week)

            # Count non-blocked weeks in the range (excluding the endpoints)
            non_blocked_weeks_in_range = []
            for week in range(start_week + 1, end_week):
                if week not in blocked_weeks_set:
                    non_blocked_weeks_in_range.append(week)

            # The gap is the number of non-blocked weeks between the two ICAs
            effective_gap = len(non_blocked_weeks_in_range)

            if effective_gap < gap_number:
                gap_number = effective_gap

        return gap_number

    @select_rule_variable(
        options=[
            a for a in [
              'CETSDIP',
              'CETSHSS',
              'PETCM',
              'PETDBFT',
              'PETFGS',
              'CETDBP',
              'PETGSM',
              'PETPFP',
              'CETWSP',
              'CETCM',
              'CETSCTP'
          ]
        ]
    )
    def course_lu_type(self):
        data = f"{self.schedule_assessment.lu.course_type}{
            self.schedule_assessment.lu.lu_type}"
        return {data: data}
    
    @select_rule_variable(
        options=["no","yes"]
    )
    def is_digital_submission(self):
        data='no'
        if 'yes' in self.schedule_assessment.digital_submission:
            data='yes'
        return {data: data}
    

    @select_rule_variable(
        options=["no","yes-in-the-same-week-as-digital-submission","yes-in-the-following-week-after-digital-submission"]
    )
    def digital_submission(self):
        return {self.schedule_assessment.digital_submission: self.schedule_assessment.digital_submission} 


    @select_rule_variable(
        options=[
            a for a in ['presentation','practical','project','assignment','test-on-campus-proctored-eassm-penpaper',
                        'test-non-proctored-eassm','test-on-campus-proctored-eassm-keyboard']
        ]
    )
    def assessment_method(self):
        data = self.schedule_assessment.assessment_method.slug
        return {data: data}

    @numeric_rule_variable
    def term1_weightage(self):
        academicWeek = AcademicWeek.objects.filter(lutype=self.schedule_assessment.lu.lu_type, coursetype=self.schedule_assessment.lu.course_type).first()
        total_term_1 = 0
        for schedule_assessement in self.schedule_assessments:
            date_by_week = get_date_by_week(schedule_assessement.week, schedule_assessement.lu)
            
            # is this term1
            if schedule_assessement.week and academicWeek.term1end:
                if date_by_week <= academicWeek.term1end:
                    total_term_1 += schedule_assessement.weightage

        return total_term_1

    @boolean_rule_variable
    def is_ica_in_term1(self):
        result = False
        academicWeek = AcademicWeek.objects.filter(lutype=self.schedule_assessment.lu.lu_type, coursetype=self.schedule_assessment.lu.course_type).first()
        date_by_week = get_date_by_week(self.schedule_assessment.week, self.schedule_assessment.lu)
        
        # is this term1
        if self.schedule_assessment.week and academicWeek.term1end:
            if date_by_week <= academicWeek.term1end:
                result = True
        return result
    
    @boolean_rule_variable
    def checking_ica_has_term1end(self):
        academicWeek = AcademicWeek.objects.filter(lutype=self.schedule_assessment.lu.lu_type, coursetype=self.schedule_assessment.lu.course_type).first()
        
        term_1=0
        if academicWeek:
            if academicWeek.term1end:
                for schedule_assessement in self.schedule_assessments:
                    date_by_week = get_date_by_week(schedule_assessement.week, schedule_assessement.lu)
                    if date_by_week <= academicWeek.term1end:
                      term_1 += schedule_assessement.weightage
                if term_1 == 0:return False

        return True

    @numeric_rule_variable
    def testfaweek_inclusive(self):
        inclusive_week=0
        ruledefinition = RuleDefinition.objects.filter(slug="test_fa_week").first()
        if ruledefinition.value and self.schedule_assessment.week:
            inclusive_week = int(self.schedule_assessment.week) - int(ruledefinition.value)

        return inclusive_week
    
    @numeric_rule_variable
    def total_ica_in_lu(self):
        return len(self.schedule_assessments)
    

    @numeric_rule_variable
    def number_examinable_lu_in_the_courses(self):

        # year_studies = list(CourseLuMap.objects.filter(academic_year=self.schedule_assessment.current_year,
        #                                     semester=self.schedule_assessment.current_semester,
        #                                     lu=self.schedule_assessment.lu).values_list('year_study', flat=True).distinct().order_by('year_study'))

        # filter_conditions_A = Q(academic_year=self.schedule_assessment.current_year,
        #                         semester=self.schedule_assessment.current_semester,
        #                         lu=self.schedule_assessment.lu)
        # if year_studies:
        #     filter_conditions_A &= Q(year_study__in=year_studies)

        # courses = CourseLuMap.objects.filter(filter_conditions_A)
        # course_lu_list_id=[]
        # for course in courses:
            
        #     filter_conditions_B = Q(academic_year=self.schedule_assessment.current_year,
        #                             semester=self.schedule_assessment.current_semester,
        #                             course_code=course.course_code)
        #     if year_studies:
        #         filter_conditions_B &= Q(year_study__in=year_studies)

        #     course_lu_list_obj = CourseLuMap.objects.filter(filter_conditions_B).distinct('lu')
        #     for course_lu in course_lu_list_obj:
        #         course_lu_list_id.append(str(course_lu.lu.id))
        course_lu_list_id=[]
        prioritize_number_lu=0
        lu_codes_course_groups = find_lu_course_group(self.schedule_assessment.lu.code)        
        for lu_codes_course_group in lu_codes_course_groups:
            course_lu_list_id = lu_codes_course_group['lu_codes']

            if self.schedule_assessment.lu.code in course_lu_list_id:course_lu_list_id.remove(self.schedule_assessment.lu.code)
            course_lu_list_id = list(set(course_lu_list_id))
            number_lu = LearningUnit.objects.filter(code__in=course_lu_list_id,is_examinable="yes").distinct('code').count()

            if number_lu >= prioritize_number_lu:prioritize_number_lu = number_lu

        return prioritize_number_lu
    
    # @boolean_rule_variable
    # def is_exceed_max_ica_count_per_week(self):
        
    #     course_count_dict = calculate_ica_per_course(self.schedule_assessment) #cource count in particular week
    #     lu_course = CourseLuMap.objects.filter(lu__code=self.schedule_assessment.lu.code,
    #                                             academic_year=self.SYSTEM_CURRENT_YEAR.current_year,
    #                                             semester=self.SYSTEM_CURRENT_YEAR.current_semester).distinct('course_code').values_list("course_code__code", flat=True)

    #     list_1 = list(course_count_dict.keys())
    #     list_2 = lu_course
    #     common = list(set(list_1) & set(list_2))

    #     academic_week = AcademicWeek.objects.filter(coursetype=self.schedule_assessment.lu.course_type,lutype=self.schedule_assessment.lu.lu_type).first()
    #     if academic_week and common and course_count_dict:
    #         if not academic_week.maxicacountperweek:return False
            
    #         if common[0] in course_count_dict:
    #             if course_count_dict[common[0]]+1 > academic_week.maxicacountperweek:
    #                 return True
                
    #     return False

    @boolean_rule_variable
    def is_exceed_max_ica_count_per_week(self):
        
        academic_week = AcademicWeek.objects.filter(coursetype=self.schedule_assessment.lu.course_type,lutype=self.schedule_assessment.lu.lu_type).first()
        if academic_week:
            if academic_week.maxicacountperweek:
            
                count_ica=0
                lu_codes_course_groups = find_lu_course_group(self.schedule_assessment.lu.code)
                
                for lu_codes_course_group in lu_codes_course_groups:
                    if self.schedule_assessment.lu.code in lu_codes_course_group['lu_codes']:
                        lu_codes_course_group['lu_codes'].remove(self.schedule_assessment.lu.code)

                    count_year_ica = ScheduleAssessment.objects.filter(lu__code__in=lu_codes_course_group['lu_codes'],
                                                            week=self.schedule_assessment.week,
                                                            current_year=self.SYSTEM_CURRENT_YEAR.current_year,
                                                            current_semester=self.SYSTEM_CURRENT_YEAR.current_semester).order_by("-created_at").count()

                    if count_year_ica>=count_ica:
                        count_ica=count_year_ica
                
                count_ica +=1 #add self

                if count_ica > academic_week.maxicacountperweek:
                    return True

        return False
    
    @numeric_rule_variable
    def is_exceed_max_ica_weight_per_week(self):

        total_weightage=0
        lu_codes_course_groups = find_lu_course_group(self.schedule_assessment.lu.code)
        for lu_codes_course_group in lu_codes_course_groups:
            if self.schedule_assessment.lu.code in lu_codes_course_group['lu_codes']:
                lu_codes_course_group['lu_codes'].remove(self.schedule_assessment.lu.code)

            total_year_weightage = ScheduleAssessment.objects.filter(lu__code__in=lu_codes_course_group['lu_codes'],
                                            week=self.schedule_assessment.week,
                                            current_year=self.SYSTEM_CURRENT_YEAR.current_year,
                                            current_semester=self.SYSTEM_CURRENT_YEAR.current_semester).order_by("-created_at").aggregate(total_weightage=Sum('weightage'))['total_weightage'] or 0
            
            if total_year_weightage>=total_weightage:
                total_weightage=total_year_weightage

        total_weightage +=self.schedule_assessment.weightage

        return total_weightage
    
    @boolean_rule_variable
    def checking_opea_week(self):
      if self.schedule_assessment.week:
        if self.schedule_assessment.assessment_method.slug == 'test-on-campus-proctored-eassm-penpaper' or self.schedule_assessment.assessment_method.slug == 'test-on-campus-proctored-eassm-keyboard':
          ruledefinition = RuleDefinition.objects.filter(slug="test_fa_week").first()
          TESTFAWEEK = ruledefinition.value
          difference_test_fa_week =int(self.schedule_assessment.week) - int(TESTFAWEEK)
          if self.schedule_assessment.lu.is_examinable == 'no':
              if difference_test_fa_week==-1:
                  #checking course
                  number_of_lu = self.number_examinable_lu_in_the_courses()
                  if number_of_lu!=0:
                      return False
                  
              elif difference_test_fa_week==1 or difference_test_fa_week==0:
                  return False
          elif self.schedule_assessment.lu.is_examinable == 'yes':
              #examinable NO
              if difference_test_fa_week>=1 or difference_test_fa_week==-1:
                  return False

      return True
  
    @numeric_rule_variable
    def relative_week_difference_with_blocked_week(self):

        # def custom_distance(x, target_week):
        #     abs_diff = abs(x - target_week)
        #     # In case of tie, prefer higher week number by adding a small penalty to lower numbers
        #     # Multiply by a very small number so it only affects ties
        #     return abs_diff - (0.0001 * x)

        acadweek = AcademicWeek.objects.filter(lutype=self.schedule_assessment.lu.lu_type, coursetype=self.schedule_assessment.lu.course_type).first()
        diff_most_close_week=999
        if acadweek and self.schedule_assessment.week:
            
            blocked_weeks_list = [int(week) for week,_ in acadweek.weeks['status'].items()]
            # closest = min(blocked_weeks_list, key=lambda x: custom_distance(x, self.schedule_assessment.week))
            
            if blocked_weeks_list:
                weeks_in_front = [week for week in blocked_weeks_list if week > self.schedule_assessment.week]

                closest = min(weeks_in_front)
                
                diff_most_close_week = self.schedule_assessment.week-closest

        return diff_most_close_week
    
    @numeric_rule_variable
    def previous_ica_gap_with_blocked_week(self):
        """
        Validates if there's a gap week between the previous ICA's occupied weeks
        and blocked weeks, before scheduling the current assessment.

        Returns:
            999: No blocked weeks affecting scheduling (allowed)
            >= 0: Gap exists between previous ICA and blocked weeks (allowed)
            < 0: No gap between previous ICA and blocked weeks (not allowed)
        """
        print("\n=== relative_week_difference_with_blocked_week START ===")

        # If no week is set, no validation needed
        print(f"Checking if week is set: {self.schedule_assessment.week}")
        if not self.schedule_assessment.week:
            print("No week set, returning 999")
            return 999

        print(f"self.schedule_assessment.week: {self.schedule_assessment.week}")

        print(f"Filtering AcademicWeek with lutype={self.schedule_assessment.lu.lu_type}, coursetype={self.schedule_assessment.lu.course_type}")
        acadweek = AcademicWeek.objects.filter(
            lutype=self.schedule_assessment.lu.lu_type,
            coursetype=self.schedule_assessment.lu.course_type
        ).first()

        # No academic week configuration, no restriction
        print(f"AcademicWeek found: {acadweek}")
        if not acadweek:
            print("No academic week configuration, returning 999")
            return 999

        # Get all blocked weeks
        blocked_weeks_list = [int(week) for week, _ in acadweek.weeks['status'].items()]
        print(f"Blocked weeks list: {blocked_weeks_list}")

        # No blocked weeks, no restriction
        if not blocked_weeks_list:
            print("No blocked weeks, returning 999")
            return 999

        # Find the previous ICA (scheduled before current week)
        print(f"Finding previous ICAs before week {self.schedule_assessment.week}")
        previous_icas = []
        for schedule in self.schedule_assessments.order_by('week'):
            print(f"  Checking schedule ID {schedule.id}")
            if str(schedule.id) in self.group_input.get('weeks', {}):
                week = self.group_input['weeks'][str(schedule.id)]
                print(f"    Schedule {schedule.id} has week {week} in group_input")
                # Exclude self and only get ICAs before current week
                if schedule.id != self.schedule_assessment.id and int(week) < int(self.schedule_assessment.week):
                    # Get over_two_weeks from group_input if available, otherwise from database
                    over_two_weeks = self.group_input.get('over_two_weeks', {}).get(str(schedule.id), schedule.over_two_weeks)
                    print(f"    Adding to previous_icas: week={week}, over_two_weeks={over_two_weeks}")
                    previous_icas.append({
                        'week': int(week),
                        'over_two_weeks': over_two_weeks,
                        'schedule': schedule
                    })
                else:
                    print(f"    Skipped (self={schedule.id == self.schedule_assessment.id}, week check={int(week) < int(self.schedule_assessment.week)})")

        print(f"Previous ICAs found: {len(previous_icas)}")
        # No previous ICA, no restriction based on gap
        if not previous_icas:
            print("No previous ICA, returning 999")
            return 999

        # Get the ICA closest to current week (the immediate previous one)
        prev_ica = max(previous_icas, key=lambda x: x['week'])
        prev_week = prev_ica['week']
        print(f"Closest previous ICA: week={prev_week}, over_two_weeks={prev_ica['over_two_weeks']}")

        # Calculate the last occupied week by previous ICA
        if prev_ica['over_two_weeks'] == 'yes':
            prev_last_occupied_week = prev_week + 1  # Occupies 2 weeks
            print(f"Previous ICA occupies 2 weeks, last occupied week: {prev_last_occupied_week}")
        else:
            prev_last_occupied_week = prev_week  # Occupies 1 week
            print(f"Previous ICA occupies 1 week, last occupied week: {prev_last_occupied_week}")

        # Find the closest blocked week that comes AFTER the previous ICA
        print(f"Finding blocked weeks that come after previous ICA's last occupied week ({prev_last_occupied_week})")
        blocked_weeks_after_prev_ica = [
            week for week in blocked_weeks_list
            if week > prev_last_occupied_week
        ]
        print(f"Blocked weeks after previous ICA: {blocked_weeks_after_prev_ica}")

        # No blocked weeks after previous ICA, no restriction
        if not blocked_weeks_after_prev_ica:
            print("No blocked weeks after previous ICA, returning 999")
            return 999

        # Get the closest (first) blocked week after previous ICA
        closest_blocked_week = min(blocked_weeks_after_prev_ica)
        print(f"Closest blocked week after previous ICA: {closest_blocked_week}")

        # Check if previous ICA has at least one gap week before the blocked week
        gap_size = closest_blocked_week - prev_last_occupied_week - 1
        gap_exists = gap_size > 0
        print(f"Gap check: closest_blocked_week({closest_blocked_week}) - prev_last_occupied_week({prev_last_occupied_week}) - 1 = {gap_size}")
        print(f"Previous ICA has gap with blocked week: {gap_exists}")

        # Return absolute value of gap size
        absolute_gap = abs(gap_size)
        print(f"Returning absolute gap size: {absolute_gap}")
        return absolute_gap
    
    @string_rule_variable
    def assessment_task(self):
        return self.schedule_assessment.assessment_task or ''
   
    @select_rule_variable(
        options=["no","yes"]
    )
    def over_two_weeks(self):
        data='no'
        if 'yes' in self.schedule_assessment.over_two_weeks:
            data='yes'
        return {data: data}
    
    @select_rule_variable(
        options=[
            a for a in [
              'PET',
              'CET',
          ]
        ]
    )
    def course_type(self):
        data = f"{self.schedule_assessment.lu.course_type}"
        return {data: data}
 

class ValidationResult:
    def __init__(self):
        self.is_valid = False
        self.reasons = []

    def add_reason(self, rule_name, message):
        self.reasons.append({
            'rule': rule_name,
            'message': message
        })

    @property
    def message(self):
        if self.is_valid:
            return "Validation passed successfully"
        return "\n".join([
            "Validation failed:",
            *[f"- Rule {r['rule']}: {r['message']}" for r in self.reasons]
        ])


def cast_condition_value(condition):
    if isinstance(condition, dict):
        if 'value' in condition:
            try:
                # Handle negative numbers
                if str(condition['value']).startswith('-'):
                    condition['value'] = int(condition['value'])
                # Handle regular numbers
                elif str(condition['value']).replace('.', '').isdigit():
                    if '.' in str(condition['value']):
                        condition['value'] = float(condition['value'])
                    else:
                        condition['value'] = int(condition['value'])
            except (ValueError, AttributeError):
                # Keep original value if can't convert
                pass
        return condition
    return condition

class ScheduleAssessmentActions(BaseActions):
    def __init__(self, validation_result):
        self.validation_result = validation_result

    @rule_action(params={})
    def mark_valid(self, message, results=None):
        """Action to mark the validation as successful"""
        self.validation_result.is_valid = True
        self.validation_result.add_reason(
            "validation_success",
            "All conditions met"
        )


def run_all_with_tracking(rule_list: List[Dict],
                          defined_variables,
                          defined_actions,
                          stop_on_first_trigger=False):
    """
    Modified run_all that tracks and returns which rules were broken
    """
    broken_rules = []
    triggered_rules = []

    for rule in rule_list:
        conditions = rule.get('conditions', {})
        # Check conditions and track results
        conditions_met = _check_conditions_recursively(conditions, defined_variables)
        if conditions_met:
            # Execute actions if conditions are met
            for action in rule.get('actions', []):
                triggered_rules.append(action.get('name'))

            if stop_on_first_trigger:
                break
        else:
            # Track failed conditions
            broken_rules.append({
                'failed_conditions': _get_failed_conditions(conditions, defined_variables)
            })

    return {
        'triggered_rules': triggered_rules,
        'broken_rules': broken_rules
    }


def _check_conditions_recursively(conditions: Dict, defined_variables) -> bool:
    """
    Recursively check conditions including nested any/all conditions
    """
    
    if 'all' in conditions:
        return all(_check_conditions_recursively(cast_condition_value(c), defined_variables)
                   if isinstance(c, dict) and ('any' in c or 'all' in c)
                   else check_condition(cast_condition_value(c), defined_variables)
                   for c in conditions['all'])

    elif 'any' in conditions:
        return any(_check_conditions_recursively(cast_condition_value(c), defined_variables)
                   if isinstance(c, dict) and ('any' in c or 'all' in c)
                   else check_condition(cast_condition_value(c), defined_variables)
                   for c in conditions['any'])

    else:
        return check_condition(conditions, defined_variables)

# Super Important, Showing All Log 
# def _get_failed_conditions(conditions: Dict, defined_variables) -> List[Dict]:
#     """
#     Get details about which conditions failed
#     """

#     failed = []

#     if 'all' in conditions:
#         for condition in conditions['all']:
#             if isinstance(condition, dict) and ('any' in condition or 'all' in condition):
#                 failed.extend(_get_failed_conditions(
#                     condition, defined_variables))
#             elif not check_condition(cast_condition_value(condition), defined_variables):
#                 failed.append(_get_condition_details(
#                     condition, defined_variables, conditions['all']))

#     elif 'any' in conditions:
#         all_failed = True
#         for condition in conditions['any']:
#             if isinstance(condition, dict) and ('any' in condition or 'all' in condition):
#                 sub_failed = _get_failed_conditions(
#                     condition, defined_variables)
#                 if not sub_failed:
#                     all_failed = False
#                 else:
#                     failed.extend(sub_failed)
#             elif not check_condition(cast_condition_value(condition), defined_variables):
#                 failed.append(_get_condition_details(
#                     condition, defined_variables,conditions['any']))
#             else:
#                 all_failed = False
#         if not all_failed:
#             failed = []

#     return failed


# def _get_failed_conditions(conditions: Dict, defined_variables) -> List[Dict]:
#     """
#     Get details about which conditions failed, with context-aware filtering
#     """
#     CONTEXT_FIELDS = {
#         'course_lu_type': lambda vars: list(getattr(vars, 'course_lu_type')().keys())[0] if isinstance(getattr(vars, 'course_lu_type')(), dict) else "",
#         'assessment_method': lambda vars: list(getattr(vars, 'assessment_method')().keys())[0] if isinstance(getattr(vars, 'assessment_method')(), dict) else "",
#         'digital_submission': lambda vars: list(getattr(vars, 'digital_submission')().keys())[0] if isinstance(getattr(vars, 'digital_submission')(), dict) else "",
#         'is_examinable': lambda vars: getattr(vars, 'is_examinable')(),
#         'testfaweek_inclusive': lambda vars: getattr(vars, 'testfaweek_inclusive')()
#     }

#     def get_unmatched_context_message(conditions_any, defined_vars):
#         """Generate message for values not found in rules"""
#         allowed_values = {}
#         # Collect all allowed values from rules
#         for condition in conditions_any:
#             if isinstance(condition, dict):
#                 if 'all' in condition:
#                     for subcond in condition['all']:
#                         if subcond.get('name') in CONTEXT_FIELDS:
#                             allowed_values.setdefault(subcond.get('name'), set()).add(str(subcond.get('value')).lower())
#                 elif condition.get('name') in CONTEXT_FIELDS:
#                     allowed_values.setdefault(condition.get('name'), set()).add(str(condition.get('value')).lower())

#         # Check current values against allowed values
#         unmatched = []
#         # for field_name, getter in CONTEXT_FIELDS.items():
#         #     current_value = getter(defined_vars)
           
#         #     if (current_value!=None or current_value!="") and (field_name in allowed_values):
#         #         if str(current_value).lower() not in allowed_values[field_name]:
#         #             unmatched.append(f"{field_name} '{current_value}' is not in the rules")
                
#         return [{'name': 'context_error', 'message': msg} for msg in unmatched] if unmatched else []

#     def is_relevant_group(group_conditions, defined_vars):
#         """Check if this group is relevant based on contextual fields"""
#         if not isinstance(group_conditions, list):
#             return True
            
#         for condition in group_conditions:
#             if isinstance(condition, dict):
#                 if condition.get('name') in CONTEXT_FIELDS:
#                     current_value = CONTEXT_FIELDS[condition.get('name')](defined_vars)
#                     expected_value = str(condition.get('value')).lower()
#                     current_value = str(current_value).lower()
                    
#                     if condition['operator'] == 'contains' and expected_value == current_value:
#                         return True
#         return False

#     failed = []

#     if 'all' in conditions:
#         for condition in conditions['all']:
#             if isinstance(condition, dict):
#                 if 'any' in condition or 'all' in condition:
#                     sub_failed = _get_failed_conditions(condition, defined_variables)
#                     failed.extend(sub_failed)
#                 else:
#                     if not check_condition(cast_condition_value(condition), defined_variables):
#                         failed.append(_get_condition_details(condition, defined_variables, conditions['all']))

#     elif 'any' in conditions:
#         any_passed = False
#         relevant_failures = []
        
#         # First: find relevant groups
#         relevant_groups = []
#         for condition in conditions['any']:
#             if isinstance(condition, dict):
#                 if 'all' in condition:
#                     if is_relevant_group(condition['all'], defined_variables):
#                         relevant_groups.append(condition)
#                 elif condition.get('name') in CONTEXT_FIELDS:
#                     current_value = CONTEXT_FIELDS[condition.get('name')](defined_variables)
#                     if str(condition.get('value')).lower() == str(current_value).lower():
#                         relevant_groups.append(condition)

#         # Then: check conditions only in relevant groups
#         if relevant_groups:
#             for group in relevant_groups:
#                 if 'all' in group:
#                     if not _check_conditions_recursively(group, defined_variables):
#                         sub_failures = _get_failed_conditions(group, defined_variables)
#                         relevant_failures.extend(sub_failures)
#                 else:
#                     if not check_condition(cast_condition_value(group), defined_variables):
#                         failure = _get_condition_details(group, defined_variables, conditions['any'])
#                         relevant_failures.append(failure)
#         else:
#             # Return specific messages about which values aren't in rules
#             return get_unmatched_context_message(conditions['any'], defined_variables)

#         failed.extend(relevant_failures)

#     return failed

# def _get_condition_details(condition: Dict, defined_variables, group_conditions=[]) -> Dict:
#     """
#     Get detailed information about a failed condition
#     """
#     variable_name = condition.get('name')
#     actual_value = getattr(defined_variables, variable_name)()

#     result = {
#         'name': variable_name,
#         'operator': condition.get('operator'),
#         'expected': condition.get('value'),
#         'actual': actual_value
#     }

#     # Add the rule group information if available
#     if group_conditions and isinstance(group_conditions, list):
#         # Get all conditions in the group except the failed one
#         group_info = [
#             {
#                 'name': cond.get('name'),
#                 'operator': cond.get('operator'),
#                 'value': cond.get('value')
#             }
#             for cond in group_conditions
#             if cond != condition
#         ]
#         result['group_conditions'] = group_info
#     return result






VALIDATION_PRIORITY = [
    'gap_of_week_to_other_ica',
    'duration',
    'project_count',
    'testfaweek_inclusive',
    'checking_opea_week',
    'number_examinable_lu_in_the_courses',
    'is_exceed_max_ica_count_per_week',
    'is_exceed_max_ica_weight_per_week',
    'relative_week_difference_with_blocked_week',
    'previous_ica_gap_with_blocked_week'
    # Add other variable names in priority order
]

def _remove_duplicates_from_list_of_dicts(lst):
    unique_dicts = []
    seen = set()
    
    for item in lst:
        # Create a hashable representation of the dictionary
        hashable_item = []
        for key, value in sorted(item.items()):
            # Handle the case where value is a dictionary
            if isinstance(value, dict):
                # Convert nested dictionary to a tuple of tuples
                nested_dict = tuple(sorted((k, str(v)) for k, v in value.items()))
                hashable_item.append((key, nested_dict))
            else:
                hashable_item.append((key, str(value)))
        
        # Convert to tuple for hashability
        item_tuple = tuple(hashable_item)
        
        if item_tuple not in seen:
            seen.add(item_tuple)
            unique_dicts.append(item)
            
    return unique_dicts

def _sort_failures(failed_conditions: List[Dict]) -> List[Dict]:
    """
    Sort failures based on predefined priority order
    """
    # Helper function to get priority index (lower index = higher priority)
    def get_priority(failure):
        try:
            return VALIDATION_PRIORITY.index(failure['name'])
        except ValueError:
            # If not in priority list, put at the end
            return len(VALIDATION_PRIORITY)
    
    return sorted(failed_conditions, key=get_priority)

# List of variables that shouldn't be included in error reporting
SKIP_ERROR_REPORTING = {
    'assessment_method',
    'is_examinable',
    'is_digital_submission',
    'digital_submission',
    'course_lu_type',
    'assessment_task',
    'course_type',
    'course_lu_type',
    'over_two_weeks'
}

def _get_condition_details(condition: Dict, defined_variables) -> Dict:
    """
    Get detailed information about a failed condition
    Skip certain user input variables
    """
    variable_name = condition.get('name')
    
    # Skip error reporting for user input variables
    if variable_name.lower() in SKIP_ERROR_REPORTING:
        return None
        
    actual_value = getattr(defined_variables, variable_name)()
    return {
        'name': variable_name,
        'operator': condition.get('operator'),
        'expected': condition.get('value'),
        'actual': actual_value
    }


def _check_conditions_recursively_with_log(conditions: Dict, defined_variables, failed_conditions: List) -> bool:
    """
    Check conditions recursively, excluding user input variables from error reporting
    """
    if 'all' in conditions:
        for c in conditions['all']:
            result = (_check_conditions_recursively_with_log(cast_condition_value(c), defined_variables, failed_conditions)
                     if isinstance(c, dict) and ('any' in c or 'all' in c)
                     else check_condition(cast_condition_value(c), defined_variables))
            if not result:
                # Add to failed conditions only if it's not a user input variable
                if isinstance(c, dict) and not ('any' in c or 'all' in c):
                    details = _get_condition_details(c, defined_variables)
                    if details:  # Only append if not None (not in skip list)
                        failed_conditions.append(details)
                return False
        return True

    elif 'any' in conditions:
        any_failed = []
        for c in conditions['any']:
            result = (_check_conditions_recursively_with_log(cast_condition_value(c), defined_variables, failed_conditions)
                     if isinstance(c, dict) and ('any' in c or 'all' in c)
                     else check_condition(cast_condition_value(c), defined_variables))
            if result:
                return True
            if isinstance(c, dict) and not ('any' in c or 'all' in c):
                details = _get_condition_details(c, defined_variables)
                if details:  # Only append if not None (not in skip list)
                    any_failed.append(details)
        # Only add failures that aren't in the skip list
        failed_conditions.extend([f for f in any_failed if f])
        return False

    else:
        result = check_condition(conditions, defined_variables)
        if not result:
            details = _get_condition_details(conditions, defined_variables)
            if details:  # Only append if not None (not in skip list)
                failed_conditions.append(details)
        return result

def _get_failed_conditions(conditions: Dict, defined_variables) -> tuple[bool, List]:
    """
    Wrapper function that returns both result and failures
    """
    failed_conditions = []
    _check_conditions_recursively_with_log(conditions, defined_variables, failed_conditions)
    failed_conditions = [f for f in failed_conditions if f]
    failed_conditions = _sort_failures(failed_conditions)
    failed_conditions = _remove_duplicates_from_list_of_dicts(failed_conditions)
    return failed_conditions





def validate_with_tracker(schedule_assessment: ScheduleAssessment, rules, group_input={},test_mode=None):

    validation_result = ValidationResult()
    actions = ScheduleAssessmentActions(validation_result)

    # Example usage with your code:
    results = run_all_with_tracking(
        rule_list=rules,
        defined_variables=ScheduleAssessmentVariables(
            schedule_assessment, group_input,test_mode),
        defined_actions=actions,
        stop_on_first_trigger=True
    )

    return results


