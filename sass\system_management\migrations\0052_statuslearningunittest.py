# Generated by Django 5.1 on 2024-12-31 16:07

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("system_management", "0051_rename_lucategory_academicweek_lutype"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="StatusLearningUnitTest",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("current_year", models.IntegerField(default=2024)),
                ("current_semester", models.IntegerField(default=1)),
                ("lu_metadata", models.JSONField(blank=True, null=True)),
                (
                    "original_lu_code",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "weightage",
                    models.IntegerField(
                        default=0,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("saved", "Saved"),
                            ("approved", "Approved"),
                            ("submitted", "Submitted"),
                            ("rework", "Rework"),
                        ],
                        default="pending",
                        max_length=10,
                    ),
                ),
                ("ica_detail_submit", models.BooleanField(default=False)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "lu",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="system_management.learningunit",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("current_year", "current_semester", "lu")},
            },
        ),
    ]
