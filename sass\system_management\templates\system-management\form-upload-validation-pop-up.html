{% load static %}

<h2>Warning: File Overwrite</h2>
<p>You are about to overwrite an existing file. This action is irreversible.</p>

{% for validation in validations_data %}
<p class="mb-0">File Details of {{validation.file_type}}:</p>
<ul class="d-flex flex-column align-items-center justify-content-center p-0">
    <li>Number of columns detected: <span class="text-danger">{{validation.num_col}}</span></li>
    <li>Expected number of columns: <span class="text-danger">{{validation.expected_num_col}}</span></li>
</ul>
{% endfor %}
<p>Before proceeding, please double-check that you have selected the correct document. If the number of columns does not match, the file may not be accurate, which could affect further processes.</p>
<p>Are you sure you want to overwrite the existing file with this one?</p>




