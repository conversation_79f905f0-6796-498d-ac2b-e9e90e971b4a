# Generated by Django 5.1 on 2024-11-06 10:46

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("schedule_assessment", "0005_alter_scheduleassessment_assessment_type"),
        ("system_management", "0041_statuslearningunit_lu_metadata_and_more"),
    ]
    def delete_schedule_data(apps, schema_editor):
        ScheduleAssessment = apps.get_model('schedule_assessment', 'ScheduleAssessment')
        ScheduleAssessment.objects.filter().delete()
        

    operations = [
        migrations.RunPython(delete_schedule_data),
        migrations.AlterField(
            model_name="scheduleassessment",
            name="assessment_method",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="system_management.assessment",
            ),
        ),
    ]
