from django.urls import include, path
from . import views

app_name = 'schedule_assessment'
urlpatterns = [
    # schedule assessment

    path('add-ica/<uuid:id>', views.add_ica, name="addIca"),
    path('ica-detail-partial/<uuid:id>', views.partial_ica_detail,
         name="partial_ica_detail"),
    path('ica-schedule-partial/<uuid:luID>', views.partial_ica_schedule,
         name="partial_ica_schedule", kwargs={'is_delete': None}),
    path('copy-from-last/<uuid:id>', views.copy_from_last, name="copy_from_last"),
    path('ica-schedule/<uuid:id>', views.ica_schedule, name="icaSchedule"),
    #     path('ica-schedule/ass-task', views.get_assm_task, name="get_assm_task"),

    path('reserved-assessement/', views.reserved_assessement,
         name="reserved_assessement"),
    path('preview-ica-schedule', views.preview_ica_schedule,
         name="preview_ica_schedule"),
    path('delete-ica/<uuid:id>', views.delete_ica, name="delete_ica"),

    path('update-ica/', views.update_ica_schedule, name="update_ica_schedule"),
    path('update-ica-detail/', views.update_ica_detail, name="update_ica_detail"),
]
