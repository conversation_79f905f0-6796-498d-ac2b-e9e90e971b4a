{% load static %}
<div class="row">
    <div class="col-12">
        <div class="breadcrumb">
            <a href="/">Back</a> <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M7.07586 4.51043C7.18836 4.39807 7.34086 4.33496 7.49986 4.33496C7.65886 4.33496 7.81136 4.39807 7.92386 4.51043L11.3239 7.91043C11.4362 8.02293 11.4993 8.17543 11.4993 8.33443C11.4993 8.49343 11.4362 8.64593 11.3239 8.75843L7.92386 12.1584C7.81012 12.2644 7.65969 12.3221 7.50424 12.3194C7.3488 12.3166 7.2005 12.2537 7.09057 12.1437C6.98064 12.0338 6.91766 11.8855 6.91492 11.7301C6.91218 11.5746 6.96988 11.4242 7.07586 11.3104L10.0519 8.33443L7.07586 5.35843C6.9635 5.24593 6.90039 5.09343 6.90039 4.93443C6.90039 4.77543 6.9635 4.62293 7.07586 4.51043Z" fill="#BBBBBB"/>
                </svg>
            <span>Set Current Year & Edit Mode</span>
        </div>  
        <div class="content-system-management">
            <div class="card card-set-current card-shadow card-padding">
                <h2 class="title-section">System Management</h2>
                <c-menu_system_management current_page="setCurrent"></c-menu_system_management>
                <h2 class="title-card text-black mb-4">Set Current Year & Edit Mode</h2>
                
                <div class="d-flex flex-column flex-md-row align-items-md-center gap-4 justify-content-start menu-user-management">   
                    <button type="button" class="btn-text v-center p-0 disabled"><img class="icon-image" src="{% static 'img/icon-pencil.svg' %}" alt="bulk"> Edit</button>
                </div>
                
                <div class="mt-4">
                    <div class="row">
                        <div class="col-lg-6">
                            <p class="fw-bold text-primary mb-2">Set Current Year</p>
                            
                            <div class="ps-3">
                                <div class="form-group">
                                    <label for="current-year-selected ">Current year</label>
                                    <div class="wrapper-calendar">
                                        <select name="current-year" class="form-control current-year" title="current-year-selected">
                                            
                                            
                                        </select>

                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="current-semester">Current semester</label>
                                    <div class="wrapper-select">
                                        <select name="current-semester" id="current-semester" class="form-control">
                                            <option value="s1">S1</option>
                                            <option value="s2">S3</option>
                                            <option value="s3">S4</option>
                                            <option value="s4">S5</option>
                                            <option value="s5">S6</option>
                                            <option value="s6">S7</option>
                                            <option value="s7">S8</option>
                                        </select>
                                    </div>
                                </div>
                            </div>             
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <p class="fw-bold text-primary mb-2 mt-4">Set Edit Mode</p>
                            <div class="table-responsive">
                                <table class="table-sass table table-borderless table-set-edit-mode">
                                    <thead class="bg-gray text-black">
                                        <tr>                                
                                            <th>LU PHASE</th>
                                            <th>EDIT MODE <p class="small fst-italic">Apply between start & end date</p> </th>
                                            <th>START DATE <p class="small fst-italic">Leave blank to start immediately</p> </th>
                                            <th>END DATE <p class="small fst-italic">Leave blank to keep it running</p> </th>
                                            <th>EDIT MODE AFTER <p class="small fst-italic">Available when an end date is set</p> </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>                                
                                            <td>Group 1</td>
                                            <td>On</td>
                                            <td>YYYY-MM-DD  00:00:00</td>
                                            <td>YYYY-MM-DD  00:00:00</td>
                                            <td>YYYY-MM-DD  00:00:00</td>                                
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
            <div class="d-flex mt-4 gap-2 align-items-center justify-content-start wrapper-save-change ">
                <button type="button" class="btn btn-primary btn-save-change disabled" hx-get="{% url 'system_management:setCurrent' %}" hx-target=".container-content" hx-swap="innerHTML" htmx.addClass(htmx.find('.wrapper-save-change'), 'd-none');
                hx-trigger="click">Save Changes</button>
                <button type="button" class="btn-text text-danger" hx-get="{% url 'system_management:setCurrent' %}" hx-target=".container-content" hx-swap="innerHTML">Cancel</button>
            </div>
        </div>      
       
    </div>
</div>

<script>
    $('.current-year').prepend(`
        
        <option value="${parseInt(localStorage.getItem('currentYear')) - 1}">${parseInt(localStorage.getItem('currentYear')) - 1}</option>
        <option selected value="${parseInt(localStorage.getItem('currentYear'))}">${parseInt(localStorage.getItem('currentYear'))}</option>
        <option value="${parseInt(localStorage.getItem('currentYear')) + 1}">${parseInt(localStorage.getItem('currentYear')) + 1}</option>
        
    `)
</script>




