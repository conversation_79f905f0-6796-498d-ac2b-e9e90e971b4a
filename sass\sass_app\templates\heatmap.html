{% load static %}
{% load mathfilters %}
{% block content %}

<div class="headmaps d-none d-md-flex">
    <div class="headmap-head"> 
        <h3>LU CODE / WEEK</h3>         
    </div>
    <div class="headmap-content headmap-content-static">
        <div class="row__headmap-content" >
            {% with learning_unit_week=learning_unit_weeks|first %}
                {% for _ in learning_unit_week.calendar %}
                <span>{{forloop.counter}}</span>
                {% endfor %}
            {% endwith %}
        </div>
               
    </div>
</div>
<div class=" new-headmap">
    <div class="headmaps">
        <div class="headmap-head"> 
            <h3 class="d-md-none">LU CODE / WEEK</h3> 
            <ul>
                {% for learning_unit_week in learning_unit_weeks %}
                <li>{{learning_unit_week.code}}</li>
                {% endfor %}
            </ul>
        </div>
        <div class="headmap-content headmap-content-dynamic">
            <div class="row__headmap-content d-md-none" >
                {% with learning_unit_week=learning_unit_weeks|first %}
                    {% for _ in learning_unit_week.calendar %}
                    <span>{{forloop.counter}}</span>
                    {% endfor %}
                {% endwith %}
            </div>
            {% for learning_unit_week in learning_unit_weeks %}
                <div class="row__headmap-content">
                    {% for learning_unit_week_calender in learning_unit_week.calendar %}
                        {% if learning_unit_week_calender.status == 'limited' %}
                        <span class="bg-yellow"></span>
                        {% elif learning_unit_week_calender.status == 'unavailable' %}
                        <span class="bg-red"></span>
                        {% elif learning_unit_week_calender.status == 'available' %}
                        <span class="bg-green"></span>
                        {% else %}
                        <span class="bg-black"></span>
                        {% endif %}
                    {% endfor %}
                </div>
            {% endfor %}        
        </div>
    </div>
</div>


{% endblock %}

    

