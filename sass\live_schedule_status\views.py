from django.shortcuts import render, redirect
from django.http import HttpResponse
from system_management.models import Department, LearningUnit, CourseLuMap, Course, SystemCurrentYear, StatusLearningUnit
from schedule_assessment.models import ScheduleAssessment
from django.db.models import Q
from django.db.models import Prefetch
from django.urls import reverse
from utility import check_access,check_permission
from django.utils import timezone
# Create your views here.


@check_access(page="live_schedule_status")
def live_schedule_status(request):
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()

    department_code = request.GET.get("department", None)
    departments = Department.objects.order_by('code')

    filter_conditions = Q(
        courselumap__academic_year=SYSTEM_CURRENT_YEAR.current_year,
        courselumap__semester=SYSTEM_CURRENT_YEAR.current_semester
    )
    if department_code:
        filter_conditions &= Q(department__code=department_code)

    learning_units = LearningUnit.objects.filter(filter_conditions).distinct('code').order_by('code')
    context = {
        'departments': departments,
        'learning_units': learning_units,
        'success_message': request.GET.get('success_message', None),
        'department_code': department_code,
        "permission": check_permission(request.user, 'live_schedule_status')
    }
    return render(request, 'live-schedule-status/index.html', context)


def get_course(request):
    learning_unit = request.GET.get("learning_unit", None)
    details_type = request.GET.get('details-type')
    SYSTEM_CURRENT_YEAR = request.GET.get("SYSTEM_CURRENT_YEAR", None)
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.filter(
        id=SYSTEM_CURRENT_YEAR).first()
    learning_unit = LearningUnit.objects.filter(id=learning_unit).first()

    if details_type == 'course':
        courses = []
        if SYSTEM_CURRENT_YEAR and learning_unit:
            course_code = CourseLuMap.objects.filter(lu=learning_unit, academic_year=SYSTEM_CURRENT_YEAR.current_year,
                                                     semester=SYSTEM_CURRENT_YEAR.current_semester).values_list('course_code', flat=True)
            courses = Course.objects.filter(id__in=course_code).prefetch_related(
                Prefetch(
                    'courselumap_set',
                    queryset=CourseLuMap.objects.filter(
                        academic_year=SYSTEM_CURRENT_YEAR.current_year,
                        semester=SYSTEM_CURRENT_YEAR.current_semester,
                        lu=learning_unit
                    ),
                    to_attr='filtered_maps'  # This will store the filtered results in a custom attribute
                )
            ).order_by('code')
        return render(request, 'live-schedule-status/inner-course.html', {
            "courses": courses
        })

    elif details_type == 'details':

        schedule_assessment_details = ScheduleAssessment.objects.filter(
            lu=learning_unit, current_year=SYSTEM_CURRENT_YEAR.current_year, current_semester=SYSTEM_CURRENT_YEAR.current_semester, week__isnull=False).order_by('week')

        return render(request, 'live-schedule-status/inner-details.html', {
            "schedule_assessment_details": schedule_assessment_details,
            "learning_unit": learning_unit
        })

    return HttpResponse(200)


def status(request):
    success_message = ""
    if request.method == "POST":
        SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
        learning_unit_id = request.POST.get("learning_unit", None)
        status = request.POST.get('status', None)
        department_code = request.POST.get("department", None)
        if department_code == 'None':
            department_code = None
        url_parameter = ''
        if status and learning_unit_id:

            status_learning_unit = StatusLearningUnit.objects.filter(
                lu_id=learning_unit_id, current_year=SYSTEM_CURRENT_YEAR.current_year, current_semester=SYSTEM_CURRENT_YEAR.current_semester).first()
            if not status_learning_unit:
                status_learning_unit, _ = StatusLearningUnit.objects.get_or_create(
                    lu_id=learning_unit_id, current_year=SYSTEM_CURRENT_YEAR.current_year, current_semester=SYSTEM_CURRENT_YEAR.current_semester)

            learning_unit_dict = LearningUnit.objects.filter(
                id=learning_unit_id,
                courselumap__academic_year=SYSTEM_CURRENT_YEAR.current_year,
                courselumap__semester=SYSTEM_CURRENT_YEAR.current_semester
                
                ).values().first()
            learning_unit_dict.pop('id')  # Remove ID

            # Process Departmeent
            department = Department.objects.get(
                id=learning_unit_dict['department_id'])
            if department:
                learning_unit_dict['department_name'] = department.name

            learning_unit_dict.pop('department_id')
            learning_unit_dict['updated_at'] = learning_unit_dict['updated_at'].isoformat(
            )
            learning_unit_dict['created_at'] = learning_unit_dict['created_at'].isoformat(
            )

            status_learning_unit.lu_metadata = learning_unit_dict
            status_learning_unit.status = status
            status_learning_unit.updated_by = request.user
            status_learning_unit.updated_at = timezone.now()
            status_learning_unit.save()
            success_message = "true"

        if department_code:
            url_parameter = "&department=" + department_code
        if success_message:
            url_parameter = "?success_message=" + success_message
        return redirect(reverse('live-schedule:liveScheduleStatus')+url_parameter)

    return HttpResponse(200)
