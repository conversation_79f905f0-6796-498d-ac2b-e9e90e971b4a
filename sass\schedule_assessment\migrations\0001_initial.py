# Generated by Django 5.1 on 2024-10-23 08:18

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        (
            "system_management",
            "0024_academicweek_assessment_course_moderationmethod_and_more",
        ),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="RuleDefinition",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, unique=True)),
                ("description", models.TextField()),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("Variable", "Variable"),
                            ("Rule", "Rule"),
                            ("Definitions", "Definitions"),
                            ("File", "File"),
                        ],
                        max_length=20,
                    ),
                ),
                ("value", models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                (
                    "applied_to_categories",
                    models.CharField(
                        default="PET,PETGSM",
                        help_text="Comma-separated categories (e.g., 'PET,PETGSM')",
                        max_length=255,
                    ),
                ),
                (
                    "not_applied_to_categories",
                    models.CharField(
                        blank=True,
                        help_text="Comma-separated categories to exclude (e.g., 'PETGSM')",
                        max_length=255,
                        null=True,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name="ScheduleAssessment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("ica_number", models.IntegerField()),
                ("week", models.IntegerField()),
                ("weightage", models.DecimalField(decimal_places=2, max_digits=5)),
                ("over_two_weeks", models.BooleanField(default=False)),
                ("assessment_method", models.CharField(max_length=100)),
                (
                    "duration",
                    models.IntegerField(
                        blank=True, help_text="Duration in minutes", null=True
                    ),
                ),
                ("digital_submission", models.BooleanField(blank=True, null=True)),
                ("descriptions", models.TextField(blank=True, null=True)),
                ("topics", models.TextField(blank=True, null=True)),
                ("reasons_change", models.TextField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "assessment_type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="system_management.assessment",
                    ),
                ),
                (
                    "lu",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="system_management.learningunit",
                    ),
                ),
                (
                    "moderation_method",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="system_management.moderationmethod",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
