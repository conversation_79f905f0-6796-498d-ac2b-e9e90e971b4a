jQuery(function ($) {
    $(document).ready(function () {
        // Import bulk user
        $(document).on("change", "#upload-bulk-user", function (e) {
            $(".popup-validation__bulk-user").removeClass("d-none");
        });
        // End Import bulk user

        // Switch rule config on change
        $(document).on("change", "#switch-ica-schedule", function () {
            let lenEmptyValueSchedule = $(
                ".wrapper-rule-schedule .value"
            ).filter(function () {
                return $(this).val() === "" || $(this).val() === null;
            }).length;

            if (lenEmptyValueSchedule > 0) {
                $(".btn-apply-rule__schedule").addClass("disabled");
            } else {
                $(".btn-apply-rule__schedule").removeClass("disabled");
            }
        });
        $(document).on("change", "#switch-ica-detail", function () {
            let lenEmptyValueDetail = $(".wrapper-rule-detail .value").filter(
                function () {
                    return $(this).val() === "" || $(this).val() === null;
                }
            ).length;
            if (lenEmptyValueDetail > 0) {
                $(".btn-apply-rule__detail").addClass("disabled");
            } else {
                $(".btn-apply-rule__detail").removeClass("disabled");
            }
        });
        // Validate rule config
        function validationApplyRule() {
            let lenEmptyValueSchedule = $(
                ".wrapper-rule-schedule .value"
            ).filter(function () {
                return $(this).val() === "" || $(this).val() === null;
            }).length;
            let lenEmptyValueDetail = $(".wrapper-rule-detail .value").filter(
                function () {
                    return $(this).val() === "" || $(this).val() === null;
                }
            ).length;
            if (lenEmptyValueSchedule > 0) {
                $(".btn-apply-rule__schedule").addClass("disabled");
            } else {
                $(".btn-apply-rule__schedule").removeClass("disabled");
            }
            if (lenEmptyValueDetail > 0) {
                $(".btn-apply-rule__detail").addClass("disabled");
            } else {
                $(".btn-apply-rule__detail").removeClass("disabled");
            }
        }
        $(document).on(
            "click",
            ".wrapper-rule-detail .add-rule, .wrapper-rule-detail .add-condition, .remove-detail",
            function () {
                validationApplyRule();
            }
        );
        $(document).on(
            "click",
            ".wrapper-rule-schedule .add-rule, .wrapper-rule-schedule .add-condition, .remove-schedule",
            function () {
                validationApplyRule();
            }
        );
        $(document).on(
            "change",
            ".wrapper-rule-schedule #conditions select, .wrapper-rule-schedule #conditions input",
            function () {
                validationApplyRule();
            }
        );
        $(document).on(
            "change",
            ".wrapper-rule-detail #conditions-detail select, .wrapper-rule-detail #conditions-detail input",
            function () {
                validationApplyRule();
            }
        );

        $(document).on(
            "click",
            "#conditions .add-rule, #conditions .add-condition",
            function () {
                setTimeout(() => {
                    $("#conditions")
                        .find(".remove")
                        .addClass("remove-schedule");
                }, 300);
            }
        );
        $(document).on(
            "click",
            "#conditions-detail .add-rule, #conditions-detail .add-condition",
            function () {
                setTimeout(() => {
                    $("#conditions-detail")
                        .find(".remove")
                        .addClass("remove-detail");
                }, 300);
            }
        );
        // End Validate rule config
        // validate add new user
        function validateAddNewUser(that, emailVal, roleVal, statusVal) {
            $(".error-msg").remove();

            let validEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailVal);

            if (!validEmail && emailVal !== "") {
                that.parents("form")
                    .find('input[name="email"]')
                    .after(
                        '<small class="text-danger error-msg">Please enter a valid email address</small>'
                    );
                that.parents("form")
                    .find('button[type="submit"]')
                    .addClass("disabled");
            }
            if (emailVal == "") {
                that.parents("form")
                    .find('input[name="email"]')
                    .after(
                        '<small class="text-danger error-msg">Email is required</small>'
                    );
                that.parents("form")
                    .find('button[type="submit"]')
                    .addClass("disabled");
            }
            if (roleVal == "") {
                that.parents("form")
                    .find('select[name="role"]')
                    .parent()
                    .after(
                        '<small class="text-danger error-msg">Role is required</small>'
                    );
                that.parents("form")
                    .find('button[type="submit"]')
                    .addClass("disabled");
            }
            if (statusVal == "") {
                that.parents("form")
                    .find('select[name="status"]')
                    .parent()
                    .after(
                        '<small class="text-danger error-msg">Status is required</small>'
                    );
                that.parents("form")
                    .find('button[type="submit"]')
                    .addClass("disabled");
            }
            if (
                emailVal !== "" &&
                validEmail &&
                roleVal !== "" &&
                statusVal !== ""
            ) {
                that.parents("form")
                    .find('button[type="submit"]')
                    .removeClass("disabled");
            } else {
                that.parents("form")
                    .find('button[type="submit"]')
                    .addClass("disabled");
            }
        }
        $(document).on(
            "keyup",
            '.form-add-new-user input[name="email"], .form-edit-user input[name="email"]',
            function () {
                let roleVal = $(this)
                    .parents("form")
                    .find('select[name="role"]')
                    .val();
                let statusVal = $(this)
                    .parents("form")
                    .find('select[name="status"]')
                    .val();
                let that = $(this);
                let emailVal = $(this).val();
                validateAddNewUser(that, emailVal, roleVal, statusVal);
            }
        );
        $(document).on(
            "change",
            '.form-edit-user select[name="role"], .form-edit-user select[name="status"], .form-add-new-user select[name="role"], .form-add-new-user select[name="status"]',
            function () {
                let that = $(this);
                let roleVal = that
                    .parents("form")
                    .find('select[name="role"]')
                    .val();
                let statusVal = that
                    .parents("form")
                    .find('select[name="status"]')
                    .val();
                let emailVal = that
                    .parents("form")
                    .find('input[name="email"]')
                    .val();
                validateAddNewUser(that, emailVal, roleVal, statusVal);
            }
        );

        $(document).on(
            "change",
            '.form-bulk-status select[name="status"]',
            function () {
                let statusVal = $(this).val();
                if (statusVal !== "") {
                    $(this)
                        .parents("form")
                        .find('button[name="submit-bulk"]')
                        .removeClass("disabled");
                } else {
                    $(this)
                        .parents("form")
                        .find('button[name="submit-bulk"]')
                        .addClass("disabled");
                }
            }
        );
        // Remove params url change status live schedule
        $(document).on(
            "click",
            ".popup-confirm-status .backdrop, .popup-confirm-status button",
            function () {
                let url = $(".popup-confirm-status").attr("data-url");
                let newUrl = url.split("?")[0];
                window.history.pushState("object", document.title, newUrl);
            }
        );
        // Remove params url popup schedule
        $(document).on(
            "click",
            ".btn-popup-ica-schedule__submission",
            function () {
                try {
                    let url = $("body").data("url");
                    if (!url) {
                        console.warn(
                            "URL data attribute not found on body element"
                        );
                        return;
                    }

                    // Get the current origin
                    const currentOrigin = window.location.origin;

                    // Parse the URL to ensure it's from the same origin
                    let newUrl = new URL(url, currentOrigin);

                    // Only take the pathname and search parameters, strip any hash
                    let cleanPath = newUrl.pathname;

                    // Safely push the new state
                    window.history.pushState(
                        { page: cleanPath },
                        document.title,
                        cleanPath
                    );
                } catch (error) {
                    console.error("Error updating browser history:", error);
                }
            }
        );

        // end remove params url change status live schedule
        $(document).on("change", ".search-activity-log", function () {
            $('input[name="search-export-log"]').val($(this).val());
        });
        
        $(".table-lu-list tbody tr td:first-child div").click(function () {
            $(".table-lu-list tbody tr td:first-child div").removeClass(
                "active"
            );
            $(this).addClass("active");
        });
        $(document).on("change", ".input-weightage", function () {
            let valueWeightage = $(this).val();
            $(this)
                .parents(".item-ica-card")
                .find(".value-weightage")
                .text("(" + valueWeightage + "%)");
        });

        // ==========================================
        // Validate form ICA Schedule
        // ==========================================
        $(document).on(
            "change",
            ".item-ica-card select, .item-ica-card input",
            function () {
                let weekVal = $(this)
                    .parents(".outer-ica-schedule")
                    .find('select[name="week"]')
                    .val();
                let over2weekVal = $(this)
                    .parents(".outer-ica-schedule")
                    .find('select[name="assm-2-weeks"]')
                    .val();
                $(this)
                    .parents(".outer-ica-schedule")
                    .find(".error-week, .error-over-2-week")
                    .remove();

                let lenEmptyWeek = $('select[name="week"]').filter(function () {
                    return $(this).val() === "";
                }).length;
                let lenEmptyOver2Week = $('select[name="assm-2-weeks"]').filter(
                    function () {
                        return $(this).val() === "";
                    }
                ).length;
                let lenEmptyDuration = $('input[name="duration"]').filter(
                    function () {
                        return $(this).val() === "0";
                    }
                ).length;
                let lenEmptyModerationMethod = $(
                    'input[name="moderation_method"]'
                ).filter(function () {
                    return $(this).val() === "None";
                }).length;
                let lenEmptyAssessmentType = $(
                    'input[name="assessment_type"]'
                ).filter(function () {
                    return $(this).val() === "";
                }).length;
                let sumWeightAge = 0;
                $(".input-weightage").each(function () {
                    sumWeightAge += Number($(this).val());
                });

                if (weekVal === "") {
                    $(this)
                        .parents(".outer-ica-schedule")
                        .find('select[name="week"]')
                        .parent()
                        .after(
                            '<div class="error-week text-danger">Week is required</div>'
                        );
                }

                if (over2weekVal == "") {
                    $(this)
                        .parents(".outer-ica-schedule")
                        .find('select[name="assm-2-weeks"]')
                        .parent()
                        .after(
                            '<div class="error-over-2-week text-danger">Over 2 Week is required</div>'
                        );
                }

                if (
                    lenEmptyWeek > 0 ||
                    sumWeightAge !== 100 ||
                    lenEmptyOver2Week > 0
                    // lenEmptyDuration > 0 ||
                    // lenEmptyAssessmentType > 0 ||
                    // lenEmptyModerationMethod > 0
                ) {
                    $(".btn-submit-ica").addClass("disabled");
                } else {
                    $(".btn-submit-ica").removeClass("disabled");
                }
            }
        );
        // ==========================================
        // End Validate form ICA Schedule
        // ==========================================

        let currentYear = new Date().getFullYear();
        $(document).on("click", ".btn-edit-setcurrent", function () {
            localStorage.setItem("currentYear", currentYear);
        });

        $(document).on("click", ".clickable-row", function () {
            $("html, body").animate({ scrollTop: 0 }, 300);
        });
        $('input[name="download-type"]').click(function () {
            $(".container-radio").removeClass("fw-bold");
            $(this).parents(".container-radio").addClass("fw-bold");
        });

        // Current Year
        let setCurrentYear = $("#current-year-report").data("current-year");
        $("#current-year-report").html(`        
                <option value="${
                    currentYear - 1
                }" ${setCurrentYear == currentYear - 1 ? "selected" : ""}>${currentYear - 1}</option>
                <option value="${currentYear}" ${setCurrentYear == currentYear ? "selected" : ""}>${currentYear}</option>
                <option value="${
                    currentYear + 1
                }" ${setCurrentYear == currentYear + 1 ? "selected" : ""}>${currentYear + 1}</option>
            `);
        // Year study
        $("#year-study").html(`        
                <option>${currentYear - 1}</option>
                <option>${currentYear}</option>
                <option>${currentYear + 1}</option>        
            `);

        $(document).on("click", ".edit-files, .btn-reupload", function () {
            $(".button-save-files").removeClass("d-none");
        });
        let widthCardHeadmap = $(".card-headmap").width();
        let headmapHead = $(".headmap-head").width();
        let headmapsgap = parseInt($(".headmaps").css("gap"));
        $(".headmap-content").css({
            width: widthCardHeadmap - (headmapHead + headmapsgap),
        });

        $(".select-department").select2({
            placeholder: "Select department",
            width: "100%",
        });
        $(".select-lu").select2({
            placeholder: "Select LU Code",
            width: "100%",
        });
        $(document).on("click", ".table-lu-status tbody tr", function () {
            if ($(this).find('input[type="checkbox"]').is(":checked")) {
                $(this).find('input[type="checkbox"]').prop("checked", false);
            } else {
                $(this).find('input[type="checkbox"]').prop("checked", true);
            }
            let totalUserChecked = $(".checkbox-tr:checked").length;
            if (totalUserChecked > 0) {
                $(".btn-bulk-action").removeClass("disabled");
            } else {
                $(".btn-bulk-action").addClass("disabled");
            }
        });
        $(".account").click(function (e) {
            e.stopPropagation();
            $(".popup-account").toggleClass("d-none");
        });
        $("body").click(function () {
            $(".popup-account").addClass("d-none");
        });
        $(".menu-system-management li").each(function () {
            $(this).click(function () {
                $(".menu-system-management li").removeClass("active");
                $(this).addClass("active");
            });
        });

        $(document).on("change", "#checkbox-change-role", function () {
            if ($(this).is(":checked")) {
                $(".form-change-role").removeClass("d-none");
            } else {
                $(".form-change-role").addClass("d-none");
                $(".form-change-role select").val("");
            }
        });

        $(document).on("change", "#checkbox-change-status", function () {
            if ($(this).is(":checked")) {
                $(".form-change-status").removeClass("d-none");
            } else {
                $(".form-change-status").addClass("d-none");
                $(".form-change-status select").val("");
            }
        });

        $(document).on("change", "#checked-all", function () {
            if ($(this).is(":checked")) {
                $(".checkbox-tr").prop("checked", true);
                $(".btn-bulk-action").removeClass("disabled");
            } else {
                $(".btn-bulk-action").addClass("disabled");
                $(".checkbox-tr").prop("checked", false);
            }
        });

        let userArr = [];
        let listUser;

        $(document).on("click", ".btn-bulk-action", function () {
            $(".form-bulk-status")
                .find('input[name="id-lu"], input[name="id-user"]')
                .remove();
            userArr = [];
            userID = [];
            let totalUserChecked = $(".checkbox-tr:checked").length;
            $(".checkbox-tr:checked").each(function () {
                userArr.push($(this).parents("tr").find(".field-value").text());
                userID.push($(this).parents("tr").find(".value-id").text());
            });
            listUser = userArr.slice(0, 4);
            $(".list-selected-user").text(
                "Selected: " +
                    listUser.join(", ") +
                    ` ${
                        totalUserChecked > 4
                            ? `and ${totalUserChecked - 4} other.`
                            : ``
                    } `
            );
            userID.map((id, index) => {
                $(".form-bulk-user").prepend(`
                    <input type="hidden" value="${id}" name="id-user" />    
                `);
            });
            userID.map((id, index) => {
                $(".form-bulk-status").prepend(`
                    <input type="hidden" value="${id}" name="id-lu" />    
                `);
            });
        });
        $(document).on("change", ".checkbox-tr", function () {
            let countChecked = $(".checkbox-tr:checked").length;
            if (countChecked > 0) {
                $(".btn-bulk-action").removeClass("disabled");
            } else {
                $(".btn-bulk-action").addClass("disabled");
            }
        });

        $(document).on("click", ".show-popup", function () {
            let dataPopup = $(this).data("popup");

            if (dataPopup) {
                $(`.${dataPopup}`).removeClass("d-none");
            } else {
                $(this).next(".popup").removeClass("d-none");
            }
        });

        function CalculateWeightAge() {
            $(".warning-weightage").addClass("d-none");
            let sumWeightAge = 0;
            $(".input-weightage").each(function () {
                sumWeightAge += Number($(this).val());
            });
            if (sumWeightAge > 100) {
                $(".warning-weightage").removeClass("d-none");
                $(".message_weightage").text(
                    "Total weightage not allow greater than 100%"
                );
            } else if (sumWeightAge < 100) {
                $(".warning-weightage").removeClass("d-none");
                $(".message_weightage").text(
                    "Total weightage not allow less than 100%"
                );
            } else {
                $(".error-total-weightage").remove();
            }
        }

        $(document).on("blur", ".input-weightage", function () {
            $(".error-total-weightage").remove();
            CalculateWeightAge();
        });

        let timerInterval;
        $(document).on("click", ".show-confirm-delete-popup", function () {
            $(this).parents(".item-ica-card").addClass("delete");
            let url = $(this).data("url");
            let id = $(this).data("id");
            $(".popup-confirm-delete").removeClass("d-none");
            $(".remove-item-ica").attr("data-url", url);
            $(".remove-item-ica").attr("data-id", id);
        });
        $(document).on("click", ".cancel-delete, .backdrop", function () {
            $(".delete").removeClass("delete");
        });
        $(document).on("click", ".remove-item-ica", function () {
            let url = $(this).data("url");            
            clearInterval(timerInterval);
            localStorage.setItem("timerstart", "false");
            let csrfToken = $('input[name="csrfmiddlewaretoken"]').val();
            let test_mode = $('input[name="test_mode"]').val();

            $.post(
                url,
                {
                    csrfmiddlewaretoken: csrfToken,
                    test_mode: test_mode,
                },
                function () {
                    htmx.trigger(".content-card-ica", "ica-changed");
                }
            );
        });

        // ===================================================
        // Validate ICA Detail + btn-ica-detail clicked Func
        // ===================================================
        $(document).on("click", ".btn-ica-detail", function () {
            clearInterval(timerInterval);
            document.addEventListener("htmx:afterRequest", function (evt) {
                $(document).on("click", ".save-change-ica-detail", function () {
                    $(this).addClass("disabled");
                    $(".message-ica-detail").removeClass("d-none");
                    htmx.trigger(".content-card-ica", "detail-changed");
                });
            });
        });

        // =================================
        // Validate Form ICA Detail
        // =================================
        function checkDuration() { 
            let totalPracDurationEmpty = 0;
            let totalTestDurationEmpty = 0;
            $(".prac").each(function () {
                let lenPracDurationEmpty = $(this)
                    .find('input[name="duration"]')
                    .filter(function () {
                        return $(this).val() === "";
                    }).length;
                totalPracDurationEmpty += lenPracDurationEmpty;
            });
            $(".test").each(function () {
                let lenTestDurationEmpty = $(this)
                    .find('input[name="duration"]')
                    .filter(function () {
                        return $(this).val() === "";
                    }).length;
                totalTestDurationEmpty += lenTestDurationEmpty;
            });
            
            
            if (totalPracDurationEmpty == 0 && totalTestDurationEmpty == 0 ) {
                $(".save-change-ica-detail").removeClass("disabled");
                $(".message-ica-detail").addClass("d-none");
            } else {
                $(".save-change-ica-detail").addClass("disabled");
            }
        }

        function checkAllVal(e){
            let parent = $(e.target).parents('.item-ica-detail-card')   
                          
            parent.find('textarea, select').each(function(){
                if($(this).val() == "") {
                    $(this).parents('.form-group')
                    .next(`.err-msg-${$(this).attr('name')}`)
                    .remove();
                    $(this).parents('.form-group')
                    .after(
                        `<div class="text-danger mt-n2 mb-3 err-msg-${$(this).attr('name')}">${$(this).attr('title')} is required</div>`
                    );
                }else {
                    $(this).parents('.form-group')
                    .next(`.err-msg-${$(this).attr('name')}`)
                    .remove();
                }
            })
        }

       

        function validateFormDetail(e, title, lenTest, lenPrac) {
            // Handling error message
            let parent = $(e.target).parents('.item-ica-detail-card')   
            
            if (lenPrac > 0 || lenTest > 0) {
                let isParentPrac = $(e.target).parents(".prac").length > 0;
                let isParentTest = $(e.target).parents(".test").length > 0;
                
                if (isParentPrac || isParentTest) {
                    let parent = $(e.target).parents('.item-ica-detail-card') 
                    let durationVal = parent.find('input[name="duration"]').val();
                    
                    if(durationVal == ""){
                        parent.find('input[name="duration"]').parents('.form-input-group')
                            .next(`.err-msg-duration`)
                            .remove();
                        parent.find('input[name="duration"]').parents('.form-input-group')
                            .after(
                                `<div class="text-danger mt-n2 mb-3 err-msg-duration">Input is required</div>`
                            );
                    }else{
                        parent.find('input[name="duration"]').parents('.form-input-group')
                            .next(`.err-msg-duration`)
                            .remove();
                    }
                    checkDuration()
                    checkAllVal(e)                    
                } 
                checkAllVal(e)  
            } else {
                checkAllVal(e)                
            }

            // Handling toggle enabled button save changes
            function emptySelect() {
                return $(".item-ica-detail-card select").filter(function () {
                    return $(this).val() === "";
                }).length;
            }
            function emptyTextarea() {
                return $(".item-ica-detail-card textarea:not([name='note'])").filter(function () {
                    return $(this).val() === "";
                }).length;
            }

            setTimeout(() => {
                emptySelect();
                emptyTextarea();
            }, 500);

            let lengthCharsNote = parent.find('textarea[name="note"]').val().length
            if(lengthCharsNote > 500){
                parent.find('textarea[name="note"]').parents('.form-group').next('.err-msg-note').remove()
                parent.find('textarea[name="note"]').parents('.form-group').after(`<div class="text-danger mt-n2 mb-3 err-msg-note">Note not allow exceed 500 characters</div>`)
            }else{
                parent.find('textarea[name="note"]').parents('.form-group').next('.err-msg-note').remove()
            }

            let countErrorNote = $('textarea[name="note"]').filter(function(){
                return $(this).val().length > 500
            }).length

           
            if (emptySelect() == 0 && emptyTextarea() == 0 && countErrorNote == 0) {
                if (lenPrac > 0 || lenTest > 0) {
                    checkDuration();
                } else {
                    $(".save-change-ica-detail").removeClass("disabled");
                }
            } else {
                $(".save-change-ica-detail").addClass("disabled");
            }
        }
        $(document).on("change", ".item-ica-detail-card select", function (e) {
            let title = $(this).attr("title");
            let lenPrac = $(".prac").length;
            let lenTest = $(".test").length;
            validateFormDetail(e, title, lenTest, lenPrac);
            
        });
        $(document).on(
            "keyup",
            ".item-ica-detail-card textarea, .item-ica-detail-card input",
            function (e) {
                let title = $(this).attr("title");
                let lenPrac = $(".prac").length;
                let lenTest = $(".test").length;
                validateFormDetail(e, title, lenTest, lenPrac);
            }
        );
        // $(document).on("keyup", "textarea[name='note']", function () {
        //     let lengthChars = $(this).val().length
        //     console.log(lengthChars)
        // });

        // =================================
        // Validasi upload field
        // =================================
        $(document).on("change", "#file-upload-form input", function () {
            let lenInputUpload = $("#file-upload-form input").filter(
                function () {
                    return $(this).val() !== "";
                }
            ).length;
            if (lenInputUpload > 0) {
                $(".btn-save-upload").removeClass("disabled");
            }
        });
        // =================================
        // End validasi upload field
        // =================================
        //==================================
        // Validasi duration ICA detail
        //==================================
        document.addEventListener("htmx:afterRequest", function (evt) {
            $(".container-error:contains('Duration')")
                .parents(".item-ica-detail-card")
                .find('input[name="duration"]')
                .addClass("is-invalid");
            $(".container-error:contains('Testfaweek_Inclusive')")
                .parents(".item-ica-detail-card")
                .find('select[name="digital-submission"]')
                .addClass("is-invalid");
        });
        //==================================
        // End Validasi duration ICA detail
        //==================================
        function countDown() {
            let timer2 = "10:00";
            timerInterval = setInterval(function () {
                let timer = timer2.split(":");
                let minutes = parseInt(timer[0], 10);
                let seconds = parseInt(timer[1], 10);
                --seconds;
                minutes = seconds < 0 ? --minutes : minutes;
                if (minutes < 0) clearInterval(timerInterval);
                seconds = seconds < 0 ? 59 : seconds;
                seconds = seconds < 10 ? "0" + seconds : seconds;
                $(".countdown").html(minutes + ":" + seconds);
                timer2 = minutes + ":" + seconds;
                if (minutes == 0 && seconds == 0) {
                    $(".week").val("");
                }
                if (minutes < 0) {
                    $(".popup-timeout").removeClass("d-none");
                    $(".countdown").html("00:00");
                }
            }, 1000);
        }
        $(document).on("click", ".close-popup, .backdrop", function () {
            $(".popup").addClass("d-none");
        });
        $(document).on("click", ".close-time-out", function () {
            countDown();
        });

        let credit = $(".content-card-ica").data("credit");

        function CountNumberICA() {
            let countIca = $(".item-ica-card").length + 1;
            let countIcaDetail = $(".item-ica-detail-card").length + 1;
            for (let i = 0; i < countIca; i++) {
                $(document)
                    .find(`.item-ica-card:eq(${i})`)
                    .find(".number-ica")
                    .text(i + 1);
            }
            for (let i = 0; i < countIcaDetail; i++) {
                $(document)
                    .find(`.item-ica-detail-card:eq(${i})`)
                    .find(".number-ica")
                    .text(i + 1);
            }
            if (countIca - 1 == 0) {
                $(".no-ica").removeClass("d-none");
            }
        }

        // Handle popup copy from last
        $(document).on("click", "#copy-from-last", function () {
            if ($(this).is(":checked")) {
                $(".popup-copy-from-last").removeClass("d-none");
            }
        });
        $(document).on("click", ".cancel-copy-from-last", function () {
            $("#copy-from-last").prop("checked", false);
        });
        //End Handle popup copy from lastsave_detail

        // ==============================
        // Display loading uplaod
        // ==============================
        $(document).on("click", ".btn-proceed-upload", function () {
            $(".loading-upload").removeClass("d-none");
        });
        // ==============================
        // End Display loading uplaod
        // ==============================
        // ==============================
        // Display FORM Edit ICA
        // ==============================
        $(document).on("click", ".proceed-edit", function () {
            $(".form-ica-schedule").removeClass("ica-disabled");
            $("input.assm-task").addClass("assm-task-edit");
            $(".btn-submit-ica, .btn-cancel-ica, .add-ica").removeClass(
                "d-none"
            );
            $(".btn-edit-schedule").addClass("d-none");
            $(".item-schedule:last-child").addClass("d-none");
            $(".item-schedule").css("width", "25%");
            $(".timer-countdown").removeClass("d-none");
            countDown();
            CountIcaFun();
            localStorage.setItem("timerstart", "true");
        });
        // ==============================
        // End Display FORM Edit ICA
        // ==============================
        // ==============================
        // First load after HTMX Request
        // ==============================
        document.body.addEventListener("htmx:afterRequest", function (evt) {
            CountNumberICA();
            $(".popup-confirm-status").attr(
                "data-url",
                $(location).attr("href")
            );
        });
        // ==============================
        // First load after HTMX Request
        // ==============================

        function CountIcaFun() {
            let countIca = $(".item-ica-card").length;

            if (countIca == parseInt(credit) + 1) {
                $(".add-ica").addClass("disabled");
            }
            if (countIca < parseInt(credit) + 1) {
                $(".add-ica").removeClass("d-none disabled");
            }
        }

        $(document).on("click", ".btn-ica-schedule", function () {
            CountNumberICA();
            document.addEventListener("htmx:afterRequest", function (evt) {
                $(".add-ica").addClass("d-none");
            });
        });
        let countIca = $(".item-ica-card").length + 1;

        // init load
        localStorage.setItem("timerstart", "false");

        $(document).on("click", ".add-ica", function () {
            $(".btn-submit-ica").addClass("disabled");
            CountNumberICA();
            let countIcaForm = $(".item-ica-card-form").length + 1;
            document.addEventListener("htmx:afterRequest", function (evt) {
                CountIcaFun();
            });
            if (localStorage.getItem("timerstart") !== "true") {
                if (countIcaForm == 1) {
                    countDown();
                    $(".btn-submit-ica").removeClass("d-none");
                }
            }

            $(".timer-countdown").removeClass("d-none");
            $(".loading-ica").css({
                display: "block",
                "z-index": 5,
            });
            let test_mode = $(".content-card-ica").data("testmode");

            if (countIca <= credit + 1) {
                let id = $(".content-card-ica").data("id");

                // Get the container element
                var container = document.querySelector(".container-ica");
                // Configure the HTMX request
                htmx.ajax("POST", `/add-ica/${id}`, {
                    target: container,
                    swap: "beforeend",
                    values: {
                        test_mode: test_mode,
                        ica_number:
                            document.querySelectorAll(".item-ica-card").length +
                            1,
                        csrfmiddlewaretoken: document.querySelector(
                            "[name=csrfmiddlewaretoken]"
                        ).value,
                    },
                    indicator: ".loading-ica",
                });

                document.addEventListener("htmx:beforeRequest", function (evt) {
                    $(".inner-card").addClass("d-none");
                });
                document.addEventListener("htmx:afterRequest", function (evt) {
                    $(".inner-card").addClass("d-none");
                    if (evt.detail.target === container) {
                        // container is the target we defined
                        document.querySelector(".loading-ica").style.display =
                            "none";
                        document.querySelector(".loading-ica").style.zIndex =
                            "-999";
                        document
                            .querySelector(".btn-cancel-ica")
                            .classList.remove("d-none");
                    }
                });
            }
        });
        // ==============================
        // Function there is no ICA CARD
        // ==============================
        function isIcaCardEmpty() {
            let countIca = $(".item-ica-card").length;
            if (countIca == 0) {
                $(".no-ica, .add-ica").removeClass("d-none");
                $(".btn-edit-schedule").addClass("d-none");
            } else {
                $(".no-ica").addClass("d-none");
            }
        }
        // ==============================
        // End Function there is no ICA CARD
        // ==============================

        $(document).on("click", ".btn-cancel-ica", function () {
            clearInterval(timerInterval);
            $(".btn-submit-ica, .btn-cancel-ica, .timer-countdown").addClass(
                "d-none"
            );
            if (countIca < 1) {
                $(".inner-card").removeClass("d-none");
            }
            $(".btn-edit-schedule").removeClass("d-none");
            $(".form-ica-schedule").addClass("ica-disabled");
            $(".btn-submit-ica, .btn-cancel-ica, .add-ica").addClass("d-none");
            $(".error-total-weightage").remove();
            $(".item-schedule:last-child").removeClass("d-none");
            $(".item-schedule").css("width", "20%");

            let arrID = [];
            $(".item-ica-card-form").each(function () {
                let id = $(this)
                    .find('input[name="schedule_assessment"]')
                    .val();
                arrID.push(id);
            });
            let csrftoken = $('input[name="csrfmiddlewaretoken"]').val();
            let test_mode = $('input[name="test_mode"]').val();
            for (let i = 0; i < arrID.length; i++) {
                $.post(
                    `/delete-ica/${arrID[i]}`,
                    { csrfmiddlewaretoken: csrftoken, test_mode: test_mode },
                    function () {
                        $(".item-ica-card-form").remove();
                        isIcaCardEmpty();
                    }
                );
            }
        });


        //========
        if($('#currentYear').length > 0) {
            $('#currentYear').text(new Date().getFullYear());
        }

    });
});
