# Generated by Django 5.1 on 2024-10-22 10:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

def delete_data(apps, schema_editor):
    # Get the model from the versioned app registry
    # Replace 'YourApp' and 'YourModel' with your actual app and model names
    YourModel = apps.get_model('system_management', 'LearningUnit')
    
    # Delete all instances
    YourModel.objects.all().delete()

class Migration(migrations.Migration):

    dependencies = [
        ("system_management", "0019_userlearningunit"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]
    operations = [
        migrations.RunPython(delete_data, reverse_code=migrations.RunPython.noop),
        # Add any other operations after data deletion
    ]
    operations = [
        migrations.AddField(
            model_name="learningunit",
            name="status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("saved", "Saved"),
                    ("approved", "Approved"),
                    ("submitted", "Submitted"),
                    ("rework", "Rework"),
                ],
                default="pending",
                max_length=10,
            ),
        ),
        migrations.AddField(
            model_name="learningunit",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.DeleteModel(
            name="UserLearningUnit",
        ),
    ]
