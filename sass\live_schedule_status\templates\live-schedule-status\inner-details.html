
{% for item in schedule_assessment_details %}
<tr>
    <td>{{ item.assessment_task }}</td>
    <td>{{ item.week  }}</td>
    <td>{{ item.assessment_method.method }}</td>
    <td>{{ item.weightage }}</td>
    <td>{{ item.duration }}</td>
    <td>{{ item.get_over_two_weeks_display }}</td>
    <td>{{ item.assessment_type|title }}</td>
    <td class="p-relative has-tooltip__live-schedule">
        {{ item.descriptions }}
        <div class="tooltip align-right">
            Desc. of assm instrument & format, {{item.assessment_task}}, {{item.lu.code}}

        </div>
    </td>
    <td>{{ item.moderation_method|title }}</td>
    <td>{{ item.topics }}</td>
    <td>{{ item.get_digital_submission_display }}</td>
    <td class="p-relative has-tooltip__live-schedule">{{ item.reasons_change }}
        <div class="tooltip align-left">
            Reason for change in assm,
            {{item.assessment_task}}, {{item.lu.code}}
        </div>
    </td>
</tr>
{% endfor %}

{% if learning_unit.message %}
<script>
    var message_specific_lu = document.getElementById("message-specific-lu")
    if (message_specific_lu){
        message_specific_lu.classList.remove('d-none')

        message_specific_lu_message = document.getElementById("message-specific-lu-message")
        if (message_specific_lu_message){
            message_specific_lu_message.innerHTML = "{{learning_unit.message|title}}"
        }
    }
</script>

{% else %}
<script>
    var message_specific_lu = document.getElementById("message-specific-lu")
    if (message_specific_lu){
        message_specific_lu.classList.remove('d-none')
        message_specific_lu.classList.add('d-none')
    }
</script>
{% endif %}