from django.db import models
import uuid
from django.utils.text import slugify
# Create your models here.

class ToolTip(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    slug = models.SlugField(max_length=255, unique=True,default="")
    name = models.CharField(max_length=255,null=True, blank=True)
    content = models.TextField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.content}"


class ErrorLog(models.Model):
    timestamp = models.DateTimeField(auto_now_add=True)
    error_type = models.CharField(max_length=255)
    error_message = models.TextField()
    stack_trace = models.TextField()
    path = models.Char<PERSON>ield(max_length=255)
    method = models.Char<PERSON>ield(max_length=10)
    user_id = models.IntegerField(null=True, blank=True)
    request_data = models.JSONField(null=True, blank=True)

    class Meta:
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.error_type} at {self.timestamp}"