from django.apps import AppConfig

class SystemManagementConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'system_management'

    def ready(self):
        from django.db.models.signals import post_migrate
        from django.dispatch import receiver
        from .models import SystemCurrentYear

        @receiver(post_migrate)
        def initialize_system(sender, **kwargs):
            # sender:  easyaudit
            # self.name:  system_management
            # sender:  sass_app
            # self.name:  system_management
            # sender:  system_management
            if sender.name == self.name: #select correct signal. Only allowed, if it is coming from system_management
                SystemCurrentYear.initialize()
                