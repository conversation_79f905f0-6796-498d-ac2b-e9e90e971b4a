
import json
import subprocess
import os
import sys
import time
import hmac
import hashlib
import logging
from functools import wraps
from pathlib import Path
from io import StringIO

import boto3
from botocore.exceptions import NoCredentialsError, ClientError

from django.http import JsonResponse, FileResponse, HttpResponse
from django.core.exceptions import ObjectDoesNotExist
from django.views.decorators.http import require_http_methods
from django.contrib.auth.models import User
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from django.core.management import call_command
from django.urls import reverse

from .microsoft_auth.auth_helper import test_microsoft_connection
from sass_app.middleware import ErrorLoggingMiddleware
#API
def require_api_key(view_func):
    @wraps(view_func)
    def wrapped(request, *args, **kwargs):
        auth_header = request.headers.get('Authorization')
        
        if not auth_header:
            return JsonResponse({'error': 'Authorization header is required'}, status=401)
            
        parts = auth_header.split()
        if len(parts) != 2 or parts[0].lower() != 'bearer':
            return JsonResponse({'error': 'Bearer token required'}, status=401)
            
        token = parts[1]
        if token != settings.SECRET_KEY:
            return JsonResponse({'error': 'Invalid token'}, status=401)
            
        return view_func(request, *args, **kwargs)
    return wrapped


@require_api_key
@require_http_methods(["GET"])
def check_microsoft_connection(request):
    result = test_microsoft_connection()
    return JsonResponse(result)


def serialize_user(user):
        """Helper function to serialize a user object"""
        return {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'is_staff':user.is_staff,
            'is_superuser': user.is_superuser,
            'date_joined': user.date_joined
        }

@require_api_key
@require_http_methods(["GET"])
def users(request):
    try:
        users_list = User.objects.filter().order_by('-date_joined')
        
        data = [serialize_user(user) for user in users_list]

        return JsonResponse({
                    "data":{'users': data},
                    "success": True,
                    "message": "Users Fetched"
                })
    
    except Exception as e:
        return JsonResponse(
            {
                "data":[],
                "success": False,
                "message": str(e)
            }
            , status=500)

@csrf_exempt
@require_api_key
@require_http_methods(["POST"])
def manage_user(request, user_id=None):
    """Handle POST requests to update user details"""
    try:

        data = json.loads(request.body)

        if user_id:
            user = User.objects.filter(id=user_id).first()
        else:
            user = User.objects.filter(email=data['email']).first()
            if not user:
                user = User.objects.create(email=data['email'])
                user.username = data['email'].split("@")[0]
        
        # Update fields if provided
        if 'email' in data:
            user.email = data['email']
        if 'first_name' in data:
            user.first_name = data['first_name']
        if 'last_name' in data:
            user.last_name = data['last_name']
        if 'is_staff' in data:
            user.is_staff = data['is_staff']
        if 'is_superuser' in data:
            user.is_superuser = data['is_superuser']
        if 'password' in data:
            user.set_password(data['password'])
            
        user.save()
        return JsonResponse({
                "data":{'users': serialize_user(user)},
                "success": True,
                "message": "Users Fetched"
            })
    
    except ObjectDoesNotExist:
        return JsonResponse({
                "data":[],
                "success": False,
                "message": 'User not found'
            }, status=404)
    
    except json.JSONDecodeError:
        return JsonResponse(
            {
                "data":[],
                "success": False,
                "message": 'Invalid JSON'
            }
            , status=400)
    except Exception as e:
        return JsonResponse(
            {
                "data":[],
                "success": False,
                "message": str(e)
            }
            , status=400)
    

@csrf_exempt
@require_api_key
@require_http_methods(["DELETE"])
def delete_user(request, user_id):
    """Handle POST requests to update user details"""
    try:

        User.objects.filter(id=user_id).first().delete()
        return JsonResponse(
            {
                "data":{'user_id':user_id},
                "success": True,
                "message": "User Deleted Successfully"
            }
            , status=200)
    
    except Exception as e:
        return JsonResponse(
            {
                "data":[],
                "success": False,
                "message": str(e)
            }
            , status=400)


@csrf_exempt
@require_api_key
@require_http_methods(["DELETE"])
def delete_file(request, user_id=None):
    """Handle DELETE requests to delete file"""
    try:

        data = json.loads(request.body)
        path = "masterfiles/"+data['name']

        file_path = os.path.join(settings.MEDIA_ROOT, path)

        if os.path.exists(file_path):
            os.remove(file_path)
        else:
            return JsonResponse(
            {
                "data":[],
                "success": False,
                "message": 'File not found'
            }
            , status=404)
        
        return JsonResponse({
                "data":{'file': data['name']},
                "success": True,
                "message": "Successfully deleted"
            })
    except json.JSONDecodeError:
        return JsonResponse(
            {
                "data":[],
                "success": False,
                "message": 'Invalid JSON'
            }
            , status=400)
    except Exception as e:
        return JsonResponse(
            {
                "data":[],
                "success": False,
                "message": str(e)
            }
            , status=400)


def reload_server():
    """Trigger reload by touching key project files"""
    try:
        # List of files to touch
        files_to_touch = [
            os.path.join(settings.BASE_DIR, 'sass', 'asgi.py'),
        ]
        
        touched_files = []
        for file_path in files_to_touch:
            if os.path.exists(file_path):
                # Touch the file to update its timestamp
                with open(file_path, 'a'):
                    os.utime(file_path, None)
                touched_files.append(os.path.basename(file_path))
        
        if touched_files:
            return True, f"Triggered reload by updating: {', '.join(touched_files)}"
        return False, "No project files found to trigger reload"
    except Exception as e:
        return False, f"Reload trigger failed: {str(e)}"

@csrf_exempt
@require_api_key
@require_http_methods(["POST"])
def run_migrations(request):
    """Run Django migrations via API endpoint"""
    try:
        # Get the app name from request body (optional)
        data = json.loads(request.body)
        command = data.get('command')
        if not command:
            return JsonResponse({
                'status': 'error',
                'message': "command not found"
            }, status=400)
        
        auto_reload = data.get('auto_reload', False) == True

        # Run the migration
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            cwd=settings.BASE_DIR
        )

        response_data = {
            'status': 'success' if result.returncode == 0 else 'error',
            'command_output': result.stdout if result.returncode == 0 else result.stderr
        }
        
        # Auto reload if makemigrations was successful and auto_reload is true
        if (auto_reload and 
            result.returncode == 0 and ('makemigrations' in command or 'migrate' in command)):
            reload_success, reload_message = reload_server()
            response_data['reload'] = {
                'status': 'success' if reload_success else 'error',
                'message': reload_message
            }
        
        status_code = 200 if result.returncode == 0 else 500
        return JsonResponse(response_data, status=status_code)
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)
    

@csrf_exempt
@require_http_methods(["POST"])
@require_api_key
def explore_directory(request):
    """Explore directory contents via API endpoint"""
    try:
        data = json.loads(request.body)
        path = data.get('path', '')
        
        # Sanitize and validate the path
        base_dir = str(settings.BASE_DIR)
        target_path = os.path.normpath(os.path.join(base_dir, path))
        
        # Security check - ensure path doesn't go above base directory
        if not target_path.startswith(base_dir):
            return JsonResponse({
                'status': 'error',
                'message': 'Invalid path - cannot access parent directories'
            }, status=403)

        if not os.path.exists(target_path):
            return JsonResponse({
                'status': 'error',
                'message': 'Path does not exist'
            }, status=404)

        # Generate breadcrumbs
        relative_path = os.path.relpath(target_path, base_dir)
        breadcrumbs = []
        current_path = ""
        
        if relative_path != ".":  # Not in root directory
            path_parts = relative_path.split(os.sep)
            for part in path_parts:
                current_path = os.path.join(current_path, part)
                breadcrumbs.append({
                    'name': part,
                    'path': current_path
                })

        # Get directory contents
        contents = []
        try:
            for item in os.listdir(target_path):
                item_path = os.path.join(target_path, item)
                contents.append({
                    'name': item,
                    'type': 'directory' if os.path.isdir(item_path) else 'file',
                    'size': os.path.getsize(item_path) if os.path.isfile(item_path) else None,
                    'modified': os.path.getmtime(item_path),
                    'path': os.path.join(path, item) if path else item
                })
            print("BASE_DIR: ", settings.BASE_DIR)
            return JsonResponse({
                'status': 'success',
                'current_path': path,
                'base_dir_code':base_dir,
                'setting_base_dir':str(settings.BASE_DIR),
                'breadcrumbs': [{'name': 'root', 'path': ''}] + breadcrumbs,
                'contents': sorted(contents, key=lambda x: (x['type'] != 'directory', x['name'].lower()))
            })

        except PermissionError:
            return JsonResponse({
                'status': 'error',
                'message': 'Permission denied'
            }, status=403)

    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)
    

@csrf_exempt
@require_http_methods(["POST"])
@require_api_key
def test_s3_connection(request):
    try:
        data = json.loads(request.body)
        bucket_name = data.get('bucket_name', '')

        # Initialize S3 client
        s3 = boto3.client('s3', region_name='ap-southeast-1')

        # Try to list objects in your bucket
        response = s3.list_objects_v2(
            Bucket=bucket_name,
            # MaxKeys=1  # Just list one object to test
        )

        if "Contents" in response:
            files = [{"Key": obj["Key"], "Size": obj["Size"]} for obj in response["Contents"]]
        else:
            files = []

        # Get bucket location
        bucket_location = s3.get_bucket_location(
            Bucket=bucket_name
        )

        sts = boto3.client('sts', region_name='ap-southeast-1')
        identity = sts.get_caller_identity()
        
        return JsonResponse({
            'status': 'success',
            'message': 'Successfully connected to S3',
            'bucket_location':bucket_location.get('LocationConstraint'),
            'objects_found': response.get('KeyCount', 0),
            'files':files,
            'bucket': bucket_name,
            'identity':identity
        })
    except Exception as e:
        # Get caller identity using STS
        sts = boto3.client('sts', region_name='ap-southeast-1')
        identity = sts.get_caller_identity()
        return JsonResponse({
            'status': 'error',
            'message': str(e),
            'identity':identity
        }, status=500)
    


@csrf_exempt
@require_http_methods(["POST"])
@require_api_key
@csrf_exempt  # Disable CSRF for simplicity (use proper CSRF handling in production)
def upload_file(request):
    """
    Django view to handle file uploads to S3.
    """
    # AWS S3 Configuration
    AWS_ACCESS_KEY = ''  # Optional: Use IAM role instead
    AWS_SECRET_KEY = ''  # Optional: Use IAM role instead
    
    bucket_name = request.POST.get('bucket_name','t-stg-sass-s3-bucket')

    AWS_BUCKET_NAME = bucket_name
    AWS_REGION = 'ap-southeast-1'

    if AWS_ACCESS_KEY and AWS_SECRET_KEY:
        # Initialize S3 client
        s3_client = boto3.client(
            's3',
            aws_access_key_id=AWS_ACCESS_KEY,
            aws_secret_access_key=AWS_SECRET_KEY,
            region_name=AWS_REGION
        )
    else:
        # Initialize S3 client
        s3_client = boto3.client(
            's3',
            region_name=AWS_REGION
        )
        print("s3_client: ", s3_client)

    if request.method != 'POST':
        return JsonResponse({"error": "Only POST requests are allowed"}, status=405)

    if 'file' not in request.FILES:
        return JsonResponse({"error": "No file provided"}, status=400)

    file = request.FILES['file']
    if file.name == '':
        return JsonResponse({"error": "No file selected"}, status=400)

    try:
        # Upload the file to S3
        s3_client.upload_fileobj(
            file,
            AWS_BUCKET_NAME,
            file.name
        )
        file_url = f"https://{AWS_BUCKET_NAME}.s3.{AWS_REGION}.amazonaws.com/{file.name}"
        return JsonResponse({"message": "File uploaded successfully", "file_url": file_url}, status=200)
    except NoCredentialsError:
        return JsonResponse({"error": "AWS credentials not found"}, status=500)
    except Exception as e:
        return JsonResponse({"Exception error": str(e)}, status=500)
    

@csrf_exempt
@require_http_methods(["POST"])
@require_api_key
def s3_upload_v2(request):
    try:

        bucket_name = request.POST.get('bucket_name','t-stg-sass-s3-bucket')
        
        AWS_BUCKET_NAME = bucket_name
        AWS_REGION = 'ap-southeast-1'
        file = request.FILES['file']
     
        s3_client = boto3.client(
            's3',
            region_name=AWS_REGION
        )
        response = s3_client.put_object(
            Bucket=AWS_BUCKET_NAME,
            Key=file.name,
            Body=file,
            ContentType=file.content_type,
            ServerSideEncryption="AES256"
        )

        if response['ResponseMetadata']['HTTPStatusCode'] == 200:
            file_url = f"https://{AWS_BUCKET_NAME}.s3.{AWS_REGION}.amazonaws.com/{file.name}"
            return JsonResponse({"status": "success", "file_url": file_url})
        else:
            return JsonResponse({
                "error": f"Upload failed with status code: {response['ResponseMetadata']['HTTPStatusCode']}"
            }, status=500)

    except Exception as err:
        return JsonResponse({"error": str(err)}, status=500)
    


@csrf_exempt
@require_http_methods(["POST"])
@require_api_key
def get_file(request):
    file_name = request.POST.get('file_name')  # Get filename from request
    bucket_name = request.POST.get('bucket_name','t-stg-sass-s3-bucket')
    AWS_BUCKET_NAME = bucket_name
    AWS_REGION = 'ap-southeast-1'
    try:
        
        # Generate presigned URL that expires in 3600 seconds (1 hour)
        s3_client = boto3.client(
            's3',
            region_name=AWS_REGION
        )
        url = s3_client.generate_presigned_url(
            'get_object',
            Params={
                'Bucket': AWS_BUCKET_NAME,
                'Key': file_name
            },
            ExpiresIn=86400
        )
        return JsonResponse({'download_url': url})
    except ClientError as e:
        return JsonResponse({'error': str(e)}, status=500)
    
@csrf_exempt
@require_http_methods(["POST"])
@require_api_key
def delete_file_s3(request):
    """Delete S3 file using file ID for more specific identification"""
    try:
        data = json.loads(request.body)
        file_id = data.get('file_id')
        bucket_name = data.get('bucket_name', 't-stg-sass-s3-bucket')

        if not file_id:
            return JsonResponse({'error': 'file_id is required'}, status=400)

        AWS_REGION = 'ap-southeast-1'
        s3_client = boto3.client('s3', region_name=AWS_REGION)

        # Comprehensive file validation before deletion
        try:
            response = s3_client.list_objects_v2(Bucket=bucket_name)

            if 'Contents' not in response:
                return JsonResponse({'error': 'No files found in the bucket'}, status=404)

            # Find all files with matching ETag
            matching_files = []
            for obj in response['Contents']:
                obj_etag = obj['ETag'].strip('"')
                if obj_etag == file_id:
                    matching_files.append({
                        'key': obj['Key'],
                        'size': obj['Size'],
                        'last_modified': obj['LastModified'].isoformat(),
                        'etag': obj_etag
                    })

            # Check file existence and handle duplicates
            if len(matching_files) == 0:
                return JsonResponse({
                    'error': 'File not found with the given ETag ID',
                    'file_id': file_id,
                    'status': 'not_found'
                }, status=404)

            elif len(matching_files) > 1:
                return JsonResponse({
                    'error': 'Multiple files found with the same ETag',
                    'file_id': file_id,
                    'status': 'duplicate_etag',
                    'matching_files': matching_files,
                    'message': 'This could indicate duplicate files. Please specify which file to delete by path.'
                }, status=409)  # 409 Conflict

            # Single file found - proceed with validation
            target_file = matching_files[0]
            target_key = target_file['key']

            # Additional validation: Check if file still exists (race condition protection)
            try:
                s3_client.head_object(Bucket=bucket_name, Key=target_key)
            except ClientError as e:
                if e.response['Error']['Code'] == '404':
                    return JsonResponse({
                        'error': 'File was deleted by another process',
                        'file_id': file_id,
                        'status': 'already_deleted',
                        'target_path': target_key
                    }, status=410)  # 410 Gone
                else:
                    raise

            # File validation passed - proceed with deletion
            s3_client.delete_object(
                Bucket=bucket_name,
                Key=target_key
            )

            # Verify deletion was successful
            try:
                s3_client.head_object(Bucket=bucket_name, Key=target_key)
                # If we reach here, deletion failed
                return JsonResponse({
                    'error': 'File deletion failed - file still exists',
                    'file_id': file_id,
                    'status': 'deletion_failed',
                    'target_path': target_key
                }, status=500)
            except ClientError as e:
                if e.response['Error']['Code'] == '404':
                    # Deletion successful
                    return JsonResponse({
                        'message': f'File {target_key} deleted successfully',
                        'file_id': file_id,
                        'deleted_path': target_key,
                        'status': 'deleted',
                        'file_info': {
                            'size': target_file['size'],
                            'last_modified': target_file['last_modified']
                        }
                    })
                else:
                    raise

        except ClientError as e:
            return JsonResponse({'error': f'S3 error: {str(e)}'}, status=500)

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON in request body'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)



@csrf_exempt
@require_http_methods(["POST"])
@require_api_key
def list_s3_files(request):
    """List files in S3 bucket with directory navigation"""
    try:
        data = json.loads(request.body)
        bucket_name = data.get('bucket_name', 't-stg-sass-s3-bucket')
        prefix = data.get('prefix', '')  # Directory path within bucket
        
        AWS_REGION = 'ap-southeast-1'
        
        # Initialize S3 client
        s3_client = boto3.client('s3', region_name=AWS_REGION)
        
        # List objects with the given prefix
        response = s3_client.list_objects_v2(
            Bucket=bucket_name,
            Prefix=prefix,
            Delimiter='/'  # Use delimiter to simulate directories
        )
        
        # Process directories (CommonPrefixes)
        directories = []
        if 'CommonPrefixes' in response:
            for prefix_obj in response['CommonPrefixes']:
                dir_name = prefix_obj['Prefix']
                # Get just the directory name, not the full path
                if prefix:
                    dir_name = dir_name[len(prefix):]
                dir_name = dir_name.rstrip('/')
                directories.append({
                    'name': dir_name,
                    'type': 'directory',
                    'path': prefix_obj['Prefix']
                })
        
        # Process files
        files = []
        if 'Contents' in response:
            for obj in response['Contents']:
                # Skip the directory marker itself
                if obj['Key'] == prefix:
                    continue
                    
                # Get just the file name, not the full path
                file_name = obj['Key']
                if prefix:
                    file_name = file_name[len(prefix):]
                
                # Skip files that are in subdirectories
                if '/' in file_name:
                    continue
                
                # Generate hidden URL for download using API endpoint with expiration
                # Set expiration to 5 minutes from now
                expires_timestamp = int(time.time()) + 300

                # Generate signature
                signature = hmac.new(
                    settings.SECRET_KEY.encode(),
                    f"{obj['Key']}:{expires_timestamp}".encode(),
                    hashlib.sha256
                ).hexdigest()

                # Build URL with expiration parameters
                base_url = reverse('sass_app:serve_api_media', kwargs={'path': obj['Key']})
                download_url = request.build_absolute_uri(f"{base_url}?expires={expires_timestamp}&signature={signature}")
                
                # Use S3's ETag as the file ID (remove quotes)
                file_id = obj['ETag'].strip('"')

                files.append({
                    'id': file_id,
                    'name': file_name,
                    'type': 'file',
                    'size': round(obj['Size'] / (1024 * 1024), 2),
                    'last_modified': obj['LastModified'].isoformat(),
                    'path': obj['Key'],
                    'download_url': download_url
                })
        
        # Build breadcrumbs
        breadcrumbs = []
        if prefix:
            parts = prefix.rstrip('/').split('/')
            current_path = ""
            for part in parts:
                if part:
                    current_path += part + "/"
                    breadcrumbs.append({
                        'name': part,
                        'path': current_path
                    })
        
        return JsonResponse({
            'status': 'success',
            'current_path': prefix,
            'breadcrumbs': [{'name': 'root', 'path': ''}] + breadcrumbs,
            'contents': directories + files
        })

    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@csrf_exempt
@require_http_methods(["GET"])
@require_api_key
def serve_api_media(request, path):
    """Serve media files through API without login requirement - uses API key authentication with URL expiration"""
    try:
        logger = logging.getLogger(__name__)

        # Check URL expiration
        expires = request.GET.get('expires')
        signature = request.GET.get('signature')

        if not expires or not signature:
            logger.warning(f"Missing expiration parameters for path: {path}")
            return HttpResponse(status=403)

        try:
            expires_timestamp = int(expires)
            current_timestamp = int(time.time())

            if current_timestamp > expires_timestamp:
                logger.warning(f"Expired URL for path: {path}")
                return HttpResponse(status=410)  # Gone - URL expired

            # Verify signature
            expected_signature = hmac.new(
                settings.SECRET_KEY.encode(),
                f"{path}:{expires}".encode(),
                hashlib.sha256
            ).hexdigest()

            if not hmac.compare_digest(signature, expected_signature):
                logger.warning(f"Invalid signature for path: {path}")
                return HttpResponse(status=403)

        except (ValueError, TypeError):
            logger.warning(f"Invalid expiration format for path: {path}")
            return HttpResponse(status=403)

        # Sanitize path to prevent directory traversal
        path = os.path.normpath(path)
        if path.startswith('/') or '..' in path:
            logger.warning(f"Potential directory traversal attempt: {path}")
            return HttpResponse(status=403)

        # S3 File handling only
        try:
            s3_client = boto3.client(
                's3',
                region_name=settings.AWS_S3_REGION_NAME
            )

            # Get filename for response headers
            filename = os.path.basename(path)

            # Stream the file through Django
            try:
                response = s3_client.get_object(
                    Bucket=settings.AWS_STORAGE_BUCKET_NAME,
                    Key=path
                )

                # Create streaming response
                file_response = HttpResponse(
                    response['Body'].read(),
                    content_type=response.get('ContentType', 'application/octet-stream')
                )
                file_response['Content-Disposition'] = f'attachment; filename="{filename}"'
                file_response['Content-Length'] = response.get('ContentLength', 0)

                logger.info(f"Served S3 file via API: {path}")
                return file_response

            except ClientError as download_error:
                logger.error(f"Failed to download S3 file {path}: {str(download_error)}")
                return HttpResponse(status=404)

        except NoCredentialsError:
            logger.error("AWS credentials not configured properly")
            return HttpResponse(status=500)
        except ClientError as e:
            logger.error(f"S3 error for file {path}: {str(e)}")
            return HttpResponse(status=404)
        except Exception as e:
            logger.error(f"S3 error for file {path}: {str(e)}")
            return HttpResponse(status=500)

    except Exception as e:
        logger.error(f"Unexpected error serving API media file {path}: {str(e)}")
        return HttpResponse(status=500)


@csrf_exempt
@require_http_methods(["POST"])
@require_api_key
def run_dbbackup(request):
    """Run Django dbbackup via API endpoint with compression and schema filtering"""
    try:
        # Capture command output
        stdout = StringIO()
        stderr = StringIO()
        sys.stdout = stdout
        sys.stderr = stderr
        
        # Run the dbbackup command with specific options
        call_command('dbbackup', compress=True, schema=['saas_data'])
        
        # Reset stdout/stderr
        sys.stdout = sys.__stdout__
        sys.stderr = sys.__stderr__
        
        output = stdout.getvalue()
        errors = stderr.getvalue()
        
        if errors:
            return JsonResponse({
                'status': 'error',
                'message': errors
            }, status=500)
            
        return JsonResponse({
            'status': 'success',
            'message': 'Database backup completed successfully with compression for saas_data schema',
            'output': output
        })
        
    except Exception as e:
        # Reset stdout/stderr in case of exception
        sys.stdout = sys.__stdout__
        sys.stderr = sys.__stderr__
        
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

