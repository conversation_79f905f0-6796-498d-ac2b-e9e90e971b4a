# Generated by Django 5.1.4 on 2025-01-07 09:27

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("schedule_assessment", "0027_alter_scheduleassessment_current_year_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name="ruledefinition",
            name="applied_to_categories",
        ),
        migrations.RemoveField(
            model_name="ruledefinition",
            name="not_applied_to_categories",
        ),
        migrations.AddField(
            model_name="scheduleassessment",
            name="owned_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="owned_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="scheduleassessmenttest",
            name="owned_by",
            field=models.Foreign<PERSON>ey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="test_owned_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
