import boto3
import io
import tempfile
import os
from django.core.files.storage import Storage
from django.conf import settings
from botocore.exceptions import ClientError
import logging

logger = logging.getLogger(__name__)

class S3Storage(Storage):
    def __init__(self, **options):
        self.client = boto3.client('s3', region_name=settings.AWS_S3_REGION_NAME)
        self.bucket = settings.AWS_STORAGE_BUCKET_NAME
        self.location = options.get('location', '').strip('/')

    def _get_key(self, name):
        """Build the full key with location prefix if set."""
        return f"{self.location}/{name}".lstrip('/') if self.location else name

    def _save(self, name, content):
        key = self._get_key(name)

        try:
            self.client.put_object(
                Bucket=self.bucket,
                Key=key,
                Body=content,
                ContentType=getattr(content, 'content_type', 'application/octet-stream'),
                ServerSideEncryption="AES256"
            )
            logger.info(f"Saved backup to S3: {key}")
        except ClientError as e:
            logger.error(f"Failed to save {key} to S3: {e}")
            raise

        return key

    def _open(self, name, mode='rb'):
        """
        Returns a file-like object with full file operations support.
        For database restore operations, this will return a real file (NamedTemporaryFile).
        """
        key = self._get_key(name)

        try:
            response = self.client.get_object(Bucket=self.bucket, Key=key)
            data = response['Body'].read()
            
            # Create a temporary file that supports seek AND fileno
            temp_file = tempfile.NamedTemporaryFile(delete=False)
            temp_file.write(data)
            temp_file.flush()
            temp_file.close()
            
            # Open the file in the requested mode and return it
            # This file will support both seek() and fileno()
            return open(temp_file.name, mode)
        except ClientError as e:
            logger.error(f"Failed to open {key} from S3: {e}")
            raise

    def url(self, name):
        key = self._get_key(name)
        return f"https://{self.bucket}.s3.{settings.AWS_S3_REGION_NAME}.amazonaws.com/{key}"

    def exists(self, name):
        key = self._get_key(name)
        try:
            self.client.head_object(Bucket=self.bucket, Key=key)
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            logger.warning(f"Error checking existence of {key}: {e}")
            return False

    def delete(self, name):
        key = self._get_key(name)
        try:
            self.client.delete_object(Bucket=self.bucket, Key=key)
            logger.info(f"Deleted {key} from S3.")
        except ClientError as e:
            logger.error(f"Failed to delete {key} from S3: {e}")
            raise

    def listdir(self, path):
        prefix = self._get_key(path).rstrip('/') + '/'
        paginator = self.client.get_paginator('list_objects_v2')

        result_dirs = set()
        result_files = []

        for page in paginator.paginate(Bucket=self.bucket, Prefix=prefix):
            for obj in page.get('Contents', []):
                key = obj['Key']
                relpath = key[len(prefix):]
                if '/' in relpath:
                    result_dirs.add(relpath.split('/')[0])
                elif relpath:
                    result_files.append(relpath)

        return list(result_dirs), result_files

    def size(self, name):
        key = self._get_key(name)
        try:
            response = self.client.head_object(Bucket=self.bucket, Key=key)
            return response['ContentLength']
        except ClientError as e:
            logger.error(f"Failed to get size for {key}: {e}")
            raise
            
    def get_available_name(self, name, max_length=None):
        """
        Return the given name, as S3 can handle overwriting files.
        """
        return name
        
    def get_modified_time(self, name):
        """
        Return the last modified time for the file.
        """
        key = self._get_key(name)
        try:
            response = self.client.head_object(Bucket=self.bucket, Key=key)
            return response['LastModified']
        except ClientError as e:
            logger.error(f"Failed to get last modified time for {key}: {e}")
            raise