<tr>
    <td>
        Group {{ id }}
        <input type="hidden" name="timezone" />
    </td>
    <td>    
        <div class="wrapper-select form-edit-mode">
            <select name="edit-mode" title="edit-mode" class="form-control-edit-mode">
                <option value="on">On</option>
                <option value="interim">Interim</option>
                <option value="off">Off</option>
            </select>
        </div>
    </td>
    <td>   
        <div class="wrapper-calendar form-edit-mode">
            <input type="text" name="start-date" placeholder="start-date"  class="form-control-edit-mode datetimepicker" value="YYYY-MM-DD  00:00">
        </div>
    </td>
    <td>    
        <div class="wrapper-calendar form-edit-mode">
            <input type="text" name="end-date" placeholder="end-date"  class="form-control-edit-mode datetimepicker" value="YYYY-MM-DD  00:00">
        </div>
    </td>
    <td>    
        <div class="wrapper-calendar form-edit-mode">
            <input type="text" name="edit-mode-after" placeholder="edit-mode-after"  class="form-control-edit-mode datetimepicker" value="YYYY-MM-DD  00:00">
        </div>
    </td>
</tr>

<script>
    // $(".datetimepicker").each(function(){
    //     $(this).datetimepicker({
    //         allowTimes: true,
    //         format:'d.M.Y H:i',

    //     });
    // });
    

    
</script>