version: 0.2

env:
  variables:
    REPOSITORY_URI: "612100627180.dkr.ecr.ap-southeast-1.amazonaws.com/saas-dev"
    IMAGE_TAG: "latest"

phases:
  install:
    runtime-versions:
      python: 3.12
    commands:
      - echo Installing dependencies....
      - pip install -r requirements.txt
      - echo Logging in to Amazon ECR....
      - aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin 612100627180.dkr.ecr.ap-southeast-1.amazonaws.com

  pre_build:
    commands:
      - echo Pre-build phase...

  build:
    commands:
      - echo Build phase...
      - echo Creating .env file...
      - |
        cat <<EOL > .env
        DB_NAME=saas-db
        DB_USER=postgres
        DB_PASS=SYIwZcgxMcc2cFeoFa75
        DB_HOST=saas-db.cruqas4yidi5.ap-southeast-1.rds.amazonaws.com
        DB_PORT=5432
        SYSTEM_RUN_LOCATION=PROD
        AZURE_CLIENT_ID=c290f4ac-4f93-400e-8d95-a39f4f3514e8
        AZURE_CLIENT_SECRET=****************************************
        AZURE_TENANT_ID=243ebaed-00d0-4690-a7dc-75893b0d9f98
        EOL
      - echo Building the Docker image...
      - docker build -t $REPOSITORY_URI:$IMAGE_TAG --build-arg DB_NAME=$DB_NAME --build-arg DB_USER=$DB_USER --build-arg DB_PASS=$DB_PASS --build-arg DB_HOST=$DB_HOST --build-arg DB_PORT=$DB_PORT --build-arg SYSTEM_RUN_LOCATION=$SYSTEM_RUN_LOCATION --build-arg AZURE_CLIENT_ID=$AZURE_CLIENT_ID --build-arg AZURE_CLIENT_SECRET=$AZURE_CLIENT_SECRET --build-arg ******************************** .

      - echo Tagging the Docker image...
      - docker tag $REPOSITORY_URI:$IMAGE_TAG $REPOSITORY_URI:$IMAGE_TAG  

  post_build:
    commands:
      - echo Pushing the Docker image to ECR...
      - docker push $REPOSITORY_URI:$IMAGE_TAG  
      - echo '[{"name":"saasdev","imageUri":"612100627180.dkr.ecr.ap-southeast-1.amazonaws.com/saas-dev:latest"}]' > imagedefinitions.json
      - echo Build completed successfully!

artifacts:
  files:
    - '**/*'  
    - imagedefinitions.json
    - .env   
  discard-paths: yes
