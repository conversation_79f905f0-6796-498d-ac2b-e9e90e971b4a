# Generated by Django 5.1 on 2024-11-25 06:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("schedule_assessment", "0010_scheduleassessment_assessment_task"),
    ]
    
    def reset_digital_submission_and_over_2_week(apps, schema_editor):
        scheduleassessment = apps.get_model('schedule_assessment', 'ScheduleAssessment')
        for scheduleassessment in  scheduleassessment.objects.filter():
            if scheduleassessment.over_two_weeks == 'false':
                scheduleassessment.over_two_weeks = "no"
            else:
                scheduleassessment.over_two_weeks = "yes"

            if scheduleassessment.digital_submission == 'false':
                scheduleassessment.digital_submission = "no"
            else:
                scheduleassessment.digital_submission = "yes-in-the-same-week-as-digital-submission"
            
            scheduleassessment.save()

    operations = [
        migrations.AlterField(
            model_name="scheduleassessment",
            name="digital_submission",
            field=models.CharField(
                choices=[
                    ("no", "No"),
                    (
                        "Yes, in the same week as digital submission",
                        "yes-in-the-same-week-as-digital-submission",
                    ),
                    (
                        "Yes, in the following week after digital submission",
                        "yes-in-the-following-week-after-digital-submission",
                    ),
                ],
                default="no",
                max_length=255
            ),
        ),
        migrations.AlterField(
            model_name="scheduleassessment",
            name="over_two_weeks",
            field=models.CharField(
                choices=[("no", "No"), ("yes", "Yes")], default="no", max_length=10
            ),
        ),
        migrations.RunPython(reset_digital_submission_and_over_2_week)
    ]
