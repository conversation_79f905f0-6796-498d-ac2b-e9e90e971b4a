{% load static %}

<div class="d-flex flex-column flex-md-row align-items-md-center gap-4 justify-content-start menu-user-management">   
    <button type="button" class="btn-text v-center p-0 disabled" ><img class="icon-image" src="{% static 'img/icon-pencil.svg' %}" alt="bulk"> Edit files</button>
</div>
<div class="mt-4">
    <div class="alert alert-warning">
        Please upload LU List, Course LU Map, and CourseTypeLUTypeCalendar together. Failure to do so will result in data discrepancies in the master data
    </div>
</div>
<div class="mt-4 row">
    <div class="col-lg-10">
        <form id="file-upload-form" hx-encoding="multipart/form-data" hx-post="{% url 'system_management:upload_file' %}" hx-trigger="submit" hx-swap="outerHTML" hx-target=".content-system-management" hx-select=".content-system-management">
            {% csrf_token %}
            <div class="form-group mb-4">
                <label for="moderation-method">Moderation method</label>
                <div class="d-flex gap-3 justify-content-start align-items-center">
                    <input type="text" id="moderation-method" class="form-control disabled-upload" placeholder="Choose file..">
                    <div class="wrapper-button-upload flex-shrink-0">
                        <input type="file" accept=".xlsx" class="d-none" title="input-file" name="moderation-method">
                        <button class="btn-outline-primary choose-file" type="button">Choose File</button>
                    </div>
                </div>
                <a href="https://deeeplabs.sharepoint.com/:x:/s/ProjectDelivery-NYPAppModernisation/EUjP2oeM4uZMkKN38tnXsoYBc0ZjrAH-E-3o3y2T6tnnoQ?e=JLH3DU&wdOrigin=TEAMS-WEB.p2p_ns.rwc&wdExp=TEAMS-TREATMENT&wdhostclicktime=1733800795547&web=1" download target="_blank" class="mt-2 d-inline-block">Download template</a>
            </div>
            <div class="form-group mb-4">
                <label for="assessment">Assessment</label>
                <div class="d-flex gap-3 justify-content-start align-items-center">
                    <input type="text"  id="assessment" class="form-control disabled-upload" placeholder="Choose file..">
                    <div class="wrapper-button-upload flex-shrink-0">
                        <input type="file" accept=".xlsx" class="d-none" title="input-file" name="assessment">
                        <button class="btn-outline-primary choose-file" type="button">Choose File</button>
                    </div>
                </div>
                <a href="https://deeeplabs.sharepoint.com/:x:/s/ProjectDelivery-NYPAppModernisation/Edb6nB3iBPNNveAmxJ-Wr1kBtLLUOt_yDG7bj5SSejMOzg?e=hZF0KY&wdOrigin=TEAMS-WEB.p2p_ns.rwc&wdExp=TEAMS-TREATMENT&wdhostclicktime=1733800738943&web=1" download="download" target="_blank" class="mt-2 d-inline-block">Download template</a>
            </div>
            <div class="form-group mb-4">
                <label for="course">Course</label>
                <div class="d-flex gap-3 justify-content-start align-items-center">
                    <input type="text" id="course" class="form-control disabled-upload" placeholder="Choose file..">
                    <div class="wrapper-button-upload flex-shrink-0">
                        <input type="file" accept=".xlsx" class="d-none" title="input-file" name="course">
                        <button class="btn-outline-primary choose-file" type="button">Choose File</button>
                    </div>
                </div>
                <a href="https://deeeplabs.sharepoint.com/:x:/s/ProjectDelivery-NYPAppModernisation/EVJMIIaSGtBLomlESpOzR6IB-dW9GaMU-5pZxy9FcioW4w?e=j4f6Rd&wdOrigin=TEAMS-WEB.p2p_ns.rwc&wdExp=TEAMS-TREATMENT&wdhostclicktime=1733800837797&web=1" download="download" target="_blank" class="mt-2 d-inline-block">Download template</a>
            </div>
            <div class="form-group mb-4">
                <label for="department-master-file">Department</label>
                <div class="d-flex gap-3 justify-content-start align-items-center">
                    <input type="text" id="department-master-file" class="form-control disabled-upload" placeholder="Choose file..">
                    <div class="wrapper-button-upload flex-shrink-0">
                        <input type="file" accept=".xlsx" class="d-none" title="input-file" name="department">
                        <button class="btn-outline-primary choose-file" type="button">Choose File</button>
                    </div>
                </div>
                <a href="https://deeeplabs.sharepoint.com/:x:/s/ProjectDelivery-NYPAppModernisation/ERs-VNTmicVLnUjeXWh51OsBP120I8c6i9f_X1Z68FeZpQ?e=rSwgAt&wdOrigin=TEAMS-WEB.p2p_ns.rwc&wdExp=TEAMS-TREATMENT&wdhostclicktime=1733800598421&web=1" download target="_blank" class="mt-2 d-inline-block">Download template</a>
            </div>
            <div class="form-group mb-4">
                <label for="LUList">LUList</label>
                <div class="d-flex gap-3 justify-content-start align-items-center">
                    <input type="text" id="LUList" class="form-control disabled-upload" placeholder="Choose file..">
                    <div class="wrapper-button-upload flex-shrink-0">
                        <input type="file" accept=".xlsx" class="d-none" title="input-file" name="lulist">
                        <button class="btn-outline-primary choose-file" type="button">Choose File</button>
                    </div>
                </div>
                <a href="https://deeeplabs.sharepoint.com/:x:/s/ProjectDelivery-NYPAppModernisation/EZPcpWXZHVJPsNhm8601Iw8BvMMXPi474ua8Fd_mXnLWqw?e=PHZHTQ&wdOrigin=TEAMS-WEB.p2p_ns.rwc&wdExp=TEAMS-TREATMENT&wdhostclicktime=1733800662201&web=1" download target="_blank" class="mt-2 d-inline-block">Download template</a>
            </div>
            <div class="form-group mb-4">
                <label for="CourseLUMap">CourseLUMap</label>
                <div class="d-flex gap-3 justify-content-start align-items-center">
                    <input type="text" id="CourseLUMap" class="form-control disabled-upload" placeholder="Choose file..">
                    <div class="wrapper-button-upload flex-shrink-0">
                        <input type="file" accept=".xlsx" class="d-none" title="input-file" name="courselumap">
                        <button class="btn-outline-primary choose-file" type="button">Choose File</button>
                    </div>
                </div>
                <a href="https://deeeplabs.sharepoint.com/:x:/s/ProjectDelivery-NYPAppModernisation/ETTVU_1OIC9EjrDByenuhzcBsSwh6VhyPrYL_Lm_BedS2A?e=jqxbQD&wdOrigin=TEAMS-WEB.p2p_ns.rwc&wdExp=TEAMS-TREATMENT&wdhostclicktime=1733800883986&web=1" download="download" target="_blank" class="mt-2 d-inline-block">Download template</a>
            </div>
            <div class="form-group mb-4">
                <label for="CourseTypeLUTypeCalendar">CourseTypeLUTypeCalendar</label>
                <div class="d-flex gap-3 justify-content-start align-items-center">
                    <input type="text" id="CourseTypeLUTypeCalendar" class="form-control disabled-upload" placeholder="Choose file..">
                    <div class="wrapper-button-upload flex-shrink-0">
                        <input type="file" accept=".xlsx" class="d-none" title="input-file" name="coursetypelutypecalendar">
                        <button class="btn-outline-primary choose-file" type="button">Choose File</button>
                    </div>
                </div>
                <a href="https://deeeplabs.sharepoint.com/:x:/s/ProjectDelivery-NYPAppModernisation/EcdwOO7rk_5GmNw-rqfrGxwBpWHiOFx2nbQ2K-Q9nAp4-Q?e=PxFacR&wdOrigin=TEAMS-MAGLEV.p2p_ns.rwc&wdExp=TEAMS-TREATMENT&wdhostclicktime=1733801275464&web=1" download="download" target="_blank" class="mt-2 d-inline-block">Download template</a>
            </div>
        </form>
    </div>
</div>
{% block script %}
<script>
    jQuery(function($){
        $('.choose-file').click(function() {
            $(this).parent().find('input[type=file]').click();
            var filename = $('input[type=file]').val().replace(/C:\\fakepath\\/i, '')           
        })
        $('input[type="file"]').change(function(e){
            let fileName = e.target.files[0].name;
            $(this).parents('.form-group').find('input[type="text"]').val(fileName)
        });
    })
</script>    
{% endblock script %}