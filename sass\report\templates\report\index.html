{% extends 'base.html' %}
{% block title %}Report{% endblock title %}
{% load static %}
{% block content %}

<div class="row ">
   <div class="col-12">
    <div class="breadcrumb">
        <a href="/">Back</a> <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M7.07586 4.51043C7.18836 4.39807 7.34086 4.33496 7.49986 4.33496C7.65886 4.33496 7.81136 4.39807 7.92386 4.51043L11.3239 7.91043C11.4362 8.02293 11.4993 8.17543 11.4993 8.33443C11.4993 8.49343 11.4362 8.64593 11.3239 8.75843L7.92386 12.1584C7.81012 12.2644 7.65969 12.3221 7.50424 12.3194C7.3488 12.3166 7.2005 12.2537 7.09057 12.1437C6.98064 12.0338 6.91766 11.8855 6.91492 11.7301C6.91218 11.5746 6.96988 11.4242 7.07586 11.3104L10.0519 8.33443L7.07586 5.35843C6.9635 5.24593 6.90039 5.09343 6.90039 4.93443C6.90039 4.77543 6.9635 4.62293 7.07586 4.51043Z" fill="#BBBBBB"/>
            </svg>
        <span>Report</span>
    </div> 
    <div class="card card-shadow card-padding">
        <h2 class="title-section">Report</h2>
        <div class="custom-accordion accordion-report mt-4">
            <!-- Accordion Item -->
            <div class="custom-accordion-item show">
                <div class="custom-accordion-header">
                    <h2>Learning Unit Assessment Strategy</h2>
                    <img src="{% static 'img/chevron-down.svg' %}" alt="chevron">
                </div>
                <div class="custom-accordion-body">
                    <form action="{% url 'report:download_learning_unit_assement_strategy' %}" method="post">
                        {% csrf_token %}
                        <p>ACAD Year {{SYSTEM_CURRENT_YEAR.current_year}} - Semester {{SYSTEM_CURRENT_YEAR.current_semester}}</p>
                        <div class="row my-2">
                            <div class="col-md-6 col-lg-3">
                                <div class="form-group">
                                    <label for="select-lU">Select LU</label>
                                    <div class="wrapper-select">
                                        <select name="lu-code" id="lu" class="form-control select-lu-report" title="lu">
                                            {%for lu in learning_units %}
                                                <option value="{{ lu.code }}">{{ lu.code }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-outline-primary" onclick="submitManual(this)" data-filename="Assm Strategy">Download</button>
                    </form>
                </div>
            </div>
            <!-- Accordion Item -->
            <div class="custom-accordion-item ">
                <div class="custom-accordion-header">
                    <h2>Learning Unit Reports</h2>
                    <img src="{% static 'img/chevron-down.svg' %}" alt="chevron">
                </div>
                <div class="custom-accordion-body">
                    <form action="{% url 'report:download_learning_unit_report' %}" method="post">
                        {% csrf_token %}
                    <div class="row ">
                        <div class="col-md-6 col-lg-3">
                            <div class="form-group">
                                <label for="de">Department</label>
                                <div class="wrapper-select">
                                    <select name="department" id="department" class="form-control" title="department">
                                        <option>All Department</option>
                                        {% for department in departments %}
                                        <option value="{{department.code}}">{{department.name}}</option>
                                        {% endfor %}

                                    </select>
                                </div>
                            </div>                                
                        </div>
                    </div>
                    <p>Download type</p>
                    
                    <div class="form-group mb-0">
                        <label class="container-radio">Learning unit without ICA in term 1
                            <input type="radio" name="download-type" value="learning-unit-without-ica-in-term-1"  checked="checked">
                            <span class="checkmark"></span>
                        </label>
                    </div>
                    <div class="form-group">
                        <label class="container-radio">Learning unit without ICA
                            <input type="radio" name="download-type" value="learning-unit-without-ica">
                            <span class="checkmark"></span>
                        </label>
                    </div>
                    <button type="button" onclick="submitManual(this)" class="btn btn-outline-primary">Download</button>
                    </form>  
                </div>
            </div>
            <!-- Accordion Item -->
            <div class="custom-accordion-item ">
                <div class="custom-accordion-header">
                    <h2>Learning Unit Assessment Detail</h2>
                    <img src="{% static 'img/chevron-down.svg' %}" alt="chevron">
                </div>
                <div class="custom-accordion-body">
                    <form action="{% url 'report:download_learning_unit_assement_details' %}" method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="current-year">Current Year</label>
                                            <div class="wrapper-calendar">
                                                <select name="current-year" id="current-year-report" data-current-year="{{ SYSTEM_CURRENT_YEAR.current_year }}" class="form-control" title="current-year">
                                                    <option value="">Current year</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="current-year">Current Semester</label>
                                            <div class="wrapper-select">
                                                <select name="current-semester" id="current-semester" class="form-control" title="current-semester">
                                                    <option value="1" {% if SYSTEM_CURRENT_YEAR.current_semester == 1 %}selected{% endif %}>1</option>
                                                    <option value="2" {% if SYSTEM_CURRENT_YEAR.current_semester == 2 %}selected{% endif %}>2</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col">
                                        <button type="button" class="btn btn-outline-primary" onclick="submitManual(this)" data-filename="Assm Detail">Download</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <!-- Accordion Item -->
            <div class="custom-accordion-item ">
                <div class="custom-accordion-header">
                    <h2>Learning Unit Assessment Schedule</h2>
                    <img src="{% static 'img/chevron-down.svg' %}" alt="chevron">
                </div>
                <div class="custom-accordion-body">
                    <form action="{% url 'report:download_learning_unit_assement_schedule' %}" method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="Course">Course</label>
                                            <div class="wrapper-select">
                                                <select name="course" id="Course-report" class="form-control select-lu-report" title="Course">
                                                    <option value="all">All Courses</option>
                                                    {% for course in courses  %}
                                                    <option value="{{ course.code }}">{{ course.code }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="year-study">Year of study</label>
                                            <select name="year-study" id="year-of-study" class="form-control select-lu-report" title="year-study">
                                                {% for year_study in year_studies %}
                                                <option value="{{year_study}}">{{year_study|upper}}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" onclick="submitManual(this)" data-filename="Assm Schedule" class="btn btn-outline-primary">Download</button>
                    </form>
                </div>
            </div>
            
        </div>
    </div>
   </div>
</div>

<!-- popup file download / email --> 
 <div class="popup popup-download d-none">
    <div class="backdrop"></div>
    <div class="content-popup">        
        <img src="{% static 'img/sassy-happy.png' %}" alt="sassy happy">         
        <p class="message-download-report">File downloaded successfully</p>
        <div class="d-flex align-items-center gap-3 mt-4 justify-content-center">            
            <button type="button" class="btn btn-primary copy-from-last close-popup">Okay</button>
        </div>
    </div>
</div>  
{% endblock content %}

{% block script %}
<script>
    
    function submitManual(btn) {
        const $btn = $(btn);
        const originalText = $btn.html();
        $btn.prop('disabled', true).html('Processing...');
        
        const $form = $btn.closest('form');
        const csrftoken = $form.find('[name=csrfmiddlewaretoken]').val();
        let lu_code = $('select[name="lu-code"]').val();
        
        let department_code = $('select[name="department"]').val();
        let current_semester = $('select[name="current-semester"]').val();
        let current_year = $('select[name="current-year"]').val();
        let downloadType = $('input[name="download-type"]:checked').val();
        let course = $('select[name="course"]').val();
        let year_study = $('select[name="year-study"]').val();
        let filename;
        
        if ($btn.data('filename')) {
            filename = $btn.data('filename');
        } else {
            if (downloadType == "learning-unit-without-ica-in-term-1") {
                filename = "LU No Term 1 ICA";
            } else if (downloadType == "learning-unit-without-ica") {
                filename = "LU No ICA";
            }
        }
        
        // Create form data
        const formData = new FormData();
        formData.append('lu-code', lu_code);
        formData.append('download-type', downloadType);
        formData.append('department', department_code);
        formData.append('current-semester', current_semester);
        formData.append('current-year', current_year);
        formData.append('course', course);
        formData.append('year-study', year_study);

        fetch($form.attr('action'), {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrftoken,
            },
            body: formData
        })
        .then(response => {            
            if (!response.ok) {
                return response.text().then(text => {
                    throw new Error(text);
                });
            }
            
            // Get the filename from the Content-Disposition header
            var contentDisposition = response.headers.get('Content-Disposition');
            if (contentDisposition) {
                // Extract the filename from the header 
                const filenameMatch = contentDisposition.match(/filename=["']?([^"'.]+)["']?/);
                if (filenameMatch && filenameMatch.length > 1) {
                    filename = filenameMatch[1];
                }
            }

            // Handle direct download vs email sending based on redirect status
            if (!response.redirected) {
                $('.message-download-report').text('File downloaded successfully');
                return response.blob();
            } else {
                $('.popup-download').removeClass('d-none');
                $('.message-download-report').text('File sent to email successfully');
                return null;
            }
        })
        .then(blob => {
            if (blob) {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${filename}.xlsx`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                a.remove();
            }
        })
        .catch(error => {
            console.error('Download failed:', error);
            alert('Download failed. Please try again.');
        })
        .finally(() => {
            $btn.prop('disabled', false).html(originalText);
            
        });
    }
    $(document).ready(function(){
        $(".select-lu-report").select2({
            placeholder: "Select LU Code",
            width: "100%",
        });
        $('input[name="lu-code"]').val($('.select-lu-report').val())
        $('.select-lu-report').on('change', function(e) {
            $('input[name="lu-code"]').val($(this).val())
        });
        // Accordion
        $('.custom-accordion-header').click(function(){
            $('.custom-accordion-body').slideUp();
            $('.custom-accordion-body').parent().removeClass('show')
            $(this).parent().find('.custom-accordion-body').addClass('show').slideDown()
           $(this).parent().addClass('show')
        })        
    })
</script>
{% endblock script %}