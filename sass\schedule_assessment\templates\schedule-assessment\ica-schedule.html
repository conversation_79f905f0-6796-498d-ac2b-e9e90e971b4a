{% extends 'base.html' %}
{% load static %}
{% load custom_tags %}
{% block title %}
{% if test_mode %} PREVIEW {% endif %} Schedule Assessment
{% endblock title %}

{% block content %}
    <div class="row">
        <div class="col-xl-10 offset-xl-1">
            <div class="breadcrumb">
                <a href="/">Back</a> <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M7.07586 4.51043C7.18836 4.39807 7.34086 4.33496 7.49986 4.33496C7.65886 4.33496 7.81136 4.39807 7.92386 4.51043L11.3239 7.91043C11.4362 8.02293 11.4993 8.17543 11.4993 8.33443C11.4993 8.49343 11.4362 8.64593 11.3239 8.75843L7.92386 12.1584C7.81012 12.2644 7.65969 12.3221 7.50424 12.3194C7.3488 12.3166 7.2005 12.2537 7.09057 12.1437C6.98064 12.0338 6.91766 11.8855 6.91492 11.7301C6.91218 11.5746 6.96988 11.4242 7.07586 11.3104L10.0519 8.33443L7.07586 5.35843C6.9635 5.24593 6.90039 5.09343 6.90039 4.93443C6.90039 4.77543 6.9635 4.62293 7.07586 4.51043Z" fill="#BBBBBB"/>
                    </svg>
                <span>{% if test_mode %} PREVIEW {% endif %}Schedule assessment</span>
            </div>  
            
            <div class="card card-shadow card-padding">
                <h2 class="title-section {% if test_mode %}title-section-preview{% endif %}">{% if test_mode %} PREVIEW {% endif %} Schedule Assessment</h2>
                <h3 class="status-assessment">Status: <span class="text-danger">
                    {% if status_learning_unit.status %}
                            
                            {{status_learning_unit.get_status_display|title}}
                        
                    {% else %}
                    Pending
                    {% endif %}
                    </span> <span class="fw-normal text-secondary fs-6">| {{ status_learning_unit.updated_at|date:"d-M-Y H:i" }}</span>
                </h3>

                {% if learning_unit.message %}
                <div role="alert" class="fade d-flex align-items-center alert alert-dismissible alert-warning show sgds">
                    <button type="button" data-bs-dismiss="alert" class="btn-close btn-sm" aria-label="Close alert"></button>
                    <div class="d-flex align-items-center justify-content-start gap-2">
                        <img src="{% static 'img/icon-warning.svg' %}" alt="Warning">
                        {{learning_unit.message}}
                    </div>
                </div>
                {% endif %}
                
                 
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="academic-year">Academic Year</label>
                            <input type="text" name="academic_year" id="academic-year" class="form-control disabled" placeholder="Academic Year"
                            value="{{system_current_year.current_year}}"
                            >
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="current-semester">Semester</label>
                            <input type="text" name="current-semester" id="current-semester" class="form-control disabled" placeholder="Current semester" 
                            value="{{system_current_year.current_semester}}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            
                            <div class="form-group">
                                <label for="year">Year of Study</label>
                                <input type="text" name="year" class="form-control disabled" placeholder="Current year of study"
                                value="{{ year_studies|join:", " }}"
                                >
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="lu-code">LU Code</label>
                            <input type="text" name="lu-code" id="lu-code" class="form-control disabled" placeholder="LU code"
                            value="{{learning_unit.code}}"
                            >
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="lu-name">LU Name </label>
                            <input type="text" name="lu-name" id="lu-name" class="disabled form-control" placeholder="LU Name "
                            value="{{learning_unit.name}}"
                            >
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="examinable-lu">Examinable LU</label>
                            <input type="text" name="examinable-lu" id="examinable-lu" class="form-control disabled" placeholder="Examinable LU" 
                                {% if learning_unit.is_examinable %}value={{learning_unit.get_is_examinable_display}} {% endif %}
                                >
                        </div>
                    </div>
                </div>
                    
                {% if not test_mode %} 
                <div class="row mt-2">
                    <div class="col">
                        <div class="form-group d-inline-block ">
                            
                            <input type="hidden" name="lu_id" value="{{ learning_unit.id }}">
                            <label class="wrapper-checkbox wrapper-checkbox-copy-from-last {% if test_mode %}wrapper-checkbox-test{% endif %} {% if not is_edit or not permission.update %}disabled{% endif %}"> Copy from last Approved Assessment Strategy
                                <input 
                                    id="copy-from-last"
                                    hx-post="{% url 'schedule_assessment:copy_from_last' learning_unit.id %}" 

                                    hx-swap="innerHTML" 
                                    hx-target="#copy-from-last-popup-content"
                                    name="validation"
                                    
                                    type="checkbox" 
                                    hx-trigger="click" 
                                    hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
                                >
                                </input>
                                
                                <span class="checkmark"></span>
                            </label>
                        </div>

                        <!-- Popup copy from last -->
                        <div class="popup popup-copy-from-last d-none">
                            <div class="backdrop"></div>
                            <div class="content-popup content-popup-upload">        
                                <img src="{% static 'img/sassy-happy.png' %}" alt="sassy happy">  
                                
                                <div id="copy-from-last-popup-content">
                                </div>

                            </div>
                        </div>     
                    </div>
                </div>
                {% endif %}
                
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="form-group">
                            
                            <label for="Total-weightage">Total weightage</label>
                            <input title="total-weightage" type="text" name="total-weightage" id="total-weightage" class="form-control disabled {% if test_mode %}error-test{% else %}error{% endif %}" 
                            {% get_total_weightage learning_unit as total_weightage %}
                            {% if total_weightage %}
                            value={{total_weightage}}%
                            {% else %}
                            value="0%"
                            {% endif %}
                            >
                        </div>
                    </div>
                </div>        
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-10 offset-xl-1" >   
             
            {%if redirect_to == 'ica-detail' %}
            {% if status_learning_unit.status and save_detail == "true" %}
            <div class="popup popup-wide">
                <div class="backdrop"></div>
                    <div class="content-popup content-popup-upload">
                        {%  if status_learning_unit.get_status_display|lower == "submitted"%}
                            <h2>Success!</h2>                             
                            <img src="{% static 'img/sassy-happy.png' %}" alt="sassy happy">
                            <p>Your submission was successful!</p>
                        {% else %}
                            <h2>Error!</h2>                             
                            <img src="{% static 'img/sassy-sad.png' %}" alt="sassy sad">
                            <p class="text-danger">Something went wrong, please check again.</p>
                        {% endif %}
                        <button class="btn btn-primary close-popup mt-4">Okay!</button>
                    </div>
                </div> 
            {% endif %}
            
            <div class="content-card-ica" hx-push-url="{% url 'schedule_assessment:icaSchedule' learning_unit.id %}" data-testmode="{{ test_mode }}" data-count="1" data-id="{{ learning_unit.id }}"data-credit="{{ credits }}" hx-get="{% url 'schedule_assessment:partial_ica_detail' learning_unit.id %}" hx-target="this" {% if test_mode %}hx-vals='{"test_mode": true, "credits": {{ credits }} }'{% endif %} hx-swap="innerHTML" hx-trigger="load, detail-changed"></div>
            {% else  %}   
                  
            <div class="content-card-ica" data-testmode="{{ test_mode }}" data-count="1" data-id="{{ learning_unit.id }}" data-credit="{{ credits }}" hx-get="{% url 'schedule_assessment:partial_ica_schedule' learning_unit.id %}?is_from_submit={{ is_from_submit }}" hx-target="this" {% if test_mode %}hx-vals='{"test_mode": true, "credits": {{ credits }} }'{% endif %} hx-swap="innerHTML" hx-trigger="load, ica-changed"></div>
            {% endif  %}
        </div>
    </div>
       
    <c-popup title="Time's Up!" class="popup-timeout popup-wide">        
        <img src="{% static 'img/sassy-sad.png' %}" alt="sassy-sad" class="icon-sassy-popup">
        <p class="mb-0">The week will be reset on your next session</p>
        <div class="d-flex align-items-center gap-3 mt-4 justify-content-center">
            <button type="button" class="btn btn-primary close-time-out close-popup">Okay</button>
        </div>
    </c-popup>

    <!-- popup success -->
    <c-popup title="Success!" class="popup-success popup-wide">        
        <img src="{% static 'img/sassy-happy.png' %}" alt="sassy-happy" class="icon-sassy-popup">
        <p class="mb-0">Your submission was successful!</p>
        <div class="d-flex align-items-center gap-3 mt-4 justify-content-center">
            <button type="button" class="btn btn-primary close-time-out close-popup">Okay</button>
        </div>
    </c-popup>
    
    
     
    <!-- Popup ICA Schedule submission -->


        
        
    
{% endblock %}
{% block script %}
<script>    
    var currentYear = new Date().getFullYear();            
    setTimeout(() => {
        $(".content-card-ica").attr("data-count", $(".item-ica-card").length + 1);        
    }, 300);
</script>    
{% endblock script %}