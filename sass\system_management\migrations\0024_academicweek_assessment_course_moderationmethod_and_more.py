# Generated by Django 5.1 on 2024-10-23 08:18

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("system_management", "0023_alter_systemeditmode_mode"),
    ]

    operations = [
        migrations.CreateModel(
            name="AcademicWeek",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("CourseType", models.CharField(max_length=128)),
                ("LuCategory", models.CharField(max_length=128)),
                ("CourseTypeLuCategory", models.CharField(max_length=128)),
                ("MaxIcaCountPerWeek", models.IntegerField(blank=True, null=True)),
                ("LastIntructionWeek", models.IntegerField(blank=True, null=True)),
                ("StartDate", models.DateField(blank=True, null=True)),
                ("Term1End", models.DateField(blank=True, null=True)),
                ("weeks", models.J<PERSON><PERSON>ield(blank=True, null=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="Assessment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("method", models.CharField(max_length=255)),
                ("task", models.CharField(max_length=255)),
                ("duration", models.CharField(max_length=255)),
                ("notes", models.CharField(max_length=255)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="Course",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("code", models.CharField(max_length=128, unique=True)),
                ("name", models.CharField(max_length=255)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="ModerationMethod",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("description", models.CharField(max_length=255)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="CourseLuMap",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("academic_year", models.IntegerField(blank=True, null=True)),
                ("semester", models.IntegerField(blank=True, null=True)),
                ("year_study", models.IntegerField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "course_code",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="system_management.course",
                    ),
                ),
                (
                    "lu_code",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="system_management.learningunit",
                    ),
                ),
            ],
        ),
    ]
