# Generated by Django 5.1 on 2024-12-30 05:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("sass_app", "0008_tooltip_name_tooltip_slug_alter_tooltip_content"),
    ]

    operations = [
        migrations.CreateModel(
            name="ErrorLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("error_type", models.CharField(max_length=255)),
                ("error_message", models.TextField()),
                ("stack_trace", models.TextField()),
                ("path", models.Char<PERSON><PERSON>(max_length=255)),
                ("method", models.Char<PERSON>ield(max_length=10)),
                ("user_id", models.IntegerField(blank=True, null=True)),
                ("request_data", models.<PERSON><PERSON><PERSON><PERSON>(blank=True, null=True)),
            ],
            options={
                "ordering": ["-timestamp"],
            },
        ),
    ]
