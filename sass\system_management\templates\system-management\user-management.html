{% load static %}
{% load mathfilters %}
<div class="row">
    <div class="col-12">
        <div class="breadcrumb">
            <a href="/">Back</a> <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M7.07586 4.51043C7.18836 4.39807 7.34086 4.33496 7.49986 4.33496C7.65886 4.33496 7.81136 4.39807 7.92386 4.51043L11.3239 7.91043C11.4362 8.02293 11.4993 8.17543 11.4993 8.33443C11.4993 8.49343 11.4362 8.64593 11.3239 8.75843L7.92386 12.1584C7.81012 12.2644 7.65969 12.3221 7.50424 12.3194C7.3488 12.3166 7.2005 12.2537 7.09057 12.1437C6.98064 12.0338 6.91766 11.8855 6.91492 11.7301C6.91218 11.5746 6.96988 11.4242 7.07586 11.3104L10.0519 8.33443L7.07586 5.35843C6.9635 5.24593 6.90039 5.09343 6.90039 4.93443C6.90039 4.77543 6.9635 4.62293 7.07586 4.51043Z" fill="#BBBBBB"/>
                </svg>
            <span>User Management</span>

        </div> 
        <div class="content-system-management">
            <div id="user_management" class="card card-shadow card-padding">
                <h2 class="title-section">System Management</h2>
                <c-menu_system_management current_page="userManagement"></c-menu_system_management>
                <h2 class="title-card text-black mb-4">User Management</h2>
                
                <div class="d-flex flex-column flex-md-row align-items-md-center gap-4 justify-content-start menu-user-management">
                    {% if permission.create %}
                    <button type="button" class="btn-text v-center p-0 show-popup">
                        <img class="icon-image" src="{% static 'img/icon-plus.svg' %}" alt="plus"> <span>Add new user</span>
                    </button>
                    {% endif %}
                    <c-popup class="popup-wide" buttonleft >
                        <div class="d-flex mb-4 justify-content-between align-items-center">
                            <h2 class="mb-0">Add New User</h2> 
                            <button class="btn-text close-popup" type="button"><img src="{% static 'img/icon-close.svg' %}" alt="close"></button>
                        </div>
                        <form hx-post="{% url 'system_management:create_user' %}" hx-trigger="submit" hx-swap="innerHTML" hx-target=".container-content" class="form-validate form-add-new-user" novalidate>
                            {% csrf_token %}
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="form-group text-start">
                                        <label for="email">User email</label>
                                        <input type="email" name="email" id="email" placeholder="email" class="form-control">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group text-start">
                                        <label for="Role" >Role</label>
                                        <div class="wrapper-select">
                                            <select required name="role" id="role" class="form-control" title="role">
                                                <option value="">Choose role</option>
                                                <option value="admin">Admin</option>
                                                <option value="user">User</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group text-start">
                                        <label for="Status">Status</label>
                                        <div class="wrapper-select">
                                            <select required name="status" id="status" class="form-control" title="status">
                                                <option value="">Choose status</option>
                                                <option value="active">Active</option>
                                                <option value="suspended">Suspended</option>
                                                <option value="inactive">Inactive</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                            <div class="d-flex">
                                <button type="submit" class="btn btn-primary  btn-submit-user disabled">Add new user</button>
                                <button type="button" class="btn-text text-danger close-popup">Cancel </button>
                            </div>
                        </form>
                    </c-popup>
                    <form action="{% url 'system_management:export_user_table' %}" method="POST">
                        {% csrf_token %}
                        <input type="hidden" name="search_query" id="export-search-query">
                        <button type="submit" class="btn-text v-center p-0">
                            <img class="icon-image" src="{% static 'img/icon-export.svg' %}" alt="export"> Export staff list
                        </button>
                    </form>
                    {% if permission.create %}

                    <form id="importBulkUserForm" action="{% url 'system_management:importBulkUser' %}" enctype="multipart/form-data" method="POST">

                        {% csrf_token %}
                        <div class="wrapper-upload__bulk-user">
                            <input
                                    hx-post="{% url 'system_management:upload_file' %}"

                                    hx-trigger="change"
                                    hx-encoding="multipart/form-data"

                                    hx-target=".validation-content"
                                    hx-swap="innerHTML"
                                    hx-vals='{"validation":"YES"}'

                                    id="upload-bulk-user"
                                    name="upload-bulk-user"
                                    type="file"
                                    accept=".xlsx,.xls"

                                >  </input>

                            <button type="button" class="btn-text v-center p-0 btn-upload-bulk-user">
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M8.33333 1.66667V5C8.33333 5.44203 8.15774 5.86595 7.84518 6.17851C7.53261 6.49107 7.10869 6.66667 6.66666 6.66667H3.33333M16.6667 5.83333V3.33333C16.6667 2.89131 16.4911 2.46738 16.1785 2.15482C15.8659 1.84226 15.442 1.66667 15 1.66667" stroke="#787676" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M16.6142 17.4992C16.451 17.7883 16.2051 18.022 15.9082 18.1705C15.6112 18.319 15.2767 18.3754 14.9475 18.3325L5 18.3333C4.55797 18.3333 4.13405 18.1577 3.82149 17.8452C3.50893 17.5326 3.33333 17.1087 3.33333 16.6667V5.83333L7.5 1.66667H15M15.8333 9.16667L18.3333 11.6667" stroke="#787676" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M15.8333 14.1667L18.3333 11.6667H10" stroke="#787676" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                     <span>Import staff list</span>
                            </button>  
                                        
                        </div>
                    </form>
                      
                    
                    {% endif %}
                    {% if permission.update %}
                    <button type="button" class="btn-text v-center p-0 show-popup disabled btn-bulk-action" data-popup="bulk-action"><img class="icon-image" src="{% static 'img/separate-dots.svg' %}" alt="bulk"> Bulk action</button>
                    {% endif %}
                </div>
                
                <div class="mt-4">
                    <div class="form-group">
                        <label for="search-user">Search user</label>
                        <input id="search-user" oninput="updateExportSearch(this.value)"
                                name="search-user" type="text"  class="form-control" placeholder="Search user ID or name" 
                                hx-get="{% url 'system_management:userManagement' %}" 
                                hx-trigger="keyup changed delay:0.5s" 
                                hx-swap="outerHTML" 
                                hx-target=".content-user-management" 
                                hx-select=".content-user-management" 
                                hx-indicator=".loading-user-management">
                    </div>
                    <script>
                        function updateExportSearch(value) {
                            // Update hidden input with search value
                            document.getElementById('export-search-query').value = value;
                        }
                    </script>
                </div>
                
                <div class="wrapper-content-user-management p-relative">
                    <div class="content-user-management ">
                        <div class="table-responsive">
                            <table class="table-sass table table-borderless">
                                <thead class="bg-gray text-black">
                                    <tr>
                                        <th>
                                            <label class="wrapper-checkbox">
                                                <input type="checkbox" title="checked all" id="checked-all">
                                                <span class="checkmark"></span>
                                            </label>
                                        </th>
                                        <th hx-get="{% url 'system_management:userManagement' %}?{% if search_user %}&search-user={{ search_user }}{% endif %}&sort={% if sort == 'asc' or sort == None %}desc{% else %}asc{% endif %}&sort-col=email" hx-target=".content-user-management" hx-select=".content-user-management" hx-swap="outerHTML" hx-indicator=".loading-user-management">
                                            <div class="wrapper-th-sort">
                                                <span>Email</span> 
                                                <div class="wrapper-caret-sort"> <span class="top-caret {% if sort == "desc" and sort_col == "email" %}disabled{% endif %}"><i class="bi bi-caret-down-fill"></i></span><span class="buttom-caret {% if sort == "asc" and sort_col == "email" %}disabled{% endif %}"><i class="bi bi-caret-up-fill"></i></span> </div>
                                            </div>
                                        </th>
                                        <th hx-get="{% url 'system_management:userManagement' %}?{% if search_user %}&search-user={{ search_user }}{% endif %}&sort={% if sort == 'asc' or sort == None %}desc{% else %}asc{% endif %}&sort-col=first_name" hx-target=".content-user-management" hx-select=".content-user-management" hx-swap="outerHTML" hx-indicator=".loading-user-management">
                                            <div class="wrapper-th-sort">
                                                <span>NAME</span> 
                                                <div class="wrapper-caret-sort"> <span class="top-caret {% if sort == "desc" and sort_col == "first_name" %}disabled{% endif %}"><i class="bi bi-caret-down-fill"></i></span><span class="buttom-caret {% if sort == "asc" and sort_col == "first_name" %}disabled{% endif %}"><i class="bi bi-caret-up-fill"></i></span> </div>
                                            </div>
                                        </th>
                                        <th hx-get="{% url 'system_management:userManagement' %}?{% if search_user %}&search-user={{ search_user }}{% endif %}&sort={% if sort == 'asc' or sort == None %}desc{% else %}asc{% endif %}&sort-col=role_type" hx-target=".content-user-management" hx-select=".content-user-management" hx-swap="outerHTML" hx-indicator=".loading-user-management">
                                            <div class="wrapper-th-sort">
                                                <span>ROLE</span> 
                                                <div class="wrapper-caret-sort"> <span class="top-caret {% if sort == "desc" and sort_col == "role_type" %}disabled{% endif %}"><i class="bi bi-caret-down-fill"></i></span><span class="buttom-caret {% if sort == "asc" and sort_col == "role_type" %}disabled{% endif %}"><i class="bi bi-caret-up-fill"></i></span> </div>
                                            </div>
                                        </th>
                                        
                                        <th hx-get="{% url 'system_management:userManagement' %}?{% if search_user %}&search-user={{ search_user }}{% endif %}&sort={% if sort == 'asc' or sort == None %}desc{% else %}asc{% endif %}&sort-col=role_status" hx-target=".content-user-management" hx-select=".content-user-management" hx-swap="outerHTML" hx-indicator=".loading-user-management">
                                            <div class="wrapper-th-sort">
                                                <span>STATUS</span> 
                                                <div class="wrapper-caret-sort"> <span class="top-caret {% if sort == "desc" and sort_col == "role_status" %}disabled{% endif %}"><i class="bi bi-caret-down-fill"></i></span><span class="buttom-caret {% if sort == "asc" and sort_col == "role_status" %}disabled{% endif %}"><i class="bi bi-caret-up-fill"></i></span> </div>
                                            </div>
                                        </th>
                                        <th>ACTION </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if not registered_users %}
                                        <tr>
                                            <td colspan="6" class="py-4 text-center">No user found.</td>
                                        </tr>
                                    {% else %}
                                        {% for registered_user in registered_users %}
                                            <tr>
                                                <td> 
                                                    <label class="wrapper-checkbox">
                                                        <input type="checkbox" title="checkbox user" class="checkbox-tr">
                                                        <span class="checkmark"></span>
                                                    </label>
                                                </td>
                                                <td class="d-none value-id">{{registered_user.id}}</td>
                                                <td>{{registered_user.email}}</td>
                                                <td class="field-value">{{registered_user.first_name}}</td>
                                                <td>{{registered_user.role.get_type_display }} </td>
                                                <td>{{registered_user.role.get_status_display }} </td>
                                                <td>
                                                    <button {% if not permission.update %} disabled {% endif %} type="button" hx-get="{% url 'system_management:get_edit_user' id=registered_user.id %}" hx-trigger="click" hx-swap="outerHTML" hx-select="#edit-user" hx-target="#edit-user"  class="btn-text p-0 show-popup" data-popup="edit-user"><img src="{% static 'img/icon-pencil.svg' %}" alt="pencil"> Edit user</button>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    {% endif %}
                                    
                                </tbody>
                            </table>
                            <div class="d-flex justify-content-center">
                                {% if page_content.paginator.num_pages > 1 %}
                                    {% with total_pages=page_content.paginator.num_pages %}
                                        <div class="pagination mt-3">
                                            {% for num in page_content.paginator.page_range %}
                                                {% if num == 1 or num == total_pages %}
                                                    <a hx-get="{% url 'system_management:userManagement' %}?page={{num}}{% if search_user %}&search-user={{ search_user }}{% endif %}&sort={% if sort == None %}asc{% else %}{{ sort }}{% endif %}&sort-col={% if sort_col == None %}code{% else %}{{ sort_col }}{% endif %}" class="page-link cursor-pointer {% if num == page_content.number %} active {% endif %}" hx-trigger="click" hx-swap="outerHTML" hx-target=".content-user-management" hx-select=".content-user-management" hx-indicator=".loading-user-management">{{ num }}</a>
                                                {% elif num|sub:page_content.number >= -1 and num|sub:page_content.number <= 1  %}
                                                    <a hx-get="{% url 'system_management:userManagement' %}?page={{num}}{% if search_user %}&search-user={{ search_user }}{% endif %}&sort={% if sort == None %}asc{% else %}{{ sort }}{% endif %}&sort-col={% if sort_col == None %}code{% else %}{{ sort_col }}{% endif %}" class="page-link cursor-pointer {% if num == page_content.number %} active {% endif %}" hx-trigger="click" hx-swap="outerHTML" hx-target=".content-user-management" hx-select=".content-user-management" hx-indicator=".loading-user-management">{{ num }}</a>
                                                {% elif num|sub:page_content.number > -3 and num|sub:page_content.number < 3  %}
                                                    <span class="page-link">...</span>
                                                {% endif %} 
                                            
                                            {% endfor %}
                                        </div>
                                    {% endwith %}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="loading htmx-indicator loading-user-management loading-absolute">
                        <img src="{% static 'img/loading.gif' %}" alt="loading" width="50">
                    </div> 
                </div>
                
            
                <c-popup class="popup-wide edit-user" buttonleft >
                    <div class="d-flex mb-4 justify-content-between align-items-center">
                        <h2 class="mb-0">Edit User</h2>
                        <button class="btn-text close-popup" type="button"><img src="{% static 'img/icon-close.svg' %}" alt="close"></button>
                    </div>
                    <div id="edit-user">
                        {% if registered_user %}
                        <form hx-post="{% url 'system_management:update_user' registered_user.id  %}" hx-trigger="submit" hx-indicator=".loading-user-management" class="form-edit-user" hx-swap="innerHTML" hx-target=".container-content">
                            {% csrf_token %}
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="form-group text-start">
                                        <label for="email">User email</label>
                                        <input required type="text" name="email" id="Email" placeholder="email" class="form-control" {% if registered_user.email %}value="{{registered_user.email}}"{% endif %}>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group text-start">
                                        <label for="Role" >Role</label>
                                        <div class="wrapper-select">
                                            <select name="role" id="role" class="form-control" title="role">
                                                <option value="admin"  {% if registered_user.role.name == 'admin' %}selected{% endif %}>Admin</option>
                                                <option value="user" {% if registered_user.role.name == 'user' %}selected{% endif %}>User</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group text-start">
                                        <label for="Status">Status</label>
                                        <div class="wrapper-select">
                                            <select name="status" id="status" class="form-control" title="status">
                                                <option value="active" {% if registered_user.role.status == 'active' %}selected{% endif %}>Active</option>
                                                <option value="suspended" {% if registered_user.role.status == 'suspended' %}selected{% endif %}>Suspended</option>
                                                <option value="inactive" {% if registered_user.role.status == 'inactive' %}selected{% endif %}>Inactive</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex" name="button">
                                <button type="submit" class="btn btn-primary close-popup">Save changes</button>
                                <button type="button" class="btn-text text-danger close-popup">Cancel </button>
                            </div> 
                        </form>
                        {% endif %}
                    </div>
                </c-popup>
                <c-popup class="popup-wide bulk-action" buttonleft >
                    <div class="d-flex mb-4 justify-content-between align-items-center">
                        <h2 class="mb-0">Bulk Action</h2>
                        <button class="btn-text close-popup" type="button"><img src="{% static 'img/icon-close.svg' %}" alt="close"></button>
                    </div>
                    <div class="my-4 list-selected-user">
                        Selected: CStewart, LKelly, MSanders, BGarcia, and 5 other. 
                    </div>
                
                
                    <form  class="form-bulk-user" hx-post="{% url 'system_management:create_user' %}" hx-trigger="submit" hx-indicator=".loading-user-management" hx-swap="innerHTML" hx-target=".container-content">
                        {% csrf_token %}
                        <div class="mb-5">
                            <p class="text-start mt-4 mb-2">Choose action</p>
                            
                            <div class="form-group mb-4">
                                <label class="wrapper-checkbox mb-2"> Change role
                                    <input type="checkbox" title="change-role" id="checkbox-change-role" name="checkbox-change-role">
                                    <span class="checkmark"></span>
                                </label>
                
                                <div class="form-change-role d-none ms-4">
                                    <div class="text-start mt-2">
                                        <div class="row">
                                            <div class="col-md-5">
                                                <p class="text-start">Change to</p>
                                                <div class="wrapper-select">
                                                    <select name="change-role" title="change-role" class="select-compact">
                                                        <option value="">Choose role</option>
                                                        <option value="admin">Admin</option>
                                                        <option value="user">User</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="wrapper-checkbox mb-2"> Change status
                                    <input type="checkbox" title="change-status" id="checkbox-change-status" name="checkbox-change-status">
                                    <span class="checkmark"></span>
                                </label>
                                <div class="form-change-status d-none ms-4">
                                    <div class="row">
                                        <div class="col-md-5">
                                            <div class="text-start">
                                                <p class="text-start">Change to</p>
                                                <div class="wrapper-select">
                                                    <select name="change-status" title="change-status" class="select-compact">
                                                        <option value="">Choose status</option>
                                                        <option value="active">Active</option>
                                                        <option value="suspended">Suspended</option>
                                                        <option value="inactive">Inactive</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>                            
                        </div>
                    
                        
                        <div class="d-flex align-items-center gap-3 justify-content-center">
                            <button name="submit-bulk" type="submit" class="btn btn-primary close-popup">Save changes</button>
                            <button type="button" class="btn-text text-danger close-popup">Cancel </button>
                        </div> 
                    </form> 
                </c-popup>    
            </div>


           
            <div class="popup popup-wide d-none popup-validation__bulk-user">
                <div class="backdrop"></div>
                <div class="content-popup content-popup-submission_ica-schedule"> 
                    <div class="validation-content"></div>
                    <div class="d-flex align-items-center gap-3 justify-content-center mt-4">
                        <button form="importBulkUserForm" type="submit" class="btn btn-primary close-popup btn-proceed-upload">Proceed</button>
                        <button type="button" class="btn-text text-danger close-popup">Cancel</button>
                    </div>
                </div>
            </div>
                         
            {% if upload_user_upload_status %}
            <div class="popup popup-wide popup-validation__bulk-user">
                <div class="backdrop"></div>
                    <div class="content-popup content-popup-submission_ica-schedule"> 
                        {% if upload_user_upload_status %}
                            <h2>Success!</h2>                             
                            <img src="{% static 'img/sassy-happy.png' %}" alt="sassy happy">
                            <p class="mb-0">File upload successful.</p>
                            <br>
                           
                            <span>Number of users created: {{upload_user_number_created}}</span><br>
                            <span>Number of users updated: {{upload_user_number_updated}}</span><br>
                            <span>Number of users failed: {{upload_user_number_failed}}</span>

                        {% else %}
                            <h2>Almost There!</h2>                             
                            <img src="{% static 'img/sassy-sad.png' %}" alt="sassy sad">
                            <p class="mb-0">Failed to upload</p>
                            <p>Please re-upload the file.</p>    
                        {% endif %}

                        <div class="mt-4">               
                            <div class="d-flex align-items-center gap-3 justify-content-center">
                                <button onclick="window.location.href='{% url 'system_management:systemManagement' %}'" onclick="" type="button" class="btn btn-primary close-popup">Okay! </button> 
                            </div>    
                        </div> 

                    </div>
                </div>  
            </div>            
            {% endif %}
        </div>
    </div>
</div>



