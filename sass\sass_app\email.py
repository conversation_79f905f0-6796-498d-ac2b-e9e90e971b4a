from django.core.mail import send_mail, EmailMessage
from django.template.loader import render_to_string
from django.conf import settings
from textwrap import dedent
from threading import Thread
from sass_app.middleware import ErrorLoggingMiddleware  # Import your middleware
from .models import ToolTip

import boto3
from django.template.loader import render_to_string
from django.conf import settings


def get_SUPPORT_EMAIL():
    SUPPORT_EMAIL="<EMAIL>"
    if ToolTip.objects.filter(slug='support-email').first():
        if ToolTip.objects.filter(slug='support-email').first().content:
            SUPPORT_EMAIL=ToolTip.objects.filter(slug='support-email').first().content
    return SUPPORT_EMAIL

SUPPORT_EMAIL = "<EMAIL>"

CREATE_USER_EMAIL_TITLE='Welcome to SASS – Your Account has been Created!'
CREATE_USER_EMAIL_TEMPLATE ="""
    Hi {name},

    Welcome to SASS!
    Your account has been successfully created. Please login with your NYP credentials.

    If you have any issues, feel free to contact our support team at {support_email}.

    Best regards,
    SASS Team
    """

LU_STATUS_CHANGE_EMAIL_TITLE='Notification of LU Status Update in SASS'
LU_STATUS_CHANGE_EMAIL_TEMPLATE ="""
    Hi  {name},
    We would like to inform you that the LU Status has been updated in the SASS system. Please log in to SASS to view the latest details.
    Details of the change:

    Admin Name: {admin_name}
    Date/Time of Change: {timestamp}
    LU Code: {lu_code}
    Status: {lu_status}

    If you have any issues, feel free to contact our support team at {support_email}.
    Best regards,
    SASS Team
    """

REPORT_DELIVERY_EMAIL_TEMPLATE ="""
    Hi  {name},
    The requested report {report_name} has been successfully processed and is now ready for download.


    Please click on the attached file to download the report. Due to the size of the file, the download may take some time depending on your network connection.
    
    If you have any issues, feel free to contact our support team at {support_email}.
    Best regards,
    SASS Team
    """

SYSTEM_MANAGEMENT_EMAIL_TEMPLATE ="""
    Hi  {name},
    The requested {file_name} has been successfully processed and is now ready for download.

    
    Please click on the attached file to download the {file_name}. Due to the size of the file, the download may take some time depending on your network connection.
    
    If you have any issues, feel free to contact our support team at {support_email}.
    Best regards,
    SASS Team
    """

OVERIDE_SASS_EMAIL_TEMPLATE_TITLE='Notification of Strategy Override in SASS'
OVERIDE_SASS_EMAIL_TEMPLATE ="""
    Hi {name},

    This is to inform you that your assessment details have been overridden by the SASS system—please log in to SASS to view the latest updates.

    Details of the change:

    Admin Name: {admin_name}
    Date/Time of Change: {timestamp}
    LU Code: {lu_code}
    Action Made: {action}
    Change Details: {change_value}
    If you have any issues, feel free to contact our support team at {support_email}.

    Best regards,
    SASS Team

"""


class SESEmailService:
    def __init__(self):
        """Initialize SES client"""
        self.client = boto3.client(
            'ses',
            region_name=settings.AWS_SES_REGION_NAME,
            aws_access_key_id=settings.AWS_SES_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SES_SECRET_ACCESS_KEY
        )

def send_text_email(payload):
    """
    Send plain text email using Amazon SES
    
    Args:
        payload (dict): Contains subject, message, and destination_email
    Returns:
        bool: True if successful, False otherwise
    """
    try:

        subject = payload['subject']
        message = payload['message']
        destination_email = payload['destination_email']

        # Convert destination_email to list if it's a string
        if isinstance(destination_email, str):
            destination_email = [destination_email]

        # Initialize SES client
        ses_service = SESEmailService()
        
        response = ses_service.client.send_email(
            Source=settings.DEFAULT_FROM_EMAIL,
            Destination={
                'ToAddresses': destination_email
            },
            Message={
                'Subject': {
                    'Data': subject,
                    'Charset': 'UTF-8'
                },
                'Body': {
                    'Text': {
                        'Data': message,
                        'Charset': 'UTF-8'
                    }
                }
            }
        )
        return True
        
    except Exception as e:
        try:
            raise ValueError("EMAIL ERROR: " + str(e))
        except Exception as e:  # caught by Error Log Middleware
            ErrorLoggingMiddleware.log(error=e, request_data=payload)
        return False

def send_html_email(payload):
    """
    Send HTML email using Amazon SES
    
    Args:
        payload (dict): Contains subject, context, html, and destination_email
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        subject = payload['subject']
        context = payload['context']
        html = payload['html']
        destination_email = payload['destination_email']

        # Convert destination_email to list if it's a string
        if isinstance(destination_email, str):
            destination_email = [destination_email]

        # Render HTML content
        html_content = render_to_string(html, context)
        
        # Initialize SES client
        ses_service = SESEmailService()
        
        response = ses_service.client.send_email(
            Source=settings.DEFAULT_FROM_EMAIL,
            Destination={
                'ToAddresses': destination_email
            },
            Message={
                'Subject': {
                    'Data': subject,
                    'Charset': 'UTF-8'
                },
                'Body': {
                    'Html': {
                        'Data': html_content,
                        'Charset': 'UTF-8'
                    }
                }
            }
        )
        return True
        
    except Exception as e:
        try:
            raise ValueError("EMAIL ERROR: " + str(e))
        except Exception as e:  # caught by Error Log Middleware
            ErrorLoggingMiddleware.log(error=e, request_data=payload)
        return False




def send_text_email_bg_jobs(payload):
    # Create and start thread for email sending
    thread = Thread(target=send_text_email, args=(payload,))
    thread.daemon = True  # Thread will exit when main program exits
    thread.start()
    return True