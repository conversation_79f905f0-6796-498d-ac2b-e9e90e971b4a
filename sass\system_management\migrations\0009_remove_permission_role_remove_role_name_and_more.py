# Generated by Django 5.1 on 2024-10-16 09:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "system_management",
            "0008_alter_permission_create_alter_permission_delete_and_more",
        ),
    ]

    operations = [
        migrations.RemoveField(
            model_name="permission",
            name="role",
        ),
        migrations.RemoveField(
            model_name="role",
            name="name",
        ),
        migrations.AddField(
            model_name="permission",
            name="type",
            field=models.CharField(
                choices=[("admin", "Admin"), ("user", "User")],
                default="admin",
                max_length=10,
            ),
        ),
        migrations.AddField(
            model_name="role",
            name="permission",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="system_management.permission",
            ),
        ),
    ]
