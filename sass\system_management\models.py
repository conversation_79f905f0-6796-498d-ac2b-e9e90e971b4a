import uuid
from django.db import models
from django.contrib.auth.models import User
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from django.utils.text import slugify
import os
import boto3
from botocore.exceptions import ClientError
from sass_app.middleware import ErrorLoggingMiddleware 
from sass.storage import S3Storage

# Create your models here.
s3_storage = S3Storage()

class Permission(models.Model):
    type = models.CharField(max_length=10, choices=[(
        'admin', 'Admin'), ('user', 'User')], null=False, default="admin")
    page = models.CharField(max_length=500, null=True, blank=True,
                            choices=[('schedule_assessement', 'Schedule Assessement'), ('system_management', 'System Management'), (
                                'report', 'Report'), ('live_schedule_status', 'Live Schedule Status')],
                            default='system_management')  # Page group type
    create = models.BooleanField(default=False)
    update = models.BooleanField(default=False)
    delete = models.BooleanField(default=False)
    view = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['type', 'page']
        verbose_name = 'Permission'
        verbose_name_plural = 'Permissions'

    def __str__(self):
        return f"{self.type}-{self.page} view:{self.view}|create:{self.create}|update:{self.update}|delete:{self.delete}"


class Role(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_query_name="registered_user",
        null=True,
        blank=True
    )

    type = models.CharField(max_length=10, choices=[(
        'admin', 'Admin'), ('user', 'User')], null=False, default="admin")

    status = models.CharField(
        max_length=10,
        choices=[('active', 'Active'), ('inactive', 'Inactive'),
                 ('suspended', 'Suspended')],
        null=True,
        blank=True,
        default='inactive'
    )

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.type


class FileUpload(models.Model):

    FILE_TYPE_CHOICES = [
        ('course', 'Course'),
        ('assessment', 'Assessment'),
        ('moderation_method', 'Moderation Method'),
        ('course_lu_map', 'Course LU Map'),
        ('academic_week', 'Academic Week'),
        ('learning_unit', 'Learning Unit'),
        ('department', 'Department'),
        ('user', 'User'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    file = models.FileField(upload_to='masterfiles/')  # Use the custom S3 storage here)
    type = models.CharField(max_length=20, choices=FILE_TYPE_CHOICES)
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def save(self, *args, **kwargs):
        if settings.SYSTEM_RUN_LOCATION in ['PROD','CSTACK', 'CSTACK-PROD']:
            self.file.storage = S3Storage()
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if settings.SYSTEM_RUN_LOCATION in ['PROD','CSTACK', 'CSTACK-PROD']:
            # Delete the file from S3
            try:
                # Delete from S3
                s3_client = boto3.client('s3', region_name=settings.AWS_S3_REGION_NAME)
                s3_client.delete_object(
                    Bucket=settings.AWS_STORAGE_BUCKET_NAME,
                    Key=self.file.name  # or with media prefix: f"media/{self.file.name}"
                )

                
                # Then delete the file field reference
                self.file.delete(save=False)
                
            except ClientError as e:
                ErrorLoggingMiddleware.log(error=str(e),request_data={
                    'file_id': str(self.id),
                    'file_name': self.name,
                    'file_type': self.type
                })    
        else:
            # Delete the file first
            if self.file:
                if os.path.exists(self.file.path):
                    if os.path.isfile(self.file.path):
                        os.remove(self.file.path)
        
        # Then delete the model instance
        super().delete(*args, **kwargs)

    def __str__(self):
        return self.name


class Department(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    code = models.CharField(max_length=20, unique=True)
    name = models.CharField(max_length=255)
    owner = models.CharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name


class LearningUnit(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    code = models.CharField(max_length=20, unique=True)
    name = models.CharField(max_length=255)
    credit = models.IntegerField(null=True, blank=True)
    is_examinable = models.CharField(max_length=10, choices=[(
        'no', 'No'), ('yes', 'Yes')], null=False, default="no")
    unit_leader = models.CharField(max_length=255, null=True, blank=True)
    course_type = models.CharField(max_length=255, null=True, blank=True)
    lu_type = models.CharField(max_length=255, null=True, blank=True)

    department = models.ForeignKey(
        Department, on_delete=models.SET_NULL, blank=True, null=True, related_name='learning_units')

    phase = models.IntegerField(null=True, blank=True)
    message = models.CharField(max_length=255, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name


class SystemCurrentYear(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    current_year = models.IntegerField(null=True, blank=True)
    current_semester = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    @classmethod
    def initialize(cls):
        """
        Creates initial record if none exists
        """
        if not cls.objects.exists():
            cls.objects.create(
                current_year=timezone.now().year,
                current_semester=1
            )

    def __str__(self):
        return f"System {self.id} - Year: {self.current_year}, Semester: {self.current_semester}"


class StatusLearningUnit(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('saved', 'Saved'),
        ('approved', 'Approved'),
        ('submitted', 'Submitted'),
        ('rework', 'Rework'),
    ]
    current_year = models.IntegerField(
        null=False, blank=False, default=int(timezone.now().year))
    current_semester = models.IntegerField(null=False, blank=False, default=1)
    lu_metadata = models.JSONField(null=True, blank=True)
    lu = models.ForeignKey(
        LearningUnit, on_delete=models.SET_NULL, null=True, blank=True)
    original_lu_code = models.CharField(max_length=100, null=True, blank=True)

    weightage = models.IntegerField(
        validators=[MinValueValidator(0), MaxValueValidator(100)], default=0)
    status = models.CharField(
        max_length=10, choices=STATUS_CHOICES, default='pending')
    updated_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    ica_detail_submit = models.BooleanField(default=False)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['current_year', 'current_semester', 'lu']

    def __str__(self):
        if self.lu:
            return self.lu.name
        return f"Status LU ({self.original_lu_code or 'No code'}) - {self.status}"
    
    def save(self, *args, **kwargs):
        if not self.status in [choice[0] for choice in self.STATUS_CHOICES]:
            self.status = self.STATUS_CHOICES[0][0]  # Reset to first choice ('pending')
        super().save(*args, **kwargs)


class StatusLearningUnitTest(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('saved', 'Saved'),
        ('approved', 'Approved'),
        ('submitted', 'Submitted'),
        ('rework', 'Rework'),
    ]
    current_year = models.IntegerField(
        null=False, blank=False, default=int(timezone.now().year))
    current_semester = models.IntegerField(null=False, blank=False, default=1)
    lu_metadata = models.JSONField(null=True, blank=True)
    lu = models.ForeignKey(
        LearningUnit, on_delete=models.SET_NULL, null=True, blank=True)
    original_lu_code = models.CharField(max_length=100, null=True, blank=True)

    weightage = models.IntegerField(
        validators=[MinValueValidator(0), MaxValueValidator(100)], default=0)
    status = models.CharField(
        max_length=10, choices=STATUS_CHOICES, default='pending')
    updated_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    ica_detail_submit = models.BooleanField(default=False)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['current_year', 'current_semester', 'lu']

    def __str__(self):
        if self.lu:
            return self.lu.name
        return f"Status LU ({self.original_lu_code or 'No code'}) - {self.status}"


class SystemEditMode(models.Model):
    MODE_CHOICES = [
        ('on', 'On'),
        ('interim', 'Interim'),
        ('off', 'Off'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    group = models.IntegerField(default=0)
    start = models.DateTimeField(null=True, blank=True)
    end = models.DateTimeField(null=True, blank=True)
    edit_after = models.CharField(
        max_length=7, choices=MODE_CHOICES, null=False, default='off')
    mode = models.CharField(
        max_length=7, choices=MODE_CHOICES, null=False, default='on')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"SystemEditMode {self.id} - {self.mode}"


class Course(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    code = models.CharField(max_length=128, unique=True)
    name = models.CharField(max_length=255)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.code} - {self.name}"


class Assessment(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    slug = models.SlugField(max_length=255, unique=True, default="")
    method = models.CharField(max_length=255)
    task = models.CharField(max_length=255)
    duration = models.CharField(max_length=255, null=True, blank=True)
    notes = models.CharField(max_length=255)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.method)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.method} - {self.task}"


class ModerationMethod(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    slug = models.SlugField(max_length=255, unique=True, default="")
    description = models.CharField(max_length=255)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.description)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.description


class CourseLuMap(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    academic_year = models.IntegerField(null=True, blank=True)
    semester = models.IntegerField(null=True, blank=True)
    year_study = models.CharField(max_length=255, null=True, blank=True)
    course_code = models.ForeignKey(Course, on_delete=models.CASCADE)
    lu = models.ForeignKey(LearningUnit, on_delete=models.CASCADE)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.course_code} - {self.lu}"


class AcademicWeek(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    coursetype = models.CharField(max_length=128)
    lutype = models.CharField(max_length=128)
    maxicacountperweek = models.IntegerField(null=True, blank=True)
    lastinstructionalweek = models.IntegerField(null=True, blank=True)
    startdate = models.DateField(null=True, blank=True)
    term1end = models.DateField(null=True, blank=True)
    weeks = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.coursetype} - {self.lutype}"
