<div class="d-flex gap-3 align-items-center justify-content-between w-100">
    
    <div class="form-group w-100 mb-0">
        <label for="date-time-from" class="text-start w-100">Date time from</label>
        <div class="wrapper-calendar">
            <input id="date-time-from" type="text" name="date-time-from" class="date-filter form-control" title="filter date from">
        </div>
    </div>
    <div class="form-group w-100 mb-0">
        <label for="date-time-to" class="text-start w-100">Date time to</label>
        <div class="wrapper-calendar">
            <input id="date-time-to" type="text" name="date-time-to" class="date-filter form-control" title="filter date to" autocomplete="false">
        </div>
    </div>
</div>

<script>
    jQuery(function($){
        $('.date-filter').datetimepicker({
            timepicker: false,
            format:'Y/m/d'
        });
    })
</script>