from django.urls import include, path
from django.conf import settings
from django.conf.urls.static import static
from . import views
app_name = "system_management"
urlpatterns = [
    path('system-management/', views.system_management, name="systemManagement"),
    path('user-management/', views.user_management, name="userManagement"),
    path('user-management/user/', views.create_or_update_user, name="create_user"),
    path('user-management/user/<id>',
         views.create_or_update_user, name="update_user"),
    path('user-management/user/get/<id>',
         views.get_edit_user, name="get_edit_user"),

    path('user-management/export', views.export_user_table,
         name="export_user_table"),


    path('lu-status/', views.lu_status, name="luStatus"),
    path('lu-status/update_lu_status',
         views.update_lu_status, name="update_lu_status"),

    path('set-current/', views.set_current, name="setCurrent"),
    path('set-current/update_set_current',
         views.update_set_current, name="update_set_current"),
    path('edit-mode/', views.edit_mode, name="editMode"),
    path('form-edit-mode/', views.form_edit_mode, name="formEditMode"),
    path('form-file-view/', views.form_file_view, name="form_file_view"),
    path('form-upload/', views.form_upload, name="formUpload"),
    path('upload_file/', views.upload_file, name="upload_file"),
    path('activity-log/', views.activity_log, name="activityLog"),

    path('activity-log/export', views.export_activity_table,
         name="export_activity_table"),
    path('filter-activity-log/', views.filter_activity_log, name="filterLog"),
    path('role-management/', views.role_management, name="roleManagement"),
    path('role-management/update', views.update_role_management,
         name="update_role_management"),
    path('role-management/export', views.export_role_management_table,
         name="export_role_management_table"),
    path('rule-configuration', views.rule_configuration,
         name="ruleConfiguration"),
    path('onchange-business-rule', views.onchange_business_rule,
         name='onchange_business_rule'),
    path('add-condition', views.add_condition,
         name='add_condition'),
    path('add-sub-condition', views.add_sub_condition,
         name='add_sub_condition'),

    path('submit-business-rule/', views.ruleSubmit, name='ruleSubmit'),
    path('submit-business-rule-test/',
         views.ruleTestSubmit, name='ruleTestSubmit'),

    path('filter-lu-status/', views.filter_by_lu_status, name="filterByLuStatus"),
    path('import-bulk-user/', views.import_bulk_user, name="importBulkUser")

]