# Generated by Django 5.1 on 2024-09-03 04:58

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="LearningUnit",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("code", models.CharField(max_length=10, unique=True)),
                ("name", models.CharField(max_length=200)),
                ("semester", models.PositiveIntegerField(blank=True)),
                ("academic_year", models.PositiveIntegerField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="Week",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("number", models.PositiveIntegerField(unique=True)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("semester", models.PositiveIntegerField(blank=True)),
                ("academic_year", models.PositiveIntegerField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="Assessment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("asses_over_2_week", models.BooleanField(default=False)),
                ("asses_weightage", models.PositiveIntegerField(blank=True)),
                ("asses_method", models.CharField(max_length=200)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "learning_unit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="sass_app.learningunit",
                    ),
                ),
                (
                    "week",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="sass_app.week"
                    ),
                ),
            ],
            options={
                "unique_together": {("learning_unit", "week")},
            },
        ),
    ]
