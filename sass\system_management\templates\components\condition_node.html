{% load static %}
{% load custom_tags %}
    <div class="{% if not condition|is_condition %}conditional all {% else %}container-condition{% endif %}">    
        
           
            {% if condition|is_condition %} {# Leaf condition #}                
                <div class="rule">
                    <div class="row content-row mt-2">
                        <div class="col-md-4">                
                            <input
                                type="text"
                                value="{{ condition.name }}"
                                class="form-control input-name field"
                                readonly                               
                            />
                            
                        </div>
                        <div class="col-md-4">                
                            <input
                                type="text"
                                value="{{ condition.operator}}"
                                class="form-control input-operator operator"
                                readonly
                               
                            />
                            
                            {% if condition.operator == "is_false" or condition.operator == "is_true" %} 
                            <div class='remove-rule'></div> 
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 {% if condition.operator == "is_false" or condition.operator == "is_true" %}d-none{% endif %}">                
                            <input
                                type="text"
                                value="{{ condition.value }}"
                                class="form-control input-value value"
                                readonly
                            />
                            {% if condition.operator != "is_false" or condition.operator != "is_true" %} 
                            <div class='remove-rule'></div> 
                            {% endif %}
                        </div>                            
                        
                    </div>
                </div>
            {% else %} 
                {% for operator, subconditions in condition.items %} 
                    
                    {% if operator == 'all' or operator == 'any' %} 
                        <div class="all-any-wrapper">
                            <div class="d-flex align-items-center gap-3 mb-2">                                
                                <a href="#" class="add-rule text-black" 
                                hx-get="{% url 'system_management:add_condition' %}"
                                hx-target="closest .all-any-wrapper"
                                hx-swap="afterend"                                
                                hx-trigger="click"
                                data-level="{{ level }}"
                               >
                                    <img src="/static/img/icon-plus.svg" alt="plus"> Add Condition </a> 
                                <a href="#" class="add-sub-condition text-black"
                                hx-target="closest .all-any-wrapper"
                                hx-swap="afterend"
                                
                                hx-trigger="click"
                                hx-get="{% url 'system_management:add_sub_condition' %}"
                                hx-vals='{"level": {{ level|add:"1" }}}'
                                ><img src="/static/img/icon-plus.svg" alt="plus"> Add Sub Condition</a>      
                                {% if level > 0 %}
                                <a class="remove remove-sub-condition" href="#"><img src="/static/img/icon-trash-red.svg" alt="trash"> Delete section</a>                                    
                                {% endif %}               
                            </div> 
                            <div class="wrapper-select">
                                <div class="d-flex gap-3 align-items-center">
                                    <p>Rule:</p>
                                    <select class="form-control all-any">
                                        <option value="all" {% if operator|upper == "ALL"  %}selected{% endif %}>All</option>
                                        <option value="any" {% if operator|upper == "ANY"  %}selected{% endif %}>Any</option>
                                    </select>                            
                                </div>
                            </div>{# Group condition (all/any) #} 
                            <p class="mt-3">Of the following conditions: 
                               {% comment %} {{ loop_index }} {% endcomment %}
                            </p>
                        </div>                                              
                       {% for subcondition in subconditions %} 
                            {% render_condition_node subcondition level=level|add:1 loop_index=forloop.counter %} 
                        {% endfor %}
                    {% endif %} 
                {% endfor %}
            {% endif %} 
        
    </div>      
