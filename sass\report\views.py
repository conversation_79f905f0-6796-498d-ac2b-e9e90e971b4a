from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from openpyxl import Workbook
from django.db.models import Q, Case, When, DateTimeField, F, Value, Subquery, OuterRef
from openpyxl.styles import Protection, Font, Alignment, Border, Side, PatternFill
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.protection import SheetProtection
from django.http import HttpResponse
from schedule_assessment.models import *
from system_management.models import *
from django.db.models.functions import Cast, Concat
from openpyxl.worksheet.filters import AutoFilter


# Create your views here.


def set_bold(cell):
    cell.font = Font(bold=True)


@check_access(page="report")
def report_index(request):
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
    learning_units = LearningUnit.objects.filter(
        courselumap__academic_year=SYSTEM_CURRENT_YEAR.current_year, courselumap__semester=SYSTEM_CURRENT_YEAR.current_semester).order_by('code').distinct('code')
    courses = Course.objects.all().order_by('code')
    departments = Department.objects.all().order_by('code')
    year_studies = CourseLuMap.objects.all().distinct('year_study').order_by('year_study').values_list("year_study", flat=True)

    context = {
        "learning_units": learning_units,
        "courses": courses,
        "departments": departments,
        "year_studies": year_studies,
        "SYSTEM_CURRENT_YEAR": SYSTEM_CURRENT_YEAR
    }
    return render(request, 'report/index.html', context)


@login_required
@sending_email_if_timeout(timeout_seconds=20)
def download_learning_unit_assement_strategy(request):
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
    lu_code = request.POST.get('lu-code', None)

    learning_unit = LearningUnit.objects.filter(code=lu_code).first()
    wb = Workbook()
    sheet = wb.active
    sheet.title = "Assessment Strategies"
    # Header
    sheet['A1'] = "NANYANG POLYTECHNIC"
    set_bold(sheet['A1'])
    sheet['A2'] = "SCHOOL OF BUSINESS MANAGEMENT"
    set_bold(sheet['A2'])
    sheet['A4'] = f"AY{SYSTEM_CURRENT_YEAR.current_year}/{
        SYSTEM_CURRENT_YEAR.current_year+1} Semester {SYSTEM_CURRENT_YEAR.current_semester}"
    set_bold(sheet['A4'])
    sheet['A5'] = "Assessment Strategies"
    set_bold(sheet['A5'])
    sheet['A6'] = f"Module Code & Description : {
        learning_unit.code.upper()} {learning_unit.name.upper()}"
    set_bold(sheet['A6'])

    # Page Setup for A4 Portrait
    sheet.page_setup.paperSize = 9  # 9 corresponds to A4
    sheet.page_margins.left = 0.25
    sheet.page_margins.right = 0.25
    sheet.page_margins.top = 1.5
    sheet.page_margins.bottom = 0.5
    # Explicitly set orientation to portrait
    sheet.page_setup.orientation = 'portrait'
    # Assessment Components
    sheet['A8'] = "1    ASSESSMENT COMPONENTS"
    set_bold(sheet['A8'])
    headers = ["Asst Method", "Weightage (%)", "Duration", "Wk of Assmt",
               "Desc of Assm Instruments & Format", "Moderation methods", "Topics & LU Learning Outcomes"]

    # Write the headers in row 13
    filter_condition = Q(current_year=SYSTEM_CURRENT_YEAR.current_year) & Q(
        current_semester=SYSTEM_CURRENT_YEAR.current_semester) & Q(lu__code=lu_code)
    
    data = ScheduleAssessment.objects.filter(filter_condition).annotate(
        assm_type=Case(
            When(assessment_type='Group', then=Value('G')),
            When(assessment_type='Individual', then=Value('I')),
            default=Value(''),
            output_field=CharField(),
        ),
        combined_assessment=Case(
            When(
                assessment_type__isnull=False,
                then=Concat(
                    Cast('weightage', CharField()),
                    Value('('),
                    F('assm_type'),
                    Value(')')
                )
            ),
            default=Cast('weightage', CharField()),
        )
    ).values_list(
        'assessment_method__method',
        'combined_assessment',
        'duration',
        'week',
        'descriptions',
        'moderation_method__description',
        'topics',
        'note'
    ).order_by('week')


    row_num = 1
    if not data:
        sheet.merge_cells("A11:H11")
        sheet['A11'] = "No Data Found"
        sheet['A11'].alignment = Alignment(horizontal='center', vertical='center')

    for row, item in enumerate(data, start=11):
        
        # First column (number) alignment
        first_col_cell = sheet.cell(row=row, column=1, value=(row-11)+1)
        first_col_cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # Track longest cell content
        max_text_length = 0
        
        # First pass: write values and find longest text
        for col, value in enumerate(item, start=2):
            if col == len(item) + 1:
                continue
            
            cell = sheet.cell(row=row, column=col, value=value)
            
            # Enable text wrapping and alignment
            if (col > 2 and col < 6):
                cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            else:
                cell.alignment = Alignment(wrap_text=True, vertical='center')
                
            # Track text length for height calculation
            if isinstance(value, str):
                # Count actual newlines and approximate word wraps
                lines_from_newlines = value.count('\n') + 1
                # Approximate characters per line based on column width
                chars_per_line = 40 if col in [5, 7] else 20  # Adjust these values based on your column widths
                lines_from_length = len(value) / chars_per_line
                total_lines = max(lines_from_newlines, lines_from_length)
                max_text_length = max(max_text_length, total_lines)
        
        # Set row height after processing all columns
        row_height = max(max_text_length * 20, 120)  # Increased minimum height and multiplier
        sheet.row_dimensions[row].height = row_height

    for cell in sheet['A10:H10'][0]:
        cell.font = Font(bold=True)
    
    # Apply borders only to the assessment components table
    thin_border = Border(left=Side(style='thin'),
                         right=Side(style='thin'),
                         top=Side(style='thin'),
                         bottom=Side(style='thin'))


    for row in sheet.iter_rows(min_row=10, max_row=sheet.max_row, min_col=1, max_col=sheet.max_column):
        for cell in row:
            cell.border = thin_border

    # Quality Assurance
    sheet['A' + str(sheet.max_row + 2)] = "2    QUALITY ASSURANCE"
    set_bold(sheet['A' + str(sheet.max_row)])
    sheet['B' + str(sheet.max_row + 2)] = "Pre-assessment"
    set_bold(sheet['B' + str(sheet.max_row)])
    sheet.merge_cells(
        'B' + str(sheet.max_row+1) + ':H' + str(sheet.max_row+1))
    sheet['B' + str(sheet.max_row)
          ].alignment = Alignment(wrap_text=True, vertical="top")
    sheet['B' + str(sheet.max_row + 2)] = "Administration of assessment"
    set_bold(sheet['B' + str(sheet.max_row)])

    sheet.merge_cells(
        'B' + str(sheet.max_row+1) + ':H' + str(sheet.max_row+1))
    sheet['B' + str(sheet.max_row)
          ].alignment = Alignment(wrap_text=True, vertical="top")
    sheet['B' + str(sheet.max_row + 2)] = "Marking and moderation"
    sheet.merge_cells(
        'B' + str(sheet.max_row+1) + ':H' + str(sheet.max_row+1))
    sheet['B' + str(sheet.max_row)
          ].alignment = Alignment(wrap_text=True, vertical="top")
    set_bold(sheet['B' + str(sheet.max_row)])
    sheet['B' + str(sheet.max_row)
          ] = "Refer to the table above for moderation strategy."
    yellowFill = PatternFill(start_color='FFF600',
                             end_color='FFF600',
                             fill_type='solid')

    for cell in sheet[f'B{str(sheet.max_row)}:F{str(sheet.max_row)}'][0]:
        cell.fill = yellowFill
    sheet['B' + str(sheet.max_row + 3)] = "Checking and verification"
    set_bold(sheet['B' + str(sheet.max_row)])
    sheet.merge_cells(
        'B' + str(sheet.max_row+1) + ':H' + str(sheet.max_row+1))
    sheet['B' + str(sheet.max_row)
          ].alignment = Alignment(wrap_text=True, vertical="top")
    sheet['B' + str(sheet.max_row)
          ] = "Checkers are assigned to check all scripts and accuracy of data entry."
    # Any Other Comments
    sheet['A' + str(sheet.max_row + 3)] = "3    ANY OTHER COMMENTS"
    set_bold(sheet['A' + str(sheet.max_row)])
    sheet.merge_cells(
        'B' + str(sheet.max_row+2) + ':H' + str(sheet.max_row+2))
    sheet['B' + str(sheet.max_row)
          ].alignment = Alignment(wrap_text=True, vertical="top")
    sheet['B' + str(sheet.max_row)] = "Nil"
    # Footer
    sheet['B' + str(sheet.max_row + 2)] = "Submitted by :"
    sheet['B' + str(sheet.max_row + 1)] = "Supported by :"
    sheet['B' + str(sheet.max_row + 1)] = "Endorsed by :"
    sheet['B' + str(sheet.max_row + 1)] = "Date :"

    first_cell = sheet.cell(row=10, column=1, value="No.")
    column_letter = get_column_letter(1)
    sheet.column_dimensions[column_letter].width = 5
    first_cell.alignment = Alignment(
        wrap_text=True, horizontal='center', vertical='center')
    
    # Column Width Optimization
    column_widths = [15, 12, 6, 8, 14, 12, 13]
    for col, (width, header) in enumerate(zip(column_widths, headers), start=2):
        column_letter = get_column_letter(col)
        sheet.column_dimensions[column_letter].width = width    # Added +1
        cell = sheet.cell(row=10, column=col, value=header)
        cell.font = Font(bold=True)
        cell.alignment = Alignment(
            wrap_text=True, horizontal='center', vertical='center')
    # Locked
    # Unlock all cells first
    for row in sheet.iter_rows():
        for cell in row:
            cell.protection = Protection(locked=False)
    # Define the range to unlock -> open 100 row and 100 column
    for row in sheet.iter_rows(min_row=1, max_row=200, min_col=1, max_col=200):
        for cell in row:
            cell.protection = Protection(locked=False)
    # Lock specific rows (e.g., rows 2 to 3)
    
    for row in sheet.iter_rows(min_row=9, max_row=10+(len(data)*2)):
        for cell in row:
            cell.protection = Protection(locked=True)
    # Enable sheet protection with a password
    sheet.protection.sheet = True
    sheet.protection.password = '1234'
    sheet.protection.formatRows = False
    sheet.protection.formatColumns = False
    sheet.protection.enable()

    file_name = f"{lu_code}_Assm Strategy"
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = f'attachment; filename={file_name}.xlsx'
    wb.save(response)

    report_delivery_email_template = REPORT_DELIVERY_EMAIL_TEMPLATE.format(
        name=request.user.first_name, report_name=file_name, support_email=get_SUPPORT_EMAIL())
    report_delivery_email_template = dedent(
        report_delivery_email_template).strip()
    return response, {"wb": wb,
                      "file_name": f"{file_name}.xlsx",
                      "email_subject": f"{file_name} - Report is ready for Download",
                      "email_body": report_delivery_email_template}


@ login_required
@ sending_email_if_timeout(timeout_seconds=20)
def download_learning_unit_report(request):

    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()
    department_code = request.POST.get('department')
    download_type = request.POST.get('download-type')
    department = Department.objects.filter(code=department_code).first()

    wb = Workbook()
    sheet = wb.active

    sheet.title = "Learning Unit"

    filter_conditions = Q(department__isnull=False)
    if department:
        # Assessment Components
        filter_conditions = Q(department=department)

    if download_type == 'learning-unit-without-ica-in-term-1':
        sheet['A1'] = "Learning Unit Without ICA TERM 1"

        term1_schedule = []
        schedule_filter = Q(week__isnull=False, lu__isnull=False,
                            current_year=SYSTEM_CURRENT_YEAR.current_year,
                            current_semester=SYSTEM_CURRENT_YEAR.current_semester,
                            lu__courselumap__academic_year=SYSTEM_CURRENT_YEAR.current_year,
                            lu__courselumap__semester=SYSTEM_CURRENT_YEAR.current_semester
                            )
        if department:
            schedule_filter &= Q(lu__department=department)

        for schedule in ScheduleAssessment.objects.filter(schedule_filter):
            acadweek = AcademicWeek.objects.filter(
                coursetype=schedule.lu.course_type, lutype=schedule.lu.lu_type).first()
            if acadweek:
                if acadweek.term1end:
                    schedule_date = get_date_by_week(
                        schedule.week, schedule.lu)
                    if schedule_date:
                        if schedule_date <= acadweek.term1end:
                            term1_schedule.append(str(schedule.id))

        if term1_schedule:
            unique_lus = ScheduleAssessment.objects.filter(
                id__in=term1_schedule).values_list('lu', flat=True).distinct()
            filter_conditions &= ~Q(id__in=unique_lus)

    elif download_type == 'learning-unit-without-ica':
        sheet['A1'] = "Learning Unit Without ICA"
        schedule_filter = Q(week__isnull=False, lu__isnull=False,
                            current_year=SYSTEM_CURRENT_YEAR.current_year,
                            current_semester=SYSTEM_CURRENT_YEAR.current_semester,
                            lu__courselumap__academic_year=SYSTEM_CURRENT_YEAR.current_year,
                            lu__courselumap__semester=SYSTEM_CURRENT_YEAR.current_semester
                            )
        unique_lus = ScheduleAssessment.objects.filter(
            schedule_filter).values_list('lu', flat=True)

        filter_conditions &= ~Q(id__in=unique_lus)

    else:
        sheet['A1'] = "All Learning Unit"
    set_bold(sheet['A1'])

    filter_conditions &= Q(courselumap__academic_year=SYSTEM_CURRENT_YEAR.current_year,
                           courselumap__semester=SYSTEM_CURRENT_YEAR.current_semester)

    headers = ["DEPT", "LUCode",	"LUDescription", "UL Name"]
    for col, header in enumerate(headers, start=1):
        sheet.cell(row=3, column=col, value=header)
        set_bold(sheet[f'{get_column_letter(col)}3'])

    learing_units = LearningUnit.objects.filter(
        filter_conditions).order_by('code').distinct('code')

    if learing_units:
        for row, item in enumerate(learing_units, start=4):
            sheet.cell(row=row, column=1, value=item.department.name)
            sheet.cell(row=row, column=2, value=item.code)
            sheet.cell(row=row, column=3, value=item.name)
            sheet.cell(row=row, column=4, value=item.unit_leader)
    else:
        sheet.merge_cells("A4:D4")
        sheet['A4'] = "No Data Found"
        sheet['A4'].alignment = Alignment(
            horizontal='center', vertical='center')

    # Adjust column widths to fit content
    for col in range(1, len(headers) + 2):
        max_length = 0
        column_letter = get_column_letter(col)
        # Iterate through all cells in the column
        for cell in sheet[column_letter]:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass

        adjusted_width = (max_length + 2) * 1
        sheet.column_dimensions[column_letter].width = adjusted_width
    if download_type == "learning-unit-without-ica-in-term-1":
        file_name = "LU No Term 1 ICA"
    else:
        file_name = "LU No ICA"

    # Create the HttpResponse object
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = f'attachment; filename={file_name}.xlsx'

    wb.save(response)

    report_delivery_email_template = REPORT_DELIVERY_EMAIL_TEMPLATE.format(
        name=request.user.first_name, report_name=file_name, support_email=get_SUPPORT_EMAIL())
    report_delivery_email_template = dedent(
        report_delivery_email_template).strip()
    return response, {"wb": wb,
                      "file_name": f"{file_name}.xlsx",
                      "email_subject": f"{file_name} - Report is ready for Download",
                      "email_body": report_delivery_email_template}


def sanitize_excel_value(value):
    """
    Sanitize value for Excel worksheet to avoid IllegalCharacterError.
    Removes or replaces illegal characters.
    """
    import re
    
    if value is None:
        return value
    
    if isinstance(value, str):
        # Replace common problematic Unicode characters
        value = value.replace('–', '-')  # en dash to hyphen
        value = value.replace('—', '-')  # em dash to hyphen
        value = value.replace(''', "'")  # smart single quote
        value = value.replace(''', "'")  # smart single quote
        value = value.replace('"', '"')  # smart double quote
        value = value.replace('"', '"')  # smart double quote
        value = value.replace('…', '...')  # ellipsis
        
        # Remove or replace other control characters that might cause issues
        # Keep only printable ASCII and basic Unicode characters
        # Remove control characters except tab, newline, carriage return
        value = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', value)
    
    return value

@ login_required
@ sending_email_if_timeout(timeout_seconds=20)
def download_learning_unit_assement_details(request):

    current_semester = request.POST.get('current-semester')
    current_year = request.POST.get('current-year')

    wb = Workbook()
    sheet = wb.active

    sheet.title = "Assessment Details"

    headers = ["Dept", "LUCode", "LUName", "Assm Task#", "Week No", "Assm Method", "Weightage", "AssmType", "Duration", "Over2 weeks", "Involve digital submission & synchronous presentation?",
               "Examinable", "Description of Assm Instruments & Format", "Topics & LU Learning Outcomes", "Moderation Method", "Reason for Change in Assm", "Note","Status", "Last updated", "User email"]

    # Write the headers in row 10
    filter_condition = Q(current_year=current_year) & Q(current_semester=current_semester) & Q(lu__isnull=False)

    filter_condition &= Q(lu__courselumap__academic_year=current_year,lu__courselumap__semester=current_semester)

    data = ScheduleAssessment.objects.filter(filter_condition).annotate(
        last_updated=Case(
            When(updated_at__isnull=False, then='updated_at'),
            default='created_at',
            output_field=DateTimeField(),
        ),
        lu_status=Subquery(
            StatusLearningUnit.objects.filter(
                lu=OuterRef('lu'),
                current_year=current_year,
                current_semester=current_semester
            ).values('status')[:1]
        )
    ).distinct("id").values_list(
        'lu__department__name', 'lu__code', 'lu__name', 'assessment_task',
        'week', 'assessment_method__method', 'weightage', 'assessment_type',
        'duration', 'over_two_weeks', 'digital_submission', 'lu__is_examinable',
        'descriptions', 'topics', 'moderation_method__description',
        'reasons_change','note', 'lu_status', 'last_updated',
        'updated_by__email'
    )

    for col, header in enumerate(headers, start=3):
        sheet.cell(row=1, column=col, value=header)

    sheet.auto_filter.ref = f'A1:{chr(65+len(headers)+1)}1'

    for col, header in enumerate(headers, start=1):
        sheet.cell(row=1, column=1, value="AcadYear")

    for col, header in enumerate(headers, start=2):
        sheet.cell(row=1, column=2, value="Semester")

    for row in range(2, 2+data.count()):
        sheet.cell(row=row, column=1, value=current_year)

    for row in range(2, 2+data.count()):
        sheet.cell(row=row, column=2, value=current_semester)

    # value_in_col_10 = ""
    if data:

        for row, item in enumerate(data, start=2):
            for col, value in enumerate(item, start=3):
                try:
                    if type(value) == datetime:
                        value = value.strftime("%d-%b-%Y %H:%M")

                    if col == 10:
                        # value_in_col_10 = sheet.cell(row=row + 1, column=col, value=value).value
                        sheet.cell(row=row, column=col, value=sanitize_excel_value(value))
                    elif col == 11:
                        if value == 0:
                            sheet.cell(row=row, column=col, value="NA")
                        else:
                            sheet.cell(row=row, column=col, value=sanitize_excel_value(str(value)))
                    elif col == 9:
                        sheet.cell(row=row, column=col, value=sanitize_excel_value(f'{str(value)}%'))
                    else:
                        sheet.cell(row=row, column=col, value=sanitize_excel_value(value))
                        
                except Exception as e:
                    sheet.cell(row=row, column=col, value="Error")
    else:
        sheet.merge_cells("A2:U2")
        sheet['A2'] = "No Data Found"
        sheet['A2'].alignment = Alignment(
            horizontal='center', vertical='center')

    for cell in sheet['A1:v1'][0]:
        cell.font = Font(bold=True)
    # Apply borders only to the assessment components table
    thin_border = Border(left=Side(style='thin'),
                         right=Side(style='thin'),
                         top=Side(style='thin'),
                         bottom=Side(style='thin'))

    # Adjust this range if you add more rows to the table
    # for row in sheet['A9:G12']:
    for row in sheet.iter_rows(min_row=1, max_row=sheet.max_row, min_col=1, max_col=sheet.max_column):
        for cell in row:
            cell.border = thin_border

    # Locked
    # Unlock all cells first
    for row in sheet.iter_rows():
        for cell in row:
            cell.protection = Protection(locked=False)

    # Define the range to unlock -> open 100 row and 100 column
    for row in sheet.iter_rows(min_row=1, max_row=200, min_col=1, max_col=200):
        for cell in row:
            cell.protection = Protection(locked=False)

    # Lock specific rows (e.g., rows 2 to 3)
    for row in sheet.iter_rows(min_row=10, max_row=10+len(data)):
        for cell in row:
            cell.protection = Protection(locked=True)

    # Enable sheet protection with a password
    # sheet.protection.sheet = True
    # sheet.protection.password = '1234'
    # sheet.protection.enable()

    # Adjust column widths to fit content
    for col in range(1, len(headers) + 3):
        max_length = 0
        column_letter = get_column_letter(col)
        if col == 1:
            sheet.column_dimensions[column_letter].width = 15
        else:
            # Iterate through all cells in the column
            for cell in sheet[column_letter]:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass

            adjusted_width = (max_length + 2) * 1
            sheet.column_dimensions[column_letter].width = adjusted_width

    # Create the HttpResponse object
    file_name = "Assm Detail"
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = f'attachment; filename={file_name}.xlsx'

    wb.save(response)

    report_delivery_email_template = REPORT_DELIVERY_EMAIL_TEMPLATE.format(
        name=request.user.first_name, report_name=file_name, support_email=get_SUPPORT_EMAIL())
    report_delivery_email_template = dedent(
        report_delivery_email_template).strip()
    return response, {"wb": wb,
                      "file_name": f"{file_name}.xlsx",
                      "email_subject": f"{file_name} - Report is ready for Download",
                      "email_body": report_delivery_email_template}


@ login_required
@ sending_email_if_timeout(timeout_seconds=20)
def download_learning_unit_assement_schedule(request):

    course = request.POST.get("course", None)
    year_of_study = request.POST.get("year-study", None)

    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()

    wb = Workbook()

    def create_worksheet(wb, course_data, sheet_name):
        # Create new worksheet
        sheet = wb.create_sheet(title=sheet_name)

        # Define border style
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        headers = ["Week Beginning", ""]
        for learning_unit in course_data:
            headers.append(learning_unit.lu.name)
        headers.extend(["Count", "%"])

        for col, header in enumerate(headers, start=1):
            cell = sheet.cell(row=9, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(
                wrap_text=True, horizontal='center', vertical='center')
        sheet.merge_cells("A9:B9")

        headers_lu_code = ["", ""]
        for learning_unit in course_data:
            headers_lu_code.append(learning_unit.lu.code)
        headers_lu_code.extend(["", ""])

        for col, header in enumerate(headers_lu_code, start=1):
            cell = sheet.cell(row=10, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(
                wrap_text=True, horizontal='center', vertical='center')

            cell = sheet.cell(row=11, column=col,
                              value='ICA Type' if header else "")
            cell.font = Font(bold=True)
            cell.alignment = Alignment(
                wrap_text=True, horizontal='center', vertical='center')

        # Header
        sheet.merge_cells(f"A1:{get_column_letter(len(headers))}1")
        merged_cell = sheet['A1']
        merged_cell.alignment = Alignment(
            wrap_text=True, horizontal='center', vertical='center')
        merged_cell.font = Font(bold=True)
        merged_cell.value = "NANYANG POLYTECHNIC"

        sheet.merge_cells(f"A2:{get_column_letter(len(headers))}2")
        merged_cell = sheet['A2']
        merged_cell.alignment = Alignment(
            wrap_text=True, horizontal='center', vertical='center')
        merged_cell.font = Font(bold=True)
        merged_cell.value = "SCHOOL OF BUSINESS MANAGEMENT"

        sheet.merge_cells(f"A4:{get_column_letter(len(headers))}4")
        merged_cell = sheet['A4']
        merged_cell.alignment = Alignment(
            wrap_text=True, horizontal='center', vertical='center')
        merged_cell.font = Font(bold=True)
        if course_data:
            merged_cell.value = course_data.first().course_code.name

        sheet.merge_cells(f"A5:{get_column_letter(len(headers))}5")
        merged_cell = sheet['A5']
        merged_cell.alignment = Alignment(
            wrap_text=True, horizontal='center', vertical='center')
        merged_cell.font = Font(bold=True)
        if course_data:
            merged_cell.value = "Year/MC/PDC " + course_data.first().year_study

        sheet.merge_cells(f"A6:{get_column_letter(len(headers))}6")
        merged_cell = sheet['A6']
        merged_cell.alignment = Alignment(
            wrap_text=True, horizontal='center', vertical='center')
        merged_cell.font = Font(bold=True)
        merged_cell.value = f"{SYSTEM_CURRENT_YEAR.current_year} Semester {
            SYSTEM_CURRENT_YEAR.current_semester}"

        sheet.merge_cells(f"A7:{get_column_letter(len(headers))}7")
        merged_cell = sheet['A7']
        merged_cell.alignment = Alignment(
            wrap_text=True, horizontal='center', vertical='center')
        merged_cell.font = Font(bold=True)
        merged_cell.value = "In-Course Assessment (ICA) Schedule"

        sheet.column_dimensions['B'].width = 12

        # Content section
        headers_lu_code = [
            header for header in headers_lu_code if header != ""]

        if headers_lu_code:
            black_fill = PatternFill(start_color='000000', end_color='000000', fill_type='solid')

            learning_unit = LearningUnit.objects.filter(code=headers_lu_code[0]).first()

            general_acad_week = AcademicWeek.objects.filter(coursetype=learning_unit.course_type,lutype=learning_unit.lu_type).first()
            general_start_week = general_acad_week.weeks['start']
            general_end_week = general_acad_week.weeks['end']

            start_content_row = 11
            total_weightage_vertical = {}
            total_ica_number_vertical = {}

            for week in range(general_start_week, general_end_week + 1):
                total_weightage_horizonal = 0
                total_ica_number_horizonal = 0
                date = get_date_by_week(
                    week, learning_unit=None, academicWeek=general_acad_week)

                cell = sheet.cell(row=start_content_row +
                                  week, column=1, value=week)
                cell.alignment = Alignment(
                    wrap_text=True, horizontal='center', vertical='center')

                cell = sheet.cell(row=start_content_row +
                                  week, column=2, value=date)
                cell.alignment = Alignment(
                    wrap_text=True, horizontal='center', vertical='center')
                cell.number_format = 'dd-mmm-yy'

                for col, header_lu_code in enumerate(headers_lu_code, start=3):
                    lu = LearningUnit.objects.filter(
                        code=header_lu_code).first()
                    if lu:
                        lu_acad_week = AcademicWeek.objects.filter(
                            lutype=lu.lu_type, coursetype=lu.course_type).first()
                    else:
                        lu_acad_week = general_acad_week  # if lu not found use general

                    if str(week) in lu_acad_week.weeks['status']:
                        if lu_acad_week.weeks['status'][str(week)]:
                            cell = sheet.cell(
                                row=start_content_row+week, column=col)
                            cell.fill = black_fill
                            continue

                    schedule_assessment = ScheduleAssessment.objects.filter(
                        week=week,
                        lu__code=header_lu_code,
                        current_year=SYSTEM_CURRENT_YEAR.current_year,
                        current_semester=SYSTEM_CURRENT_YEAR.current_semester
                    ).first()

                    if schedule_assessment:
                        total_ica_number_horizonal += 1
                        total_weightage_horizonal += schedule_assessment.weightage

                        if schedule_assessment.over_two_weeks == 'yes':
                            cell = sheet.cell(row=start_content_row+week+1, column=col, value=f"{
                                schedule_assessment.assessment_method.method} 0%")
                            cell.alignment = Alignment(
                                wrap_text=True, horizontal='center', vertical='center')

                        value = f"{schedule_assessment.assessment_method.method} {
                            schedule_assessment.weightage}%"
                        cell = sheet.cell(
                            row=start_content_row+week, column=col, value=value)
                        cell.alignment = Alignment(
                            wrap_text=True, horizontal='center', vertical='center')

                        total_weightage_vertical[header_lu_code] = total_weightage_vertical.get(
                            header_lu_code, 0) + schedule_assessment.weightage
                        total_ica_number_vertical[header_lu_code] = total_ica_number_vertical.get(
                            header_lu_code, 0) + 1

                cell = sheet.cell(row=start_content_row+week,
                                  column=col+1, value=total_ica_number_horizonal)
                cell.alignment = Alignment(
                    wrap_text=True, horizontal='center', vertical='center')
                cell = sheet.cell(row=start_content_row+week,
                                  column=col+2, value=total_weightage_horizonal)
                cell.alignment = Alignment(
                    wrap_text=True, horizontal='center', vertical='center')

            # Add summary rows
            summary_row = start_content_row+general_end_week+1
            cell = sheet.cell(row=summary_row, column=1, value='Count')
            cell.alignment = Alignment(
                wrap_text=True, horizontal='center', vertical='center')
            cell.font = Font(bold=True)
            sheet.merge_cells(f"A{summary_row}:B{summary_row}")

            cell = sheet.cell(row=summary_row+1, column=1, value='%')
            cell.alignment = Alignment(
                wrap_text=True, horizontal='center', vertical='center')
            cell.font = Font(bold=True)
            sheet.merge_cells(f"A{summary_row+1}:B{summary_row+1}")

            for col, header_lu_code in enumerate(headers_lu_code, start=3):
                if header_lu_code in total_ica_number_vertical:
                    cell = sheet.cell(
                        row=summary_row, column=col, value=total_ica_number_vertical[header_lu_code])
                    cell.alignment = Alignment(
                        wrap_text=True, horizontal='center', vertical='center')

                if header_lu_code in total_weightage_vertical:
                    cell = sheet.cell(
                        row=summary_row+1, column=col, value=total_weightage_vertical[header_lu_code])
                    cell.alignment = Alignment(
                        wrap_text=True, horizontal='center', vertical='center')

            # Adjust column widths
            for col in range(1, len(headers) + 1):
                column_letter = get_column_letter(col)
                if col == 1:
                    sheet.column_dimensions[column_letter].width = 5
                elif col == 2:
                    sheet.column_dimensions[column_letter].width = 15
                else:
                    sheet.column_dimensions[column_letter].width = 30

            # Apply borders
            for row in range(9, summary_row + 2):
                for col in range(1, len(headers) + 1):
                    cell = sheet.cell(row=row, column=col)
                    cell.border = thin_border
        else:
            sheet.merge_cells("A10:D10")
            sheet['A10'] = "No Data Found"
            sheet['A10'].alignment = Alignment(
                horizontal='center', vertical='center')

            for row in range(9, 11):
                for col in range(1, 5):
                    cell = sheet.cell(row=row, column=col)
                    cell.border = thin_border

    # Remove default sheet
    wb.remove(wb.active)

    if course == "all":
        # Get all unique courses for the given year of study
        courses = CourseLuMap.objects.filter(
            year_study=year_of_study,
            academic_year=SYSTEM_CURRENT_YEAR.current_year,
            semester=SYSTEM_CURRENT_YEAR.current_semester
        ).values_list('course_code__code', flat=True).distinct('course_code__code').order_by("course_code__code", "lu__code")

        for course_code in courses:
            courselumap = CourseLuMap.objects.filter(
                course_code__code=course_code,
                year_study=year_of_study,
                academic_year=SYSTEM_CURRENT_YEAR.current_year,
                semester=SYSTEM_CURRENT_YEAR.current_semester
            ).distinct().order_by('lu__code')

            if courselumap.exists():
                create_worksheet(wb, courselumap, f"{course_code}")
    else:
        courselumap = CourseLuMap.objects.filter(
            course_code__code=course,
            year_study=year_of_study,
            academic_year=SYSTEM_CURRENT_YEAR.current_year,
            semester=SYSTEM_CURRENT_YEAR.current_semester
        ).distinct().order_by('course_code__code', 'lu__code')

        create_worksheet(wb, courselumap, course)

    coursename = ""
    if course == "all":
        coursename = f"All Courses_Yr{year_of_study}"
    else:
        coursename = f"{course}_Yr{year_of_study}"

    file_name = coursename
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename={file_name}.xlsx'
    wb.save(response)

    report_delivery_email_template = REPORT_DELIVERY_EMAIL_TEMPLATE.format(
        name=request.user.first_name, report_name=file_name, support_email=get_SUPPORT_EMAIL())
    report_delivery_email_template = dedent(
        report_delivery_email_template).strip()
    return response, {"wb": wb,
                      "file_name": f"{file_name}.xlsx",
                      "email_subject": f"{file_name} - Report is ready for Download",
                      "email_body": report_delivery_email_template}


def print_lock_status(sheet, rows_to_check=5, cols_to_check=5):
    """
    How to use:
    print("\nAfter locking specific rows:")
    print_lock_status(sheet)
    """

    for row in range(1, rows_to_check + 1):
        for col in range(1, cols_to_check + 1):
            cell = sheet.cell(row=row, column=col)
            print(f"Cell {cell.coordinate} locked: {cell.protection.locked}")
