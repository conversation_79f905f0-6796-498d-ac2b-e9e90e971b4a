{% load static %}
{% load mathfilters %}
{% load custom_tags %}

<div class="row">    
    <div class="col-12">
        
        <div class="breadcrumb">
            <a href="/">Back</a> <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M7.07586 4.51043C7.18836 4.39807 7.34086 4.33496 7.49986 4.33496C7.65886 4.33496 7.81136 4.39807 7.92386 4.51043L11.3239 7.91043C11.4362 8.02293 11.4993 8.17543 11.4993 8.33443C11.4993 8.49343 11.4362 8.64593 11.3239 8.75843L7.92386 12.1584C7.81012 12.2644 7.65969 12.3221 7.50424 12.3194C7.3488 12.3166 7.2005 12.2537 7.09057 12.1437C6.98064 12.0338 6.91766 11.8855 6.91492 11.7301C6.91218 11.5746 6.96988 11.4242 7.07586 11.3104L10.0519 8.33443L7.07586 5.35843C6.9635 5.24593 6.90039 5.09343 6.90039 4.93443C6.90039 4.77543 6.9635 4.62293 7.07586 4.51043Z" fill="#BBBBBB"/>
                </svg>
            <span>Rule Configuration</span>
        </div> 

        
            <div class="content-system-management">
                <div id="user_management" class="card card-shadow card-padding">
                    <h2 class="title-section">System Management</h2>
                    <c-menu_system_management current_page="ruleConfiguration"></c-menu_system_management>
                    <h2 class="title-card text-black mb-4">Rule Configuration For ICA Schedule</h2>
                    <div class="form-check form-switch mb-4">
                        <input class="form-check-input" type="checkbox" role="switch" id="switch-ica-schedule" {% if storage_of_business_rule.status_admin_break_rule_stage1 %} checked {% endif %} name="switch-ica-schedule">
                        <label class="form-check-label" for="switch-ica-schedule">Allow admin to break rules</label>
                    </div>
                    <p class="fw-bold text-primary">When these condition are met..</p>
                    <div class="wrapper-rule-schedule">
                        <div id="conditions"></div>
                        <p class="fw-bold text-primary mb-2">Do these actions...</p>
                        <div id="actions"></div>
                        <div class="mt-4 d-flex align-items-center justify-content-start gap-3">
                            <button id="submit" form="rule-form" type='submit' class="btn btn-primary disabled btn-apply-rule btn-apply-rule__schedule">Apply Rules</button>
                            <button class="btn-text text-danger btn-cancel-rule disabled" hx-get="{% url 'system_management:ruleConfiguration' %}" hx-swap="innerHTML" hx-target=".container-content" hx-trigger="click">Cancel</button>
                        </div>
                    </div>
                    <hr class="my-5" />
                    <h2 class="title-card text-black mb-4">Rule Configuration For ICA Detail</h2>
                    <div class="form-check form-switch mb-4">
                        <input class="form-check-input" type="checkbox" role="switch" id="switch-ica-detail"  {% if storage_of_business_rule.status_admin_break_rule_stage2 %} checked {% endif %} name="switch-ica-detail">
                        <label class="form-check-label" for="switch-ica-detail">Allow admin to break rules</label>
                    </div>
                    <p class="fw-bold text-primary">When these condition are met..</p>
                    <div class="wrapper-rule-detail">
                        <div id="conditions-detail"></div>
                        <p class="fw-bold text-primary mb-2">Do these actions...</p>
                        <div id="actions-detail"></div>
                        <div class="mt-4 d-flex align-items-center justify-content-start gap-3">
                            <button id="submit-detail"  type='button' class="btn btn-primary disabled btn-apply-rule btn-apply-rule__detail">Apply Rules</button>
                            <button class="btn-text text-danger btn-cancel-rule disabled" hx-get="{% url 'system_management:ruleConfiguration' %}" hx-swap="innerHTML" hx-target=".container-content" hx-trigger="click">Cancel</button>
                        </div>
                    </div>
                </div>
                <button class="btn btn-primary show-popup mt-4" data-popup="lu-popup">Run test</button>
            </div>
    </div>
</div>
<c-popup class="popup-wide lu-popup">
    <div class="d-flex mb-4 justify-content-between align-items-center">
        <h2 class="mb-0">LU for running test</h2>
        <button class="btn-text close-popup" type="button"><img src="{% static 'img/icon-close.svg' %}" alt="close"></button>
    </div>
    <form id="rule-test" action="" method='POST'>
        <div class="form-group">
            <label for="lu-popup-input" class="text-start w-100">LU</label>
            
            <div class="wrapper-select">
                <select title="Learning Unit" class="select-lu form-control" name="learning_unit" >
                    <option value="">Select LU Code</option>
                    {% for lu in learning_units %}
                        <option value="{{lu.id}}">{{lu.code}}</option>
                    {% endfor %}       
                </select>
            </div>
        </div>
        <div class="d-flex align-items-center gap-3 justify-content-start mt-4">
            <button form="rule-test" name="run-test" type="submit" class="btn btn-primary close-popup">Run Test</button>
            <button type="button" class="btn-text text-danger close-popup" >Cancel </button>
        </div> 
    
</c-popup> 

 {% csrf_token %} 

<div class="rulefields" data-action="{{ rules_action }}" data-fields="{{ business_rule_fields }}" data-conditions="{{ conditions_ica_schedule }}" data-action-detail="{{ rules_action }}" data-fields-detail="{{ business_rule_fields }}" data-conditions-detail="{{ conditions_ica_detail }}"></div>

{% block script %}
    <script src="{% static 'js/rule-config.js' %}"></script>
    <script>
        jQuery(function($){
            $(document).ready(function(){
                $(".select-lu").select2({
                    placeholder: "Select LU Code",
                    width: "100%",
                });
                
                    
            })
        })

    </script>
{% endblock script %}
