# Generated by Django 5.1.4 on 2025-01-10 03:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('schedule_assessment', '0028_remove_ruledefinition_applied_to_categories_and_more'),
    ]

    operations = [
        migrations.<PERSON>er<PERSON>ield(
            model_name='scheduleassessment',
            name='digital_submission',
            field=models.CharField(choices=[('no', 'No'), ('yes-in-the-same-week-as-digital-submission', 'Yes, in the same week as digital submission'), ('yes-in-the-following-week-after-digital-submission', 'Yes, in the following week after digital submission')], max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='scheduleassessmenttest',
            name='digital_submission',
            field=models.Char<PERSON>ield(choices=[('no', 'No'), ('yes-in-the-same-week-as-digital-submission', 'Yes, in the same week as digital submission'), ('yes-in-the-following-week-after-digital-submission', 'Yes, in the following week after digital submission')], max_length=255, null=True),
        ),
    ]
