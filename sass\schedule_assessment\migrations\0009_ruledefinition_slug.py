# Generated by Django 5.1 on 2024-11-15 06:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("schedule_assessment", "0008_businessrules"),
    ]

    def update_rule_data(apps, schema_editor):
        ScheduleAssessment = apps.get_model('schedule_assessment', 'ScheduleAssessment')
        schedule_assessements = ScheduleAssessment.objects.all()
        for schedule_assessement in schedule_assessements:
            schedule_assessement.save()

    operations = [
        migrations.AddField(
            model_name="ruledefinition",
            name="slug",
            field=models.SlugField(default="", max_length=255, unique=True),
        ),
        migrations.RunPython(update_rule_data),
    ]
