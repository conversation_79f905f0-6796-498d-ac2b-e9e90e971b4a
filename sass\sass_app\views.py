from django.shortcuts import render
from django.http import HttpResponse
import requests
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.core.paginator import Paginator
from business_rules import export_rule_data
from schedule_assessment.rule import *
from system_management.models import *
from django.db.models import Q
from utility import check_permission, check_access, find_lu_course_group
from django.db.models import Prefetch
from easyaudit.models import CRUDEvent
from django.contrib.contenttypes.models import ContentType
from collections import defaultdict
# Create your views here.
POSTS_PER_PAGE = 5
POSTS_PER_PAGE_BEGIN = POSTS_PER_PAGE - 1


@login_required
@check_access()
def main(request):

    system_current_year = SystemCurrentYear.objects.first()

    search_lu_code = request.GET.get('search-lu-code', None)
    filter_condition = Q(
        courselumap__academic_year=system_current_year.current_year,
        courselumap__semester=system_current_year.current_semester
    )
    if search_lu_code:
        filter_condition &= Q(code__icontains=search_lu_code)

    learning_units = LearningUnit.objects.filter(filter_condition).prefetch_related(
        Prefetch(
            'statuslearningunit_set',
            queryset=StatusLearningUnit.objects.filter(
                current_year=system_current_year.current_year,
                current_semester=system_current_year.current_semester
            ).select_related('updated_by')  # If you need user information
            .order_by('-created_at'),  # Order by creation date
            to_attr='status_units'
        )
    ).distinct('code').order_by('code')

    paginator = Paginator(learning_units, POSTS_PER_PAGE)
    page = request.GET.get('page', 1)
    if page:
        page_content = paginator.page(page)
        learning_units = page_content.object_list
    else:
        page_content = paginator.page(1)
    paginator = Paginator(learning_units, POSTS_PER_PAGE)

    

    permission = {
        'system_management': check_permission(request.user, 'system_management'),
        'live_schedule_status': check_permission(request.user, 'live_schedule_status'),
        'schedule_assessement': check_permission(request.user, 'schedule_assessement'),
        'report': check_permission(request.user, 'report'),
    }

    context = {"system_current_year": system_current_year,
               "learning_units": learning_units,
               "search_lu_code": search_lu_code,

               "page": page,
               "page_content": page_content,
               
               "paginator": paginator,
               
               "permission": permission}
    
    response = render(request, 'landing.html', context)
    return response



def heatmap_container(request):

    selected_filter_departement_codes = request.GET.getlist('filter-department', [])
    
    system_current_year = SystemCurrentYear.objects.first()

    departments = Department.objects.all().order_by('name')

    lu_code_filter_condition = Q(courselumap__academic_year=system_current_year.current_year, courselumap__semester=system_current_year.current_semester)
    if selected_filter_departement_codes:
        lu_code_filter_condition &=Q(department__code__in = selected_filter_departement_codes) 
    
    lu_codes = LearningUnit.objects.filter(lu_code_filter_condition).values('code').distinct('code')    
    context={
        "system_current_year": system_current_year,
        "lu_codes":lu_codes,
        "departments": departments,
        "selected_filter_departement_codes": selected_filter_departement_codes,
        "selected_filter_lu_codes" : request.GET.getlist('filter-learning-unit', [])
    }
    return render(request, 'heatmap-container.html', context)


def heatmap(request):
    SYSTEM_CURRENT_YEAR = SystemCurrentYear.objects.first()

    filter_department = request.GET.getlist("filter-department", None)
    filter_learning_unit = request.GET.getlist("filter-learning-unit", None)

    filter_conditions = Q(lu_type__in=AcademicWeek.objects.values_list('lutype', flat=True).distinct('lutype'),
                          course_type__in=AcademicWeek.objects.values_list('coursetype', flat=True).distinct('coursetype'),
                          courselumap__academic_year=SYSTEM_CURRENT_YEAR.current_year,
                          courselumap__semester=SYSTEM_CURRENT_YEAR.current_semester
                          )

    if filter_learning_unit:
        filter_conditions &= Q(code__in=filter_learning_unit)

    if filter_department:
        filter_conditions &= Q(department__code__in=filter_department)

    learnit_unit_and_academic_weeks = LearningUnit.objects.filter(
        filter_conditions
    ).values(
        'code',
        'lu_type',
        'course_type'
    ).distinct('code').annotate(
        weeks=models.Subquery(
            AcademicWeek.objects.filter(
                lutype=models.OuterRef('lu_type'),
                coursetype=models.OuterRef('course_type'),
            ).values('weeks')[:1]
        ), 
        maxicacountperweek=models.Subquery(
            AcademicWeek.objects.filter(
                lutype=models.OuterRef('lu_type'),
                coursetype=models.OuterRef('course_type'),
            ).values('maxicacountperweek')[:1]
        ),
    )

    course_groups = {
        lu_aw['code']: [
            {
                'course_code': group['course_code__code'],
                'year_of_study': group['year_study'],
                'lu_codes': group['lu_codes']
            }
            for group in find_lu_course_group(lu_aw['code'])
        ]
        for lu_aw in learnit_unit_and_academic_weeks
    }

    learnit_unit_and_academic_week_general = learnit_unit_and_academic_weeks[0]
    start = learnit_unit_and_academic_week_general['weeks']['start']
    end = learnit_unit_and_academic_week_general['weeks']['end']
    weeks_range = range(start, end + 1)
    
    # Process learning units
    learning_unit_weeks = []
    for learnit_unit_and_academic_week in learnit_unit_and_academic_weeks:

        if not learnit_unit_and_academic_week['weeks']:
            continue
        
        max_week_counts = {}
        for course_ in course_groups[learnit_unit_and_academic_week['code']]:
            count_by_week = ScheduleAssessment.objects.filter(
                    lu__code__in=course_['lu_codes'],
                    week__in=weeks_range,
                    current_year=SYSTEM_CURRENT_YEAR.current_year,
                    current_semester=SYSTEM_CURRENT_YEAR.current_semester
                ).values('week').annotate(count=Count('id')).order_by('week')   
            
            # Convert to dictionary format
            for item in count_by_week:
                week = item['week']
                count = item['count']
                # Update if week not in dict or if new count is higher
                if week not in max_week_counts or count > max_week_counts[week]:
                    max_week_counts[week] = count
        
        lu_code = learnit_unit_and_academic_week['code']
    
        learning_unit_weeks.append({
            "code": lu_code,  # Use composite key as the code
            "original_code": lu_code,  # Keep original code for reference
            "lu_type": learnit_unit_and_academic_week['lu_type'],
            "course_type": learnit_unit_and_academic_week['course_type'],
            "calendar": elaborate_week(learnit_unit_and_academic_week, max_week_counts)
        })

    context = {
        "learning_unit_weeks": learning_unit_weeks,
        "system_current_year": SYSTEM_CURRENT_YEAR,
        "lu_codes": LearningUnit.objects.filter(
            courselumap__academic_year=SYSTEM_CURRENT_YEAR.current_year, 
            courselumap__semester=SYSTEM_CURRENT_YEAR.current_semester
        ).values('code').distinct('code'),

        "filter_learning_unit": filter_learning_unit,
        "filter_department": filter_department,
    }

    return render(request, 'heatmap.html', context)


def elaborate_week(learnit_unit_and_academic_week: dict, max_week_counts: dict):
    """
    Elaborates the weekly status for a learning unit with composite key.
    
    Args:
        learnit_unit_and_academic_week: Dictionary containing learning unit and academic week data
        ica_count_in_week: Dictionary containing ica in lu counts by week
        
    Returns:
        List of dictionaries with week numbers and status
    """
    start = learnit_unit_and_academic_week['weeks']['start']
    end = learnit_unit_and_academic_week['weeks']['end']
    status_dict = learnit_unit_and_academic_week['weeks'].get('status', {})
    maxicacountperweek = learnit_unit_and_academic_week['maxicacountperweek']

    # print("learnit_unit_and_academic_week['code']: ", learnit_unit_and_academic_week['code'])

    result = []

    # Create array for all weeks from start to end
    for week in range(start, end + 1):
        # Convert week number to string to match with status_dict keys
        week_str = str(week)

        # Get count of ica in lu for the week
        # count = ica_count_in_week.get(week, {}).get(learnit_unit_and_academic_week['code'], 0)
        count=0
        if week in max_week_counts:
            count = max_week_counts[week]

        # Determine status based on count
        if week_str in status_dict:
            # Use predefined status if available
            status = status_dict[week_str].lower()
        else:
            # Determine status based on count
            if count == 0 or maxicacountperweek == 0:
                status = "available"
            elif count >= maxicacountperweek:
                status = "unavailable"
            else:
                status = "limited"
        
        result.append({
            "week": week,
            "status": status,
            "count": count
        })

    return result


def health(request):
    return JsonResponse({
        'success': True,
        'message': 'System Health, Ready to Run',
    }, status=200)


def get_activity_log(request, id):

    learning_unit = LearningUnit.objects.get(id=id)

    crud_events = CRUDEvent.objects.filter(
        Q(content_type=ContentType.objects.get(model='ScheduleAssessment'.lower()), object_json_repr__icontains=f'"lu": "{learning_unit.id}"') |
        Q(content_type=ContentType.objects.filter(model='LearningUnit'.lower()).last(), object_json_repr__icontains=f'"id": "{learning_unit.id}"') |
        Q(content_type=ContentType.objects.filter(model='StatusLearningUnit'.lower()).last(), object_json_repr__icontains=f'"lu": "{learning_unit.id}"')).order_by('-datetime')

    paginator = Paginator(crud_events, POSTS_PER_PAGE)
    page = request.GET.get('page', 1)
    if page:
        page_content = paginator.page(page)
        crud_events = page_content.object_list
    else:
        page_content = paginator.page(1)

    context = {
        'learning_unit': learning_unit,
        "activity_logs": crud_events,
        "page": page,
        'page_content': page_content,
        'paginator': paginator,
    }
    return render(request, 'get-activity-log.html', context)
