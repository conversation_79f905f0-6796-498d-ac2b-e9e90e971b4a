{% load static %}
{% load mathfilters %}
{% load custom_tags %}

<p class="fw-bold text-start mt-4">LU CODE {{learning_unit.code}}</p>
<div class="table-responsive table-activity-log-popup">
    <table class="table-sass table table-borderless">
        <thead class="bg-gray text-black sticky-top">
            <tr>                            
                <th>TIMESTAMP</th>
                <th>EMAIL </th>
                <th>DEPARTMENT </th>
                <th>LU CODE </th>
                <th>MODULE </th>
                <th>ACTION </th>
                <th>DETAILS OF CHANGE </th>
            </tr>
        </thead>
        <tbody>
            {% for activity_log in activity_logs %}
                <tr>                           
                    <td>{{activity_log.datetime|date:"d-M-Y H:i"}}</td>
                    <td>{{activity_log.user.email}}</td>
                    <td>{{activity_log|get_department}} </td>
                    <td>{{activity_log|get_lu_code}} </td>
                    <td>{{activity_log.content_type.model|title }} </td>
                    <td>{{activity_log.get_event_type_display }} </td>
                    <td>{{activity_log|clean_changed_fields}} </td>
                </tr>
            {% empty %}
                <tr>
                    <td colspan="7" class="py-4 text-center">No log found.</td>
                </tr>
            {% endfor %}                                                    
        </tbody>
    </table>
</div>
<div class="d-flex justify-content-center">
    {% if page_content.paginator.num_pages > 1 %}
        {% with total_pages=page_content.paginator.num_pages %}
            <div class="pagination mt-3">
                {% for num in page_content.paginator.page_range %}
                    {% if num == 1 or num == total_pages %}
                        <a hx-get="{% url 'sass_app:getActivityLog' learning_unit.id %}?page={{num}}" class="page-link cursor-pointer {% if num == page_content.number %} active {% endif %}" hx-trigger="click" hx-swap="innerHTML" hx-target="#activity-log" hx-indicator=".loading-schedule-assessment">{{ num }}</a>
                    {% elif num|sub:page_content.number >= -1 and num|sub:page_content.number <= 1  %}
                        <a hx-get="{% url 'sass_app:getActivityLog' learning_unit.id %}?page={{num}}" class="page-link cursor-pointer {% if num == page_content.number %} active {% endif %}" hx-trigger="click" hx-swap="innerHTML" hx-target="#activity-log" hx-indicator=".loading-schedule-assessment">{{ num }}</a>
                    {% elif num|sub:page_content.number > -3 and num|sub:page_content.number < 3  %}
                        <span class="page-link">...</span>
                    {% endif %} 
                {% endfor %}
            </div>
        {% endwith %}
    {% endif %}
</div>