{% load static %}
{% load custom_tags %}
<div class="card card-shadow card-padding mt-5" id="card-ica-schedule">
    
    <div class="tab-ica {% if test_mode %}tab-ica-test{% endif %}">
        <a class="active btn-ica-schedule" href="{% url 'schedule_assessment:icaSchedule' learning_unit.id %}?redirect_to=ica-schedule{% if test_mode %}&test_mode=true{% endif %}">ICA Schedule</a> 
        <a href="{% url 'schedule_assessment:icaSchedule' learning_unit.id %}?redirect_to=ica-detail{% if test_mode %}&test_mode=true{% endif %}" class="btn-ica-detail">ICA Detail</a>
    </div> 
        <form id="ica-item" action="{% url 'schedule_assessment:reserved_assessement' %}" method="POST" class="input">
            
            <input type="hidden" name="learning_unit_id" value="{{learning_unit.id}}" />

            {% get_weeks_of_max_ica learning_unit as weeks_of_max_ica %}

            {% if weeks_of_max_ica %}<input type="hidden" name="weeks_of_max_ica" value="{{weeks_of_max_ica}}" />{% endif %}
            
            {% if test_mode %}<input type="hidden" name="test_mode" value="{{test_mode}}" />{% endif %}
                            
            <div class="container-ica">
                {% if not test_mode %}
                    <div class="timer-countdown mb-4 d-none">
                        <p class="text-danger fst-italic">Time Remaining: [<span class="countdown"></span>]</p>
                        <p class="fst-italic">Please complete the schedule within 10 minutes. Weeks may become unavailable if not submitted in time.</p>
                    </div>                
                {% endif %}

                <div class="inner-card no-ica d-none">
                    <svg width="49" height="49" viewBox="0 0 49 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M43.8738 12.3491L24.4293 3.16462C24.2416 3.07706 24.0367 3.03284 23.8296 3.03525C23.6226 3.03766 23.4187 3.08664 23.2331 3.17855C23.0476 3.27046 22.8851 3.40296 22.7577 3.56621C22.6303 3.72946 22.5413 3.91929 22.4973 4.12162C22.4928 4.13662 20.9373 11.3666 18.6273 18.5741C17.8398 21.0446 16.9578 23.4866 16.0368 25.6316L18.5343 26.8106C21.5343 19.8476 23.9103 10.2356 24.8043 6.39112L41.7048 14.3636C41.1858 16.6331 39.8553 22.1981 38.0748 27.7541C36.9243 31.3541 35.5773 34.9496 34.1973 37.5521C32.8623 40.2371 31.2603 41.5571 30.9018 41.3801C28.7718 41.4251 27.2748 39.4871 26.2668 37.0031C25.8428 35.9103 25.5131 34.7832 25.2813 33.6341C25.0893 32.7071 25.0308 32.1251 25.0263 32.1251C25.0032 31.8904 24.9191 31.6657 24.7823 31.4736C24.6454 31.2815 24.4606 31.1286 24.2463 31.0301L4.80627 21.8501C4.58493 21.745 4.33961 21.7007 4.0955 21.7218C3.85139 21.7429 3.61729 21.8286 3.41727 21.9701C3.21815 22.1123 3.06043 22.305 2.96027 22.5282C2.8601 22.7515 2.82111 22.9973 2.84727 23.2406C2.85927 23.3096 3.13977 26.0156 4.26777 28.8461C5.05077 30.8261 6.37826 33.0731 8.53676 34.2086L27.9708 43.4036L27.9798 43.3856C28.8228 43.8281 29.7798 44.1161 30.9003 44.1296C33.5958 43.9541 35.0658 41.6276 36.6273 38.8421C41.1273 30.3071 44.6073 13.9421 44.6343 13.8821C44.7009 13.5791 44.6623 13.2625 44.5248 12.9844C44.3874 12.7063 44.1594 12.4833 43.8783 12.3521L43.8738 12.3491Z" fill="#BBBBBB"/>
                    </svg>
                    <p>No scheduled ICA yet</p>
                    <p>Click add ICA to create schedule</p>            
                </div>   
                
                <!-- Item card schedule -->
                {% for schedule_assessment in schedule_assessments %} 
                    <div id="form-{{schedule_assessment.id}}" 
                        hx-trigger="change from:#form-{{schedule_assessment.id}} :input" 
                        hx-swap='none' 
                        hx-post="{% url 'schedule_assessment:update_ica_schedule' %}" 
                        class="input">
                        
                        {% csrf_token %}

                        <div class="card item-ica-card mb-4" id="{{schedule_assessment.id}}">
                            
                            <input type="hidden" name="schedule_assessment" value="{{schedule_assessment.id}}" />
                            <input type="hidden" name="moderation_method" value="{{ schedule_assessment.moderation_method }}"/>
                            <input type="hidden" name="duration" value="{{ schedule_assessment.duration }}"/>
                            <input type="hidden" name="assessment_type" value="{{ schedule_assessment.assessment_type }}"/>     
                            <input type="hidden" name="learning_unit_id" value="{{learning_unit.id}}"></input>               
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <h2 class="title-card {% if test_mode %}title-card-test{% endif %}">ICA <span class="number-ica"></span> <span class="value-weightage">(0%)</span></h2>
                              
                                {% if is_edit and permission.delete %}
                                    <button data-id="{{ learning_unit.id }}"  data-url="{% url 'schedule_assessment:delete_ica' schedule_assessment.id %}" 
                                    type="button" class="btn-text show-confirm-delete-popup">
                                    <img src="{% static 'img/icon-trash.svg' %}" alt="trash"> 
                                      
                                    </button> 
                                {% endif %}
                            </div>
                            
                            <div class="outer-ica-schedule">
                                <div class="item-schedule">
                                    <div class="form-group">
                                        <label class="text-gray has-tooltips">Week
                                            <div class="tooltips">
                                                <img src="{% static 'img/icon-info.svg' %}" alt="info">
                                                <div class="tooltips-content ">
            
                                                    {% get_tooltip 'ica-week' as tooltips %}
                                                    
                                                    <p>{{tooltips|clean_text}}</p>
            
                                                </div>
                                            </div>
                                        </label>
                                        <div class="wrapper-select form-ica-schedule ica-disabled">
                                            <select name="week" id="week-{{schedule_assessment.id}}" class="form-control week" 
                                            {% comment %} onchange='assm_methodSubmit(document.getElementById("assm-method-{{schedule_assessment.id}}"))' {% endcomment %}
                                            >
                                                <option value="">Not Reserved </option>
                                                {% get_valid_week schedule_assessment learning_unit as valid_weeks %}
            
                                                {% for valid_week in valid_weeks %}
                                                    {% if not valid_week.week in weeks_of_max_ica or schedule_assessment.week in weeks_of_max_ica %}
                                                    <option value="{{valid_week.week}}" {% if schedule_assessment.week == valid_week.week %} selected {% endif %}>{{valid_week.week}} ({{valid_week.date|date:"d-M-Y"}}) </option>
                                                    {% endif %}
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="item-schedule">
                                    <div class="form-group">
                                        <label class="text-gray has-tooltips">Assm. Weightage
                                            <div class="tooltips">
                                                <img src="{% static 'img/icon-info.svg' %}" alt="info">
                                                <div class="tooltips-content align-right">
            
                                                    {% get_tooltip 'ica-assm-weightage' as tooltips %}
                                                    
                                                    <p>{{tooltips|clean_text }}</p>
            
                                                </div>
                                            </div>
                                        </label>
                                        <div class="form-ica-schedule ica-disabled wrapper-percent">
                                            <input name="weightage" type="number" class="form-control input-weightage" value="{{schedule_assessment.weightage}}" inputmode="numeric" oninput="this.value = this.value.replace(/\D+/g, '')"
                                            {% comment %} onchange="customSubmit()" {% endcomment %}
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div class="item-schedule">
                                    <div class="form-group">
                                        <label class="text-gray has-tooltips">Over 2 weeks
                                            <div class="tooltips">
                                                <img src="{% static 'img/icon-info.svg' %}" alt="info">
                                                <div class="tooltips-content align-right">
            
                                                    {% get_tooltip 'ica-over2week' as tooltips %}
                                                    
                                                    <p>{{tooltips|clean_text}}</p>
            
                                                </div>
                                            </div>
                                        </label>                            
                                        <div class="wrapper-select form-ica-schedule ica-disabled">
                                           
                                            <select name="assm-2-weeks" class="form-control">
                                            <option value="">--</option>
                                            <option value="no" {% if schedule_assessment.over_two_weeks == 'no' %}selected{% endif %}>No</option>
                                            <option value="yes" {% if schedule_assessment.over_two_weeks == 'yes' %}selected{% endif %}>Yes</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="item-schedule">
                                    <div class="form-group">
                                        <label class="text-gray has-tooltips">Assm. Method
                                            <div class="tooltips">
                                                <img src="{% static 'img/icon-info.svg' %}" alt="info">
                                                <div class="tooltips-content align-right">
            
                                                    {% get_tooltip 'ica-assm-method' as tooltips %}
                                                    
                                                    <p>{{tooltips|clean_text}}</p>
            
                                                </div>
                                            </div>
                                        </label>
                                        <div class="wrapper-select form-ica-schedule ica-disabled">
                                            <select id="assm-method-{{schedule_assessment.id}}" name="assm-method" class="form-control">
                                                {% for assessement in assessements %}
                                                <option value="{{assessement.slug}}" {% if schedule_assessment.assessment_method == assessement %}selected{% endif %}>{{assessement.method}}</option>
                                                {% endfor %}

                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="item-schedule">
                                    <div id="assm-task-{{schedule_assessment.id}}" class="form-group">
                                        <p class="text-gray">Assm. Task</p>
                                        <input type="text" id="assm-task" class="form-control" disabled {% if schedule_assessment.assessment_task %}  value="{{schedule_assessment.assessment_task}}" {% else %} value="PRES1" {% endif %} />
                                    </div>
                                </div>

                            </div>

                            {% if schedule_assessment.broken_rules_stage1 %}
                
                                <div  role="alert" class="mb-4 mb-0 fade d-flex align-items-center alert alert-danger show sgds">
                                    <div class="d-flex align-items-center justify-content-start">
                                        <div id="message-specific-lu-message-{{schedule_assessment.id}}" >
                                            {% for failed_message in schedule_assessment.broken_rules_stage1.failed_conditions %}
                                            <div>
                                                {% if failed_message.name == "context_error" %}
                                                    {{forloop.counter}}. {{failed_message.message|title}} | Please Check Rule Configuration
                                                {% elif failed_message.name %}
                                             
                                                    {{forloop.counter}}. Rule: {{failed_message.name|rule_error_cleanup_message}} | 
                                                    Input: {{failed_message.actual|check_dict|title}} | 

                                                    Expected Input: ({{failed_message.operator|rule_error_cleanup_message|upper}}) {{failed_message.expected|title}}

                                           
                                                {% else %}
                                             
                                                    {{forloop.counter}}. {{failed_message}}
                                                
                                                {% endif %}
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                    
                            {% endif %}

                        </div>

                    </div>
                {% endfor %}  
                
            </div>

        </form>
        
        {% get_total_weightage learning_unit as total_weightage %}
       
        {% comment %} {% if count_error_stage1 < 1 %} {% endcomment %}
        <div role="alert" class="{% if total_weightage == 100  %}d-none{% endif %} warning-weightage mt-4 mb-0 fade d-flex align-items-center alert alert-warning show sgds">
            <div class="d-flex align-items-center justify-content-start gap-2">
                <img src="{% static 'img/icon-warning.svg' %}" alt="Warning">
                <div class="message_weightage">
                    {% if total_weightage < 100 %}
                        Total weightage not allow less than 100%
                    {% elif total_weightage > 100 %}
                        Total weightage not allow greater than 100%
                    {% endif %}            
                </div>
            </div>
        </div>
        {% comment %} {% endif %}             {% endcomment %}
        
    
    
    <div class="loading loading-ica">
        <img src="{% static 'img/loading.gif' %}" alt="loading">
    </div>
    <div class="d-flex align-items-center justify-content-start mt-4">
        
        {% if is_edit and permission.create %}
            <button type="button"  class="btn-text add-ica d-none"  >
                <svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1.6665 7.33431H13.3332M7.49984 1.50098V13.1676" stroke="#787676" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                </svg> Add ICA
            </button>
        {% endif %}
            
        <button type='button' data-popup="popup-log" class="show-popup btn-text text-decoration-none"
        
            hx-get="{% url 'sass_app:getActivityLog' learning_unit.id %}" 
            hx-target="#activity-log" 
            hx-swap="innerHTML" 
            hx-trigger='click'
        
        ><svg width="17" height="13" viewBox="0 0 17 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9.33333 6.33398H16M9.33333 11.334H16M9.33333 1.33398H16M1 6.33398H1.83333M1 11.334H1.83333M1 1.33398H1.83333M5.16667 6.33398H6M5.16667 11.334H6M5.16667 1.33398H6" stroke="#787676" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
            </svg> 
            
            See change log
        </button>           
    </div>                
</div>
{% if is_edit and permission.update %}
    <div class="mt-4 d-flex gap-3 align-items-center">
        <button type="button" id="btn-edit-schedule"  class="btn btn-edit-schedule show-popup {% if test_mode %}btn-black{% else %}btn-primary{% endif %}" data-popup="popup-warning-edit">Edit Schedule</button>
        <button form="ica-item" type="submit" class="btn btn-submit-ica disabled d-none {% if test_mode %}btn-black{% else %}btn-primary{% endif %}">Submit</button>    
        <button type="button" class="btn-text text-danger fw-bold btn-cancel-ica d-none">Cancel</button>     
    </div>    
{% endif %}
<c-popup title="Warning: Editing May Affect Selection Availabilty" class="popup-warning-edit" >
    <p>Some of the weeks you have previously selected may no longer be available after entering edit mode. </p>
    <p>Would you like to proceed?</p>
    <div class="d-flex align-items-center gap-3 mt-4 justify-content-center">                      
        <button type="button" class="btn btn-primary close-popup proceed-edit">Proceed</button>
        <button type="button" class="btn-text text-danger fw-bold close-popup">Cancel</button>                       
    </div>
</c-popup>
<div id="temp" class="d-none"></div>

<c-popup title="Confirmation" class="popup-confirm-delete" >
    <img src="{% static 'img/sassy-sad.png' %}" alt="sassy shock" class="mx-auto mb-2">
    <p>Are you sure you want to delete this item?</p>
    <div class="d-flex align-items-center gap-3 mt-4 justify-content-center">                      
        <button type="button" class="btn btn-primary close-popup remove-item-ica" >Proceed</button>
        <button type="button" class="btn-text text-danger fw-bold close-popup cancel-delete">Cancel</button>                       
    </div>
</c-popup>

<c-popup class="popup-log" buttonLeft>
    
    <div class="d-flex justify-content-between align-items-center">
        <h2 class="mb-0">Activity Log</h2>
        <button class="btn-text close-popup" type="button"><img src="{% static 'img/icon-close.svg' %}" alt="close"></button>
    </div>
    <div id="activity-log"></div>
</c-popup>

{% if is_from_submit == "true" %}
    <div class="popup popup-wide">
        <div class="backdrop"></div>
            <div class="content-popup content-popup-submission_ica-schedule"> 
                            
                {% if count_error_stage1 > 0 %}
                    <h2>Error!</h2>
                    <img src="{% static '/img/sassy-sad.png' %}" alt="sassy sad">
                    <p class="text-danger">Something went wrong, please check again.</p>
                {% else %}
                    <h2>Success!</h2>                   
                    <img src="{% static '/img/sassy-happy.png' %}" alt="sassy happy">
                    {% get_tooltip 'ica-schedule-save-success' as tooltips %}
                    <p>{{tooltips|clean_text }}</p>
                {% endif %}
            
                <button hx-get="{% url 'schedule_assessment:icaSchedule' learning_unit.id %}?is_from_submit=false" hx-select=".content-card-ica" hx-target=".content-card-ica" hx-swap="outerHTML" class="btn btn-primary btn-popup-ica-schedule__submission close-popup mt-4">Okay!</button>
            </div>
        </div>  
    </div>      
{% endif %}


<script>
    $(document).ready(function(){
        let countIca = $(".item-ica-card").length;
        if (countIca > 0) {
            // $(".status-assessment").addClass("d-none");
            $(".no-ica").addClass("d-none");
            $('.btn-edit-schedule').removeClass('d-none')
        } else {
            $('.btn-edit-schedule').addClass('d-none')
            // $(".status-assessment").removeClass("d-none");
            $(".no-ica").removeClass("d-none");
        }
        $('.value-weightage').each(function(){   
            let valueWeightage = $(this).parents(".item-ica-card").find(".input-weightage").val();
            $(this).text("(" + valueWeightage + "%)");
        })
    });
    
    function assm_methodSubmit(elm){
        htmx.trigger(elm,'htmx-changed')
        document.body.addEventListener("htmx:afterRequest", function (evt) {
            $(this).parents('.outer-ica-schedule').find(".label-assm-task").removeClass("text-gray");        
        })
        
    }
    setTimeout(() => {
        let credit = $(".content-card-ica").data("credit");
        let countIca = $(".item-ica-card").length;

        if (countIca == parseInt(credit) + 1) {
            $(".add-ica").addClass("disabled");
        }
        
        if (countIca == 0) {
            $(".no-ica, .add-ica").removeClass("d-none");
            $(".add-ica").removeClass("disabled");
            $(
                ".timer-countdown, .btn-submit-ica, .btn-edit-schedule"
            ).addClass("d-none");
            clearInterval(timerInterval);
            $(".countdown").html("00:00");
        }
    }, 300);

</script>

