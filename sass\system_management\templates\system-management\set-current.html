{% load static %}
<div class="row">
    <div class="col-12">
        <div class="breadcrumb">
            <a href="/">Back</a> <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M7.07586 4.51043C7.18836 4.39807 7.34086 4.33496 7.49986 4.33496C7.65886 4.33496 7.81136 4.39807 7.92386 4.51043L11.3239 7.91043C11.4362 8.02293 11.4993 8.17543 11.4993 8.33443C11.4993 8.49343 11.4362 8.64593 11.3239 8.75843L7.92386 12.1584C7.81012 12.2644 7.65969 12.3221 7.50424 12.3194C7.3488 12.3166 7.2005 12.2537 7.09057 12.1437C6.98064 12.0338 6.91766 11.8855 6.91492 11.7301C6.91218 11.5746 6.96988 11.4242 7.07586 11.3104L10.0519 8.33443L7.07586 5.35843C6.9635 5.24593 6.90039 5.09343 6.90039 4.93443C6.90039 4.77543 6.9635 4.62293 7.07586 4.51043Z" fill="#BBBBBB"/>
                </svg>
            <span>Set Current Year & Edit Mode</span>
        </div>  
        <div class="content-system-management">
            <div class="card card-set-current card-shadow card-padding">
                <h2 class="title-section">System Management</h2>
                <c-menu_system_management current_page="setCurrent"></c-menu_system_management>
                <h2 class="title-card text-black mb-4">Set Current Year & Edit Mode</h2>
                {% if permission.update %} 
                <div class="d-flex flex-column flex-md-row align-items-md-center gap-4 justify-content-start menu-user-management">   
                    <button type="button" class="btn-text v-center p-0 btn-edit-setcurrent {% if editMode %}disabled{% endif %}" hx-get="{% url 'system_management:setCurrent' %}" hx-vals='{"editMode":"true"}' hx-target=".container-content" hx-swap="innerHTML"><img class="icon-image" src="{% static 'img/icon-pencil.svg' %}" alt="bulk"> Edit</button>
                </div>
                {% endif %}
                <form id="form-set-current" class="form-set-current" hx-post="{% url 'system_management:update_set_current' %}" hx-trigger="submit" hx-swap="outerHTML" hx-target=".content-system-management" hx-select=".content-system-management">
                    {% csrf_token %}
                    <div class="mt-4">
                        <div class="row">
                            <div class="col-lg-6">
                                <p class="fw-bold text-primary mb-2">Set Current Year</p>
                                <div class="ps-3">
                                    <div class="form-group">
                                        <label for="current-year">Current year</label>
                                        <div class="wrapper-calendar">
                                            {% if not editMode %}
                                            <input type="text" name="current-year" id="year-study" class="form-control disabled" placeholder="Select Current Year" {% if system_current_year.current_year %} value="{{system_current_year.current_year}}" {% endif %}  title="yearPicker"/>
                                            {% else %}
                                            <select type="text" name="current-year" id="year-study"  class="form-control current-year " placeholder="Select Current Year" {% if system_current_year.current_year %} value="{{system_current_year.current_year}}" {% endif %}  title="yearPicker"></select>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="selected-current-semester">Current semester</label>
                                        <div class="wrapper-select">
                                            {% if editMode %}
                                            <select type="text" name="current-semester" id="current-semester" class="form-control" placeholder="Select Semester">
                                                <option value="1" {% if system_current_year.current_semester == 1 %} selected {% endif %}>1</option>
                                                <option value="2" {% if system_current_year.current_semester == 2 %} selected {% endif %}>2</option>
                                                
                                            </select>
                                            {% else %}
                                            <input readonly type="text" name="selected-current-semester" id="selected-current-semester" class="form-control disabled" placeholder="Select Semester" {% if system_current_year.current_semester %} value="{{system_current_year.current_semester}}" {% endif %}>
                                            {% endif %}
                                        
                                        </div>
                                    </div>
                                </div>             
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <p class="fw-bold text-primary mb-2 mt-4">Set Edit Mode</p>
                                <div class="table-responsive ">
                                    <table class="table-sass table table-borderless {% if editMode %}table-set-edit-mode{% endif %}">
                                        <thead class="bg-gray text-black">
                                            <tr>                                
                                                <th>LU PHASE</th>
                                                <th>EDIT MODE <p class="small fst-italic">Apply between start & end date</p> </th>
                                                <th>START DATE <p class="small fst-italic">Leave blank to start immediately</p> </th>
                                                <th>END DATE <p class="small fst-italic">Leave blank to keep it running</p> </th>
                                                <th>EDIT MODE AFTER <p class="small fst-italic">Available when an end date is set</p> </th>
                                            </tr>
                                        </thead>
                        
                                        <tbody>
                                            {% for systemeditmode in systemeditmodes%}
                                            
                                            <input hidden name="systemeditmode" value="{{systemeditmode.id}}"></input>

                                            <tr>      
                                                <td> Group {{systemeditmode.group}}</td>
                                                <td class="edit-mode"> {{systemeditmode.get_mode_display}}</td>
                                                <td class="edit-mode"> {% if systemeditmode.start %} {{systemeditmode.start|date:"d-M-Y H:i"}} {% else %} DD-MM-YYYY  00:00  {% endif %}</td>
                                                <td class="edit-mode"> {% if systemeditmode.end %} {{systemeditmode.end|date:"d-M-Y H:i"}} {% else %} DD-MM-YYYY  00:00  {% endif %}</td>
                                                <td class="edit-mode"> {{systemeditmode.get_edit_after_display}}</td>
                                                
                                                    {% if editMode %}
                                                        <td class="form-edit-mode d-none">    
                                                            <div class="wrapper-select form-edit-mode">
                                                                <select name="edit-mode" title="edit-mode" class="form-control-edit-mode">
                                                                    <option value="on" {% if systemeditmode.mode == "on" %}selected{% endif %}>On</option>
                                                                    <option value="interim" {% if systemeditmode.mode == "interim" %}selected{% endif %}>Interim</option>
                                                                    <option value="off" {% if systemeditmode.mode == "off" %}selected{% endif %}>Off</option>
                                                                </select>
                                                            </div>
                                                        </td>
                                                        <td class="form-edit-mode d-none">   
                                                            <div class="wrapper-calendar form-edit-mode">
                                                                <input autocomplete="off" type="text" name="start-date" placeholder="start-date"  class="form-control-edit-mode datetimepicker " placeholder="DD-MM-YYYY  00:00" {% if systemeditmode.start %}value="{{systemeditmode.start|date:"d-M-Y H:i"}}"{% endif %} >
                                                            </div>
                                                        </td>
                                                        <td class="form-edit-mode d-none">    
                                                            <div class="wrapper-calendar form-edit-mode">
                                                                <input autocomplete="off" type="text" name="end-date" placeholder="end-date"  class="form-control-edit-mode datetimepicker " placeholder="DD-MM-YYYY  00:00" {% if systemeditmode.end %}value="{{systemeditmode.end|date:"d-M-Y H:i"}}"{% endif %}>
                                                            </div>
                                                        </td>
                                                        <td class="form-edit-mode d-none">
                                                            <div class="wrapper-select form-edit-mode">
                                                                <select name="edit-mode-after" title="edit-mode" class="form-control-edit-mode">
                                                                    <option value="on" {% if systemeditmode.edit_after == "on" %}selected{% endif %}>On</option>
                                                                    <option value="interim" {% if systemeditmode.edit_after == "interim" %}selected{% endif %}>Interim</option>
                                                                    <option value="off" {% if systemeditmode.edit_after == "off" %}selected{% endif %}>Off</option>
                                                                </select>
                                                            </div>
                                                        </td>
                                                    {% endif %}
                                                </tr>
                                            {% endfor %}

                                            
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    
                    </div>
                    
                </form>
                
            </div>
            {% if editMode %} 
                <div class="d-flex mt-4 gap-2 align-items-center justify-content-start wrapper-save-change ">
                    <button form="form-set-current" type="submit" class="btn btn-primary btn-save-change" >Save Changes</button>
                    <button type="button" class="btn-text text-danger" hx-get="{% url 'system_management:setCurrent' %}" hx-target=".container-content" hx-swap="innerHTML">Cancel</button>
                </div>
            {% endif %}
            
        </div>  
    </div>
</div>


{% if editMode %}
<script>
    $('.current-year').prepend(`
       
        <option value="${parseInt(localStorage.getItem('currentYear')) - 1}">${parseInt(localStorage.getItem('currentYear')) - 1}</option>
        <option selected value="${parseInt(localStorage.getItem('currentYear'))}">${parseInt(localStorage.getItem('currentYear'))}</option>
        <option value="${parseInt(localStorage.getItem('currentYear')) + 1}">${parseInt(localStorage.getItem('currentYear')) + 1}</option>
        
    `)
    $(".datetimepicker").each(function(){
        $(this).datetimepicker({          
            format:'d-M-Y H:i',
            step:15
        });
    });
</script>
{% endif %}

<script>
    $(document).ready(function(){
        $('.btn-save-change').click(function(e){
            e.stopPropagation()
        })
        $('body').click(function(){
            $('.form-edit-mode').addClass('d-none');
            $('.edit-mode').removeClass('d-none');
        })
        $('.table-set-edit-mode tbody tr').click(function(e){
            e.stopPropagation()
            $(this).find('.edit-mode').addClass('d-none');
            $(this).find('.form-edit-mode').removeClass('d-none');
        })       
        let d = new Date()
        let timezone = d.getTimezoneOffset();
        $('input[name="timezone"]').val(d.toString().match(/([A-Z]+[\+-][0-9]+)/)[1])
        
    });

    $(document).ready(function() {
        // Store original values to restore on cancel
        const originalValues = {};
        
        // Initialize tracking of original values
        $('.table-set-edit-mode tbody tr').each(function(index) {
            const rowId = $(this).find('input[name="systemeditmode"]').val();
            originalValues[rowId] = {
                mode: $(this).find('td.edit-mode').eq(0).text().trim(),
                startDate: $(this).find('td.edit-mode').eq(1).text().trim(),
                endDate: $(this).find('td.edit-mode').eq(2).text().trim(),
                editAfter: $(this).find('td.edit-mode').eq(3).text().trim()
            };
        });
        
        // Click handler for table rows (you already have this)
        $('.table-set-edit-mode tbody tr').click(function(e) {
            e.stopPropagation();
            $(this).find('.edit-mode').addClass('d-none');
            $(this).find('.form-edit-mode').removeClass('d-none');
        });
        
        // Real-time update from edit fields to display cells
        $(document).on('change', 'select[name="edit-mode"]', function() {
            const row = $(this).closest('tr');
            const selectedText = $(this).find('option:selected').text();
            row.find('td.edit-mode').eq(0).text(selectedText);
        });
        
        // Update start date on change
        $(document).on('change', 'input[name="start-date"]', function() {
            const row = $(this).closest('tr');
            const dateValue = $(this).val() || 'DD-MM-YYYY  00:00';
            row.find('td.edit-mode').eq(1).text(dateValue);
        });
        
        // Update end date on change
        $(document).on('change', 'input[name="end-date"]', function() {
            const row = $(this).closest('tr');
            const dateValue = $(this).val() || 'DD-MM-YYYY  00:00';
            row.find('td.edit-mode').eq(2).text(dateValue);
        });
        
        // Update edit-after on change
        $(document).on('change', 'select[name="edit-mode-after"]', function() {
            const row = $(this).closest('tr');
            const selectedText = $(this).find('option:selected').text();
            row.find('td.edit-mode').eq(3).text(selectedText);
        });
        
        // Handle datetime picker changes since it may not trigger standard change events
        $(document).on('dp.change', '.datetimepicker', function(e) {
            const inputName = $(this).attr('name');
            const row = $(this).closest('tr');
            const dateValue = $(this).val() || 'DD-MM-YYYY  00:00';
            
            if (inputName === 'start-date') {
                row.find('td.edit-mode').eq(1).text(dateValue);
            } else if (inputName === 'end-date') {
                row.find('td.edit-mode').eq(2).text(dateValue);
            }
        });
        
    });



   
    
</script>    

